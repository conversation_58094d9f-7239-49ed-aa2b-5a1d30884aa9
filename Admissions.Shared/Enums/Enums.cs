using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admissions.Shared
{
    public enum BieuMauDkxtEnums
    {
        XTHB, DGNL, THNK
    }

    public enum NganHangThanhToanOnlineEnum
    {
        [Display(Name = "Vietcombank")]
        VCB = 1,
        [Display(Name = "Techcombank")]
        TCB = 2,
        [Display(Name = "BIDV")]
        BIDV = 3,
        [Display(Name = "Vietinbank")]
        VTB = 4,
        [Display(Name = "Agribank")]
        AGB = 5,
        [Display(Name = "ACB")]
        ACB = 6,
        [Display(Name = "Sacombank")]
        STB = 7,
        [Display(Name = "VPBank")]
        VPB = 8,
        [Display(Name = "MBBank")]
        MBB = 9,
        [Display(Name = "TPBank")]
        TPB = 10,
        [Display(Name = "HDBank")]
        HDB = 11,
        [Display(Name = "VIB")]
        VIB = 12,
        [Display(Name = "SHB")]
        SHB = 13,
        [Display(Name = "Eximbank")]
        EIB = 14,
        [Display(Name = "OCB")]
        OCB = 15,
        [Display(Name = "MSB")]
        MSB = 16,
        [Display(Name = "SeABank")]
        SSB = 17,
        [Display(Name = "LienVietPostBank")]
        LPB = 18,
        [Display(Name = "VietCapitalBank")]
        BVB = 19,
        [Display(Name = "BacABank")]
        NAB = 20
    }
}
