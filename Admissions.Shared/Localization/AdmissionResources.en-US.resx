<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="data.not-found" xml:space="preserve">
    <value>Data is not found</value>
  </data>
  <data name="home" xml:space="preserve">
    <value>Home page</value>
  </data>
  <data name="message.error-500" xml:space="preserve">
    <value>An error was occur, read this message for more details:</value>
  </data>
  <data name="redis.test.cache-time" xml:space="preserve">
    <value>Cached Time</value>
  </data>
  <data name="redis.test.current-time" xml:space="preserve">
    <value>Current Time</value>
  </data>
  <data name="redis.test.label" xml:space="preserve">
    <value>Redis check service</value>
  </data>
  <data name="system-application.code.existed" xml:space="preserve">
    <value>System Application code is existed!</value>
  </data>
  <data name="CanBo.MaCanBo.Required" xml:space="preserve">
    <value>Mã cán bộ không được để trống</value>
  </data>
  <data name="CanBo.MaCanBo.Maxlength" xml:space="preserve">
    <value>Mã cán bộ có độ dài tối đa 10 ký tự</value>
  </data>
  <data name="CanBo.MaCanBo.Exist" xml:space="preserve">
    <value>Mã cán bộ {0} đã tồn tại trong hệ thống</value>
  </data>
  <data name="CanBo.HoTen.Required" xml:space="preserve">
    <value>Họ tên không được để trống</value>
  </data>
  <data name="CanBo.HoTen.Maxlength" xml:space="preserve">
    <value>Họ tên có độ dài tối đa 100 ký tự</value>
  </data>
  <data name="CanBo.NgaySinh.Required" xml:space="preserve">
    <value>Ngày sinh không được để trống</value>
  </data>
  <data name="CanBo.GioiTinh.Required" xml:space="preserve">
    <value>Giới tính không được để trống</value>
  </data>
  <data name="CanBo.DanToc.Required" xml:space="preserve">
    <value>Dân tộc không được để trống</value>
  </data>
  <data name="CanBo.QuocTich.Required" xml:space="preserve">
    <value>Quốc tịch không được để trống</value>
  </data>
  <data name="CanBo.TinhTrangHonNhan.Required" xml:space="preserve">
    <value>Tình trạng hôn nhân không được để trống</value>
  </data>
  <data name="CanBo.DiaChiNoiSinh.Required" xml:space="preserve">
    <value>Địa chỉ nơi sinh không được để trống</value>
  </data>
  <data name="CanBo.DiaChiNoiSinh.Maxlength" xml:space="preserve">
    <value>Địa chỉ nơi sinh nhập tối đa 500 ký tự</value>
  </data>
  <data name="CanBo.TinhNoiSinh.Required" xml:space="preserve">
    <value>Tỉnh nơi sinh không được để trống</value>
  </data>
  <data name="CanBo.HuyenNoiSinh.Required" xml:space="preserve">
    <value>Huyện nơi sinh không được để trống</value>
  </data>
  <data name="CanBo.DiaChiQueQuan.Maxlength" xml:space="preserve">
    <value>Địa chỉ quê quán nhập tối đa 500 ký tự</value>
  </data>
  <data name="CanBo.SoCmnd.Maxlength" xml:space="preserve">
    <value>Số CMND/CCCD nhập tối đa 20 ký tự</value>
  </data>
  <data name="CanBo.NoiCapCmnd.Maxlength" xml:space="preserve">
    <value>Nơi cấp CMND/CCCD nhập tối đa 500 ký tự</value>
  </data>
  <data name="CanBo.SoHoChieu.Maxlength" xml:space="preserve">
    <value>Số hộ chiếu nhập tối đa 20 ký tự</value>
  </data>
  <data name="CanBo.NoiCapHoChieu.Maxlength" xml:space="preserve">
    <value>Nơi cấp hộ chiếu nhập tối đa 500 ký tự</value>
  </data>
  <data name="CanBo.SoSoBhxh.Maxlength" xml:space="preserve">
    <value>Số sổ BHXH nhập tối đa 50 ký tự</value>
  </data>
  <data name="CanBo.NoiCapSoBhxh.Maxlength" xml:space="preserve">
    <value>Nơi cấp BHXH nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.SoTaiKhoanNganHang.Maxlength" xml:space="preserve">
    <value>Số tài khoản ngân hàng nhập tối đa 50 ký tự</value>
  </data>
  <data name="CanBo.TenNganHang.Maxlength" xml:space="preserve">
    <value>Tên ngân hàng nhập tối đa 50 ký tự</value>
  </data>
  <data name="CanBo.ChiNhanhNganHang.Maxlength" xml:space="preserve">
    <value>Chi nhánh nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.TrinhDoGiaoDucPhoThong.Maxlength" xml:space="preserve">
    <value>Trình độ văn hóa nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.TrinhDoNgoaiNgu.Maxlength" xml:space="preserve">
    <value>Trình độ ngoại ngữ nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.TrinhDoLyLuanChinhTri.Maxlength" xml:space="preserve">
    <value>Trình độ lý luận chính trị nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.TrinhDoQuanLyNhaNuoc.Maxlength" xml:space="preserve">
    <value>Trình độ quản lý nhà nước nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.TrinhDoTinHoc.Maxlength" xml:space="preserve">
    <value>Trình độ tin học nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.DienThoaiDiDong.Maxlength" xml:space="preserve">
    <value>Điện thoại di động nhập tối đa 20 ký tự</value>
  </data>
  <data name="CanBo.DienThoaiNhaRieng.Maxlength" xml:space="preserve">
    <value>Số điện thoại nhà riêng nhập tối đa 20 ký tự</value>
  </data>
  <data name="CanBo.Email.Maxlength" xml:space="preserve">
    <value>Email cá nhân nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.Email2.Maxlength" xml:space="preserve">
    <value>Email cơ quan nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.DiaChiNoiO.Maxlength" xml:space="preserve">
    <value>Địa chỉ nơi ở nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.HoTenNguoiLienHe.Maxlength" xml:space="preserve">
    <value>Họ và tên nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.QuanHeVoiNguoiLaoDong.Maxlength" xml:space="preserve">
    <value>Quan hệ nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.DienThoaiDiDongNguoiLienHe.Maxlength" xml:space="preserve">
    <value>ĐT di động nhập tối đa 20 ký tự</value>
  </data>
  <data name="CanBo.DienThoaiNhaRiengNguoiLienHe.Maxlength" xml:space="preserve">
    <value>ĐT nhà riêng nhập tối đa 20 ký tự</value>
  </data>
  <data name="CanBo.EmailNguoiLienHe.Maxlength" xml:space="preserve">
    <value>Email nhập tối đa 50 ký tự</value>
  </data>
  <data name="CanBo.DiaChiNguoiLienHe.Maxlength" xml:space="preserve">
    <value>Địa chỉ nhập tối đa 500 ký tự</value>
  </data>
  <data name="CanBo.SoTheDangVien.Maxlength" xml:space="preserve">
    <value>Số thẻ đảng viên nhập tối đa 50 ký tự</value>
  </data>
  <data name="CanBo.NoiKetNapDang.Maxlength" xml:space="preserve">
    <value>Nơi kết nạp đảng nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.NoiVaoDoan.Maxlength" xml:space="preserve">
    <value>Nơi kết nạp đoàn nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.CapBac.Maxlength" xml:space="preserve">
    <value>Cấp bậc nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.ChucVuQuanDoi.Maxlength" xml:space="preserve">
    <value>Chức vụ nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.LyDoXuatNgu.Maxlength" xml:space="preserve">
    <value>Lý do xuất ngũ nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.MucDoThuongTat.Maxlength" xml:space="preserve">
    <value>Mức độ thương tật nhập tối đa 50 ký tự</value>
  </data>
  <data name="CanBo.PhanTramThuongTat.Maxlength" xml:space="preserve">
    <value>Phần trăm tổn thương nhập tối đa 50 ký tự</value>
  </data>
  <data name="CanBo.HuanHuyChuong.Maxlength" xml:space="preserve">
    <value>Huân, huy chương nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.NhomMau.Maxlength" xml:space="preserve">
    <value>Nhóm máu nhập tối đa 50 ký tự</value>
  </data>
  <data name="CanBo.ChieuCao.Maxlength" xml:space="preserve">
    <value>Chiều cao nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.CanNang.Maxlength" xml:space="preserve">
    <value>Cân nặng nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.TinhTrangSucKhoe.Maxlength" xml:space="preserve">
    <value>Tình trạng sức khỏe nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.DonViTuyenDung.Maxlength" xml:space="preserve">
    <value>Đơn vị tuyển dụng nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.NgheNghiepTuyenDung.Maxlength" xml:space="preserve">
    <value>Nghề nghiệp tuyển dụng nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.CongViecDuocGiao.Maxlength" xml:space="preserve">
    <value>Công việc được giao nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.LinhVucTheoDoi.Maxlength" xml:space="preserve">
    <value>Lĩnh vực theo dõi nhập tối đa 200 ký tự</value>
  </data>
  <data name="CanBo.NotExist" xml:space="preserve">
    <value>Thông tin cán bộ không tồn tại</value>
  </data>
  <data name="CanBo.HrQuanHeGiaDinhs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin quan hệ gia đình liên kết</value>
  </data>
  <data name="CanBo.HrHopDongs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin hợp đồng liên kết</value>
  </data>
  <data name="CanBo.HrLuongs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin lương liên kết</value>
  </data>
  <data name="CanBo.HrLuongQuaTrinhs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin quá trình lương liên kết</value>
  </data>
  <data name="CanBo.HrPhuCaps.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin phụ cấp liên kết</value>
  </data>
  <data name="CanBo.HrPhuCapQuaTrinhs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin quá trình phụ cấp liên kết</value>
  </data>
  <data name="CanBo.HrBaoHiemQuanTrinhs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin quá trình bảo hiểm liên kết</value>
  </data>
  <data name="CanBo.HrCongTacTrongNuocs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin công tác trong nước liên kết</value>
  </data>
  <data name="CanBo.HrCongTacNuocNgoais.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin công tác nước ngoài liên kết</value>
  </data>
  <data name="CanBo.HrCongTacSauDaiHocs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin công tác sau đại học liên kết</value>
  </data>
  <data name="CanBo.HrBoNhiemChucVus.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin bổ nhiệm chức vụ liên kết</value>
  </data>
  <data name="CanBo.HrMienNhiemChucVus.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin miễn nhiệm chức vụ liên kết</value>
  </data>
  <data name="CanBo.HrChucVuDangDoans.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin chức vụ đảng/đoàn liên kết</value>
  </data>
  <data name="CanBo.HrDaiBieuQhhdnds.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin đại biểu QH/HDND liên kết</value>
  </data>
  <data name="CanBo.HrDaoTaoChuyenMons.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin đào tạo chuyên môn liên kết</value>
  </data>
  <data name="CanBo.HrDaoTaoBoiDuongs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin đào tạo bồi dưỡng liên kết</value>
  </data>
  <data name="CanBo.HrKhenThuongs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin khen thưởng liên kết</value>
  </data>
  <data name="CanBo.HrKyLuats.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin kỷ luật liên kết</value>
  </data>
  <data name="CanBo.HrQuyetDinhs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin quyết định liên kết</value>
  </data>
  <data name="authentication.code.required" xml:space="preserve">
    <value>Mã xác thực không được để trống</value>
  </data>
  <data name="chung-chi.id-loai-boi-duong.required" xml:space="preserve">
    <value>Loại bồi dưỡng không được để trống</value>
  </data>
  <data name="chung-chi.ten-don-vi-boi-duong.required" xml:space="preserve">
    <value>Tên đơn vị bồi dưỡng không được để trống</value>
  </data>
  <data name="chung-chi.ten-loai-van-bang.max-length" xml:space="preserve">
    <value>Tên loại văn bằng không được vượt quá 128 ký tự</value>
  </data>
  <data name="chung-chi.ten-loai-van-bang.required" xml:space="preserve">
    <value>Tên loại văn bằng không được để trống</value>
  </data>
  <data name="chung-chi.tu-thang-nam-boi-duong.required" xml:space="preserve">
    <value>Từ tháng năm bồi dưỡng không được để trống</value>
  </data>
  <data name="khen-thuong.co-quan-khen-thuong.max-length" xml:space="preserve">
    <value>Cơ quan khen thưởng không được vượt quá 500 ký tự</value>
  </data>
  <data name="khen-thuong.co-quan-khen-thuong.required" xml:space="preserve">
    <value>Cơ quan khen thưởng không được để trống</value>
  </data>
  <data name="khen-thuong.ghi-chu-khen-thuong.max-length" xml:space="preserve">
    <value>Ghi chú khen thưởng không được vượt quá 500 ký tự</value>
  </data>
  <data name="khen-thuong.id-can-bo.required" xml:space="preserve">
    <value>Id cán bộ không được để trống</value>
  </data>
  <data name="khen-thuong.ly-do-khen-thuong.max-length" xml:space="preserve">
    <value>Lý do khen thưởng không được vượt quá 500 ký tự</value>
  </data>
  <data name="khen-thuong.ngay-qd-khen-thuong.required" xml:space="preserve">
    <value>Ngày QĐ kỷ luật không được để trống</value>
  </data>
  <data name="khen-thuong.nguoi-ky-qd-khen-thuong.max-length" xml:space="preserve">
    <value>Người ký QĐ khen thưởng không được vượt quá 50 ký tự</value>
  </data>
  <data name="khen-thuong.so-qd-khen-thuong.max-length" xml:space="preserve">
    <value>Số QĐ không được vượt quá 50 ký tự</value>
  </data>
  <data name="khen-thuong.so-qd-khen-thuong.required" xml:space="preserve">
    <value>Số QĐ không được để trống</value>
  </data>
  <data name="ky-luat.co-quan-qd-ky-luat.max-length" xml:space="preserve">
    <value>Cơ quan QĐ kỷ luật không được vượt quá 200 ký tự</value>
  </data>
  <data name="ky-luat.ghi-chu-ky-luat.max-length" xml:space="preserve">
    <value>Ghi chú kỷ luật không được vượt quá 500 ký tự</value>
  </data>
  <data name="ky-luat.id-can-bo.required" xml:space="preserve">
    <value>Id cán bộ không được để trống</value>
  </data>
  <data name="ky-luat.ly-do-ky-luat.max-length" xml:space="preserve">
    <value>Lý do kỷ luật không được vượt quá 500 ký tự</value>
  </data>
  <data name="ky-luat.ngay-qd-ky-luat.required" xml:space="preserve">
    <value>Ngày QĐ kỷ luật không được để trống</value>
  </data>
  <data name="ky-luat.nguoi-ky-qd-ky-luat.max-length" xml:space="preserve">
    <value>Người ký QĐ kỷ luật không được vượt quá 50 ký tự</value>
  </data>
  <data name="ky-luat.so-qd-ky-luat.max-length" xml:space="preserve">
    <value>Số QĐ không được vượt quá 50 ký tự</value>
  </data>
  <data name="ky-luat.so-qd-ky-luat.required" xml:space="preserve">
    <value>Số QĐ không được để trống</value>
  </data>
  <data name="ky-luat.so-thang-khong-nang-luong.only-number" xml:space="preserve">
    <value>Sô tháng không nâng lương chỉ được nhập số</value>
  </data>
  <data name="system-application.code.required" xml:space="preserve">
    <value>Mã ứng dụng không được để trống</value>
  </data>
  <data name="system-application.name.max-length" xml:space="preserve">
    <value>Tên ứng dụng không được vượt quá 128 ký tự</value>
  </data>
  <data name="system-application.name.required" xml:space="preserve">
    <value>Tên ứng dụng không được để trống</value>
  </data>
  <data name="van-bang.chuyen-nganh-dao-tao.max-length" xml:space="preserve">
    <value>Chuyên ngành đào tạo không được vượt quá 50 ký tự</value>
  </data>
  <data name="van-bang.chuyen-nganh-dao-tao.required" xml:space="preserve">
    <value>Chuyên ngành đào tạo không được để trống</value>
  </data>
  <data name="van-bang.den-thang-nam-dao-tao.required" xml:space="preserve">
    <value>Đến năm đào tạo không được để trống</value>
  </data>
  <data name="van-bang.id-quoc-gia-dao-tao.required" xml:space="preserve">
    <value>Quốc gia đào tạo không được để trống</value>
  </data>
  <data name="van-bang.id-trinh-do-dao-tao.required" xml:space="preserve">
    <value>Trình độ đào tạo không được để trống</value>
  </data>
  <data name="van-bang.ten-truong-dao-tao.max-length" xml:space="preserve">
    <value>Tên trường đào tạo không được vượt quá 128 ký tự</value>
  </data>
  <data name="van-bang.ten-truong-dao-tao.required" xml:space="preserve">
    <value>Tên trường đào tạo không được để trống</value>
  </data>
  <data name="van-bang.tu-thang-nam-dao-tao.required" xml:space="preserve">
    <value>Từ năm đào tạo không được để trống</value>
  </data>
  <data name="van-bang.xep-loa-dao-tao.required" xml:space="preserve">
    <value>Xếp loại đào tạo không được để trống</value>
  </data>
  <data name="van-bang.xep-loai-dao-tao.max-length" xml:space="preserve">
    <value>Xếp loại đào tạo không quá 50 kí tự</value>
  </data>
  <data name="loai-cham-cong.ma-loai-cham-cong.required" xml:space="preserve">
    <value>Mã loại chấm công không được để trống</value>
  </data>
  <data name="loai-cham-cong.ma-loai-cham-cong.max-length" xml:space="preserve">
    <value>Mã loại chấm công không được vượt quá 8 ký tự</value>
  </data>
  <data name="loai-cham-cong.ten-loai-cham-cong.required" xml:space="preserve">
    <value>Tên loại chấm công không được để trống</value>
  </data>
  <data name="loai-cham-cong.ten-loai-cham-cong.max-length" xml:space="preserve">
    <value>Tên loại chấm công không được vượt quá 1000 ký tự</value>
  </data>
  <data name="loai-cham-cong.thu-tu-hien-thi.required" xml:space="preserve">
    <value>Thứ tự hiển thị không được để trống</value>
  </data>
  <data name="loai-cham-cong.ma-loai-cham-cong.existed" xml:space="preserve">
    <value>Mã loại chấm công đã tồn tại!</value>
  </data>
  <data name="loai-cham-cong.thu-tu-hien-thi.existed" xml:space="preserve">
    <value>Thứ tự hiển thị đã tồn tại!</value>
  </data>
  <data name="CaLamViec.HrBangCongChiTiets.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin bảng chấm công chi tiết liên kết</value>
  </data>
  <data name="CaLamViec.HrPhanCaLamViecs.Exist" xml:space="preserve">
    <value>Không thể xóa do tồn tại thông tin phân ca làm việc liên kết</value>
  </data>
  <data name="CaLamViec.MaCa.Exist" xml:space="preserve">
    <value>Mã ca {0} đã tồn tại trong hệ thống</value>
  </data>
  <data name="CaLamViec.NotExist" xml:space="preserve">
    <value>Mã ca {0} không tồn tại trong hệ thống</value>
  </data>
  <data name="MaChamCong.Exist" xml:space="preserve">
    <value>Mã chấm công {0} đã tồn tại trong hệ thống</value>
  </data>
  <data name="MaChamCong.CanBo.Exist" xml:space="preserve">
    <value>Nhân viên đã được thiết lập mã chấm công. Id/MaCanBo: {0}</value>
  </data>
  <data name="MaChamCong.CanBo.NotExist" xml:space="preserve">
    <value>Thông tin nhân viên không tồn tại. Id/MaCanBo: {0}</value>
  </data>
  <data name="MaChamCong.Required" xml:space="preserve">
    <value>Mã chấm công không được để trống </value>
  </data>
  <data name="MaChamCong.Invalid" xml:space="preserve">
    <value>Mã chấm công không hợp lệ: {0} </value>
  </data>
  <data name="MaChamCong.MaNhanVien.Required" xml:space="preserve">
    <value>Mã nhân viên không được để trống </value>
  </data>
  <data name="MaChamCong.MaCanBo.Duplidated" xml:space="preserve">
    <value>Mã nhân viên bị trùng: {0}</value>
  </data>
  <data name="MaChamCong.Duplidated" xml:space="preserve">
    <value>Mã chấm công bị trùng: {0}</value>
  </data>
  <data name="DuLieuChamCong.MaNhanVien.Required" xml:space="preserve">
    <value>Mã nhân viên không được để trống </value>
  </data>
  <data name="DuLieuChamCong.CanBo.NotExist" xml:space="preserve">
    <value>Thông tin nhân viên không tồn tại. Id/MaCanBo: {0}</value>
  </data>
  <data name="DuLieuChamCong.NotExist" xml:space="preserve">
    <value>Dữ liệu chấm công không tồn tại</value>
  </data>
  <data name="lich-ca-lam-viec.ca-lam-viec.required" xml:space="preserve">
    <value>Ca làm việc không được để trống</value>
  </data>
  <data name="lich-ca-lam-viec.doi-tuong-ap-dung.required" xml:space="preserve">
    <value>Đối tượng áp dụng không được để trống</value>
  </data>
  <data name="lich-ca-lam-viec.ngay-bat-dau.invalid" xml:space="preserve">
    <value>Ngày bắt đầu phải nhỏ hơn ngày kết thúc</value>
  </data>
  <data name="lich-ca-lam-viec.doi-tuong-ap-dung-phong-ban.invalid" xml:space="preserve">
    <value>Phòng ban không tồn tại cán bộ</value>
  </data>
  <data name="NgayNghiLe.NgayNghi.Existed" xml:space="preserve">
    <value>Ngày nghỉ {0} đã tồn tại</value>
  </data>
  <data name="ky-hieu-cham-cong.ma-ky-hieu-cham-cong.existed" xml:space="preserve">
    <value>Mã ký hiệu chấm công đã tồn tại!</value>
  </data>
  <data name="ky-hieu-cham-cong.ky-hieu-cham-cong.existed" xml:space="preserve">
    <value>Ký hiệu chấm công đã tồn tại!</value>
  </data>
  <data name="loai-giang-vien.ma-loai-giang-vien.existed" xml:space="preserve">
    <value>Mã loại giảng viên đã tồn tại</value>
  </data>
  <data name="loai-chung-chi.ky-hieu-loai-chung-chi.existed" xml:space="preserve">
    <value>Tên/Ký hiệu chứng chỉ đã tồn tại</value>
  </data>
  <data name="loai-khen-thuong.ten-loai-khen-thuong.existed" xml:space="preserve">
    <value>Tên loại khen thưởng đã tồn tại</value>
  </data>

  <!--Phòng ban-->
  <data name="phong-ban.don-vi.required" xml:space="preserve">
    <value>Đơn vị không được để trống</value>
  </data>
  <data name="phong-ban.ma-phong-ban.required" xml:space="preserve">
    <value>Mã phòng ban không được để trống</value>
  </data>
  <data name="phong-ban.ten-phong-ban.required" xml:space="preserve">
    <value>Tên phòng ban không được để trống</value>
  </data>
  <data name="phong-ban.ma-phong-ban.existed" xml:space="preserve">
    <value>Mã phòng ban đã tồn tại!</value>
  </data>
  <!--End phòng ban-->

  <!--Ngạch lương-->
  <data name="ngach-luong.ma-ngach-luong.existed" xml:space="preserve">
    <value>Mã ngạch lương đã tồn tại!</value>
  </data>
  <!--End ngạch lương-->

  <!--Bậc lương-->
  <data name="bac-luong.ngach-luong.required" xml:space="preserve">
    <value>Ngạch lương không được để trống</value>
  </data>
  <data name="bac-luong.he-so-luong.required" xml:space="preserve">
    <value>Hệ số lương không được để trống</value>
  </data>
  <data name="bac-luong.phan-tram-vuot-khung.required" xml:space="preserve">
    <value>Phần trăm vượt khung không được để trống</value>
  </data>
  <!--End bậc lương-->

  <!--Loại ngạch lương-->
  <data name="loai-ngach-luong.loai-ngach-luong.existed" xml:space="preserve">
    <value>Loại ngạch lương đã tồn tại!</value>
  </data>
  <!--End loại ngạch lương-->

  <!--Đăng ký nghỉ-->
  <data name="dang-ky-nghi.gio-bat-dau.required" xml:space="preserve">
    <value>Giờ bắt đầu không được để trống</value>
  </data>

  <data name="dang-ky-nghi.gio-ket-thuc.required" xml:space="preserve">
    <value>Giờ kết thúc không được để trống</value>
  </data>
  <!--End đăng ký nghỉ-->
  
  <!--Làm thêm giờ-->
  <data name="lam-them-gio.NotExist" xml:space="preserve">
    <value>Thông tin đăng ký làm thêm giờ không tồn tại</value>
  </data>
  <data name="lam-them-gio.CanNotUpdateStatus" xml:space="preserve">
    <value>Không thể cập nhật trạng thái đăng ký làm thêm giờ</value>
  </data>
  <data name="lam-them-gio.CanNotDelete" xml:space="preserve">
    <value>Không thể xóa đăng ký làm thêm giờ</value>
  </data>
  <data name="lam-them-gio.CanNotUpdate" xml:space="preserve">
    <value>Không thể cập nhật đăng ký làm thêm giờ</value>
  </data>
  <data name="lam-them-gio.nhan-vien.NotExist" xml:space="preserve">
    <value>Thông tin nhân viên không tồn tại. IdCanBo: {0}</value>
  </data>
  <data name="lam-them-gio.phong-ban.NotExist" xml:space="preserve">
    <value>Thông tin phòng ban không tồn tại. MaCanBo: {0}</value>
  </data>
  <data name="lam-them-gio.nhan-vien.NoData" xml:space="preserve">
    <value>Không tìm thấy thông tin nhân viên</value>
  </data>
  <!--End làm thêm giờ-->
  <!--Kết quả phỏng vấn-->
  <data name="ket-qua-phong-van.ung-vien.NotExist" xml:space="preserve">
    <value>Thông tin ứng viên không tồn tại</value>
  </data>
  <data name="ket-qua-phong-van.ung-vien.AlreadyExist" xml:space="preserve">
    <value>Kết quả phỏng vấn của ứng viên {0} đã tồn tại</value>
  </data>
  <!--End Kết quả phỏng vấn-->
</root>