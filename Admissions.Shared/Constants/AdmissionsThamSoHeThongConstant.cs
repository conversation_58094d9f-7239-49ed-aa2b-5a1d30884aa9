using Core.Shared.EmailTemplate;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admissions.Shared
{
    public enum AdmissionsThamSoHeThongEnum
    {
        Quy_tac_sinh_ma_ho_so_tuyen_sinh,
        Nguong_diem_xet_tuyen,
        So_tai_khoan_ngan_hang,
        Ma_ngan_hang,
        Hinh_thuc_thanh_toan,
        Cong_thanh_toan_tuyen_sinh,
        Don_vi_su_dung
    }

    public class AdmissionsThamSoHeThongSeedModel
    {
        public string IdThamSo { get; set; }
        public bool Active { get; set; }
        public DateTime? DateModify { get; set; }
        public string GiaTri { get; set; }
        public string NhomThamSo { get; set;}
        public string TenThamSo { get; set; }
        public string Ghi<PERSON>hu { get; set; }
    }

    public class AdmissionsThamSoHeThongSeedConstant
    {
        public static List<AdmissionsThamSoHeThongSeedModel> ListSeedThamSos = new List<AdmissionsThamSoHeThongSeedModel>
        {
            new AdmissionsThamSoHeThongSeedModel
            {
                Active = false,
                DateModify = DateTime.Now,
                GiaTri = "5.5",
                IdThamSo = AdmissionsThamSoHeThongEnum.Nguong_diem_xet_tuyen.ToString(),
                NhomThamSo = "Quản lý tuyển sinh",
                TenThamSo = "Ngưỡng điểm xét tuyển"
            },
            new AdmissionsThamSoHeThongSeedModel
            {
                Active = true,
                DateModify = DateTime.Now,
                GiaTri = "0",
                IdThamSo = AdmissionsThamSoHeThongEnum.Quy_tac_sinh_ma_ho_so_tuyen_sinh.ToString(),
                NhomThamSo = "Quản lý tuyển sinh",
                TenThamSo = "Quy tắc sinh mã hồ sơ tuyển sinh",
                GhiChu = "0: Mã hồ sơ = CMND, 1: Mã hồ sơ theo thứ tự tăng dần, TS{{NamTuyenSinh}}{{STT}}: Sinh mã theo tiền tố"
            },
            new AdmissionsThamSoHeThongSeedModel
            {
                Active = true,
                DateModify = DateTime.Now,
                GiaTri = "",
                IdThamSo = AdmissionsThamSoHeThongEnum.So_tai_khoan_ngan_hang.ToString(),
                NhomThamSo = "Quản lý tuyển sinh",
                TenThamSo = "Số tài khoản ngân hàng thu lệ phí tuyển sinh",
                GhiChu = ""
            },
            new AdmissionsThamSoHeThongSeedModel
            {
                Active = true,
                DateModify = DateTime.Now,
                GiaTri = "",
                IdThamSo = AdmissionsThamSoHeThongEnum.Ma_ngan_hang.ToString(),
                NhomThamSo = "Quản lý tuyển sinh",
                TenThamSo = "Mã ngân hàng thu lệ phí tuyển sinh",
                GhiChu = "Mã ngân hàng theo chuẩn VietQR"
            },
            new AdmissionsThamSoHeThongSeedModel
            {
                Active = true,
                DateModify = DateTime.Now,
                GiaTri = "0",
                IdThamSo = AdmissionsThamSoHeThongEnum.Hinh_thuc_thanh_toan.ToString(),
                NhomThamSo = "Quản lý tuyển sinh",
                TenThamSo = "Hình thức thu lệ phí tuyển sinh",
                GhiChu = "0: Thu lệ phí tuyển sinh offline, 1: Thu lệ phí tuyển sinh online"
            },
            new AdmissionsThamSoHeThongSeedModel
            {
                Active = true,
                DateModify = DateTime.Now,
                GiaTri = "",
                IdThamSo = AdmissionsThamSoHeThongEnum.Cong_thanh_toan_tuyen_sinh.ToString(),
                NhomThamSo = "Quản lý tuyển sinh",
                TenThamSo = "Cổng thanh toán tuyển sinh",
                GhiChu = ""
            },
            new AdmissionsThamSoHeThongSeedModel
            {
                Active = true,
                DateModify = DateTime.Now,
                GiaTri = "",
                IdThamSo = AdmissionsThamSoHeThongEnum.Don_vi_su_dung.ToString(),
                NhomThamSo = "Quản lý tuyển sinh",
                TenThamSo = "Mã Đơn vị sử dụng",
                GhiChu = "Được cấp cho mỗi đơn vị sử dụng, cần nhập chính xác (Liên hệ với nhà cung cấp để lấy mã chính thức)"
            }
        };
    }
}
