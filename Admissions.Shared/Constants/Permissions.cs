using Core.Shared;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Admissions.Shared
{
    public enum PermissionAdmissionEnum
    {
        [Display(GroupName = "Phương thức tuyển sinh", Name = "Xem thông tin phương thức tuyển sinh")]
        PHUONG_THUC_TUYEN_SINH_VIEW,

        [Display(GroupName = "Phương thức tuyển sinh", Name = "Thêm mới phương thức tuyển sinh")]
        PHUONG_THUC_TUYEN_SINH_ADD,

        [Display(GroupName = "Phương thức tuyển sinh", Name = "Cập nhật phương thức tuyển sinh")]
        PHUONG_THUC_TUYEN_SINH_EDIT,

        [Display(GroupName = "Phương thức tuyển sinh", Name = "<PERSON><PERSON>a phương thức tuyển sinh")]
        PHUONG_THUC_TUYEN_SINH_DELETE,

        [Display(GroupName = "Ngành", Name = "Xem thông tin ngành")]
        NGANH_VIEW,

        [Display(GroupName = "Ngành", Name = "Thêm mới ngành")]
        NGANH_ADD,

        [Display(GroupName = "Ngành", Name = "Cập nhật ngành")]
        NGANH_EDIT,

        [Display(GroupName = "Ngành", Name = "Xóa ngành")]
        NGANH_DELETE,

        [Display(GroupName = "Loại môn", Name = "Xem thông tin loại môn")]
        LOAI_MON_VIEW,

        [Display(GroupName = "Loại môn", Name = "Thêm mới loại môn")]
        LOAI_MON_ADD,

        [Display(GroupName = "Loại môn", Name = "Cập nhật loại môn")]
        LOAI_MON_EDIT,

        [Display(GroupName = "Loại môn", Name = "Xóa loại môn")]
        LOAI_MON_DELETE,

        [Display(GroupName = "Đối tượng xét tuyển", Name = "Xem thông tin đối tượng xét tuyển")]
        DOI_TUONG_XET_TUYEN_VIEW,

        [Display(GroupName = "Đối tượng xét tuyển", Name = "Thêm mới đối tượng xét tuyển")]
        DOI_TUONG_XET_TUYEN_ADD,

        [Display(GroupName = "Đối tượng xét tuyển", Name = "Cập nhật đối tượng xét tuyển")]
        DOI_TUONG_XET_TUYEN_EDIT,

        [Display(GroupName = "Đối tượng xét tuyển", Name = "Xóa đối tượng xét tuyển")]
        DOI_TUONG_XET_TUYEN_DELETE,

        [Display(GroupName = "Khu vực ưu tiên", Name = "Xem thông tin khu vực ưu tiên")]
        KHU_VUC_UU_TIEN_VIEW,

        [Display(GroupName = "Khu vực ưu tiên", Name = "Thêm mới khu vực ưu tiên")]
        KHU_VUC_UU_TIEN_ADD,

        [Display(GroupName = "Khu vực ưu tiên", Name = "Cập nhật khu vực ưu tiên")]
        KHU_VUC_UU_TIEN_UPDATE,

        [Display(GroupName = "Khu vực ưu tiên", Name = "Xóa khu vực ưu tiên")]
        KHU_VUC_UU_TIEN_DELETE,

        [Display(GroupName = "Môn xét tuyển", Name = "Xem thông tin môn xét tuyển")]
        MON_XET_TUYEN_VIEW,

        [Display(GroupName = "Môn xét tuyển", Name = "Thêm mới môn xét tuyển")]
        MON_XET_TUYEN_ADD,

        [Display(GroupName = "Môn xét tuyển", Name = "Cập nhật môn xét tuyển")]
        MON_XET_TUYEN_EDIT,

        [Display(GroupName = "Môn xét tuyển", Name = "Xóa môn xét tuyển")]
        MON_XET_TUYEN_DELETE,

        [Display(GroupName = "Giấy tờ", Name = "Xem thông tin giấy tờ")]
        GIAY_TO_VIEW,

        [Display(GroupName = "Giấy tờ", Name = "Thêm mới giấy tờ")]
        GIAY_TO_ADD,

        [Display(GroupName = "Giấy tờ", Name = "Cập nhật giấy tờ")]
        GIAY_TO_EDIT,

        [Display(GroupName = "Giấy tờ", Name = "Xóa giấy tờ")]
        GIAY_TO_DELETE,

        [Display(GroupName = "Tổ hợp xét tuyển", Name = "Xem thông tin tổ hợp xét tuyển")]
        TO_HOP_XET_TUYEN_VIEW,

        [Display(GroupName = "Tổ hợp xét tuyển", Name = "Thêm mới tổ hợp xét tuyển")]
        TO_HOP_XET_TUYEN_ADD,

        [Display(GroupName = "Tổ hợp xét tuyển", Name = "Cập nhật tổ hợp xét tuyển")]
        TO_HOP_XET_TUYEN_EDIT,

        [Display(GroupName = "Tổ hợp xét tuyển", Name = "Xóa tổ hợp xét tuyển")]
        TO_HOP_XET_TUYEN_DELETE,

        [Display(GroupName = "Trường THPT", Name = "Xem thông tin trường THPT")]
        TRUONG_THPT_VIEW,

        [Display(GroupName = "Trường THPT", Name = "Thêm mới trường THPT")]
        TRUONG_THPT_ADD,

        [Display(GroupName = "Trường THPT", Name = "Cập nhật trường THPT")]
        TRUONG_THPT_EDIT,

        [Display(GroupName = "Trường THPT", Name = "Xóa trường THPT")]
        TRUONG_THPT_DELETE,

        [Display(GroupName = "Xã", Name = "Xem thông tin xã")]
        XA_VIEW,

        [Display(GroupName = "Xã", Name = "Thêm mới xã")]
        XA_ADD,

        [Display(GroupName = "Xã", Name = "Cập nhật xã")]
        XA_EDIT,

        [Display(GroupName = "Xã", Name = "Xóa xã")]
        XA_DELETE,

        [Display(GroupName = "Huyện", Name = "Xem thông tin huyện")]
        HUYEN_VIEW,

        [Display(GroupName = "Huyện", Name = "Thêm mới huyện")]
        HUYEN_ADD,

        [Display(GroupName = "Huyện", Name = "Cập nhật huyện")]
        HUYEN_EDIT,

        [Display(GroupName = "Huyện", Name = "Xóa huyện")]
        HUYEN_DELETE,

        [Display(GroupName = "Tỉnh", Name = "Xem thông tin tỉnh")]
        TINH_VIEW,

        [Display(GroupName = "Tỉnh", Name = "Thêm mới tỉnh")]
        TINH_ADD,

        [Display(GroupName = "Tỉnh", Name = "Cập nhật tỉnh")]
        TINH_EDIT,

        [Display(GroupName = "Tỉnh", Name = "Xóa tỉnh")]
        TINH_DELETE,

        [Display(GroupName = "Khối ngành", Name = "Xem thông tin khối ngành")]
        KHOI_NGANH_VIEW,

        [Display(GroupName = "Khối ngành", Name = "Thêm mới khối ngành")]
        KHOI_NGANH_ADD,

        [Display(GroupName = "Khối ngành", Name = "Cập nhật khối ngành")]
        KHOI_NGANH_EDIT,

        [Display(GroupName = "Khối ngành", Name = "Xóa khối ngành")]
        KHOI_NGANH_DELETE,

        [Display(GroupName = "Loại ngành", Name = "Xem thông tin loại ngành")]
        LOAI_NGANH_VIEW,

        [Display(GroupName = "Loại ngành", Name = "Thêm mới loại ngành")]
        LOAI_NGANH_ADD,

        [Display(GroupName = "Loại ngành", Name = "Cập nhật loại ngành")]
        LOAI_NGANH_EDIT,

        [Display(GroupName = "Loại ngành", Name = "Xóa loại ngành")]
        LOAI_NGANH_DELETE,

        [Display(GroupName = "Ngành chuẩn", Name = "Xem thông tin ngành chuẩn")]
        NGANH_CHUAN_VIEW,

        [Display(GroupName = "Ngành chuẩn", Name = "Thêm mới ngành chuẩn")]
        NGANH_CHUAN_ADD,

        [Display(GroupName = "Ngành chuẩn", Name = "Cập nhật ngành chuẩn")]
        NGANH_CHUAN_EDIT,

        [Display(GroupName = "Ngành chuẩn", Name = "Xóa ngành chuẩn")]
        NGANH_CHUAN_DELETE,

        [Display(GroupName = "Đợt tuyển sinh", Name = "Xem thông tin đợt tuyển sinh")]
        DOT_TUYEN_SINH_VIEW,

        [Display(GroupName = "Đợt tuyển sinh", Name = "Thêm mới đợt tuyển sinh")]
        DOT_TUYEN_SINH_ADD,

        [Display(GroupName = "Đợt tuyển sinh", Name = "Cập nhật đợt tuyển sinh")]
        DOT_TUYEN_SINH_EDIT,

        [Display(GroupName = "Đợt tuyển sinh", Name = "Xóa đợt tuyển sinh")]
        DOT_TUYEN_SINH_DELETE,

        [Display(GroupName = "Nhóm ngành", Name = "Xem thông tin nhóm ngành")]
        NHOM_NGANH_VIEW,

        [Display(GroupName = "Nhóm ngành", Name = "Thêm mới nhóm ngành")]
        NHOM_NGANH_ADD,

        [Display(GroupName = "Nhóm ngành", Name = "Cập nhật nhóm ngành")]
        NHOM_NGANH_EDIT,

        [Display(GroupName = "Nhóm ngành", Name = "Xóa nhóm ngành")]
        NHOM_NGANH_DELETE,

        [Display(GroupName = "Tuyển sinh đăng ký đợt", Name = "Xem thông tin tuyển sinh đăng ký đợt")]
        TUYEN_SINH_DANG_KY_DOT_VIEW,

        [Display(GroupName = "Tuyển sinh đăng ký đợt", Name = "Thêm mới tuyển sinh đăng ký đợt")]
        TUYEN_SINH_DANG_KY_DOT_ADD,

        [Display(GroupName = "Tuyển sinh đăng ký đợt", Name = "Cập nhật tuyển sinh đăng ký đợt")]
        TUYEN_SINH_DANG_KY_DOT_EDIT,

        [Display(GroupName = "Tuyển sinh đăng ký đợt", Name = "Xóa tuyển sinh đăng ký đợt")]
        TUYEN_SINH_DANG_KY_DOT_DELETE,

        [Display(GroupName = "Đối tượng ưu tiên", Name = "Xem thông tin đối tượng ưu tiên")]
        DOI_TUONG_UU_TIEN_VIEW,

        [Display(GroupName = "Đối tượng ưu tiên", Name = "Thêm mới đối tượng ưu tiên")]
        DOI_TUONG_UU_TIEN_ADD,

        [Display(GroupName = "Đối tượng ưu tiên", Name = "Cập nhật đối tượng ưu tiên")]
        DOI_TUONG_UU_TIEN_EDIT,

        [Display(GroupName = "Đối tượng ưu tiên", Name = "Xóa đối tượng ưu tiên")]
        DOI_TUONG_UU_TIEN_DELETE,

        [Display(GroupName = "Tuyển sinh phương án điểm học bạ", Name = "Xem thông tin tuyển sinh phương án điểm học bạ")]
        TUYEN_SINH_PHUONG_AN_DIEM_HOC_BA_VIEW,

        [Display(GroupName = "Tuyển sinh phương án điểm học bạ", Name = "Thêm mới tuyển sinh phương án điểm học bạ")]
        TUYEN_SINH_PHUONG_AN_DIEM_HOC_BA_ADD,

        [Display(GroupName = "Tuyển sinh phương án điểm học bạ", Name = "Cập nhật tuyển sinh phương án điểm học bạ")]
        TUYEN_SINH_PHUONG_AN_DIEM_HOC_BA_EDIT,

        [Display(GroupName = "Tuyển sinh phương án điểm học bạ", Name = "Xóa tuyển sinh phương án điểm học bạ")]
        TUYEN_SINH_PHUONG_AN_DIEM_HOC_BA_DELETE,

        [Display(GroupName = "Phương thức xét tuyển", Name = "Xem thông tin phương thức xét tuyển")]
        PHUONG_THUC_XET_TUYEN_VIEW,

        [Display(GroupName = "Phương thức xét tuyển", Name = "Thêm mới phương thức xét tuyển")]
        PHUONG_THUC_XET_TUYEN_ADD,

        [Display(GroupName = "Phương thức xét tuyển", Name = "Cập nhật phương thức xét tuyển")]
        PHUONG_THUC_XET_TUYEN_EDIT,

        [Display(GroupName = "Phương thức xét tuyển", Name = "Xóa phương thức xét tuyển")]
        PHUONG_THUC_XET_TUYEN_DELETE,

        [Display(GroupName = "Tuyển sinh mẫu phiếu", Name = "Xem thông tin mẫu phiếu tuyển sinh")]
        TUYEN_SINH_MAU_PHIEU_VIEW,

        [Display(GroupName = "Tuyển sinh mẫu phiếu", Name = "Thêm mới mẫu phiếu tuyển sinh")]
        TUYEN_SINH_MAU_PHIEU_ADD,

        [Display(GroupName = "Tuyển sinh mẫu phiếu", Name = "Cập nhật mẫu phiếu tuyển sinh")]
        TUYEN_SINH_MAU_PHIEU_EDIT,

        [Display(GroupName = "Tuyển sinh mẫu phiếu", Name = "Xóa mẫu phiếu tuyển sinh")]
        TUYEN_SINH_MAU_PHIEU_DELETE,

        [Display(GroupName = "Xếp loại học tập THPT", Name = "Xem thông tin xếp loại học tập THPT")]
        XEP_LOAI_HOC_TAP_THPT_VIEW,

        [Display(GroupName = "Xếp loại học tập THPT", Name = "Thêm mới xếp loại học tập THPT")]
        XEP_LOAI_HOC_TAP_THPT_ADD,

        [Display(GroupName = "Xếp loại học tập THPT", Name = "Cập nhật xếp loại học tập THPT")]
        XEP_LOAI_HOC_TAP_THPT_EDIT,

        [Display(GroupName = "Xếp loại học tập THPT", Name = "Xóa xếp loại học tập THPT")]
        XEP_LOAI_HOC_TAP_THPT_DELETE,

        [Display(GroupName = "Xếp loại hạnh kiểm THPT", Name = "Xem thông tin xếp loại hạnh kiểm THPT")]
        XEP_LOAI_HANH_KIEM_THPT_VIEW,

        [Display(GroupName = "Xếp loại hạnh kiểm THPT", Name = "Thêm mới xếp loại hạnh kiểm THPT")]
        XEP_LOAI_HANH_KIEM_THPT_ADD,

        [Display(GroupName = "Xếp loại hạnh kiểm THPT", Name = "Cập nhật xếp loại hạnh kiểm THPT")]
        XEP_LOAI_HANH_KIEM_THPT_EDIT,

        [Display(GroupName = "Xếp loại hạnh kiểm THPT", Name = "Xóa xếp loại hạnh kiểm THPT")]
        XEP_LOAI_HANH_KIEM_THPT_DELETE,

        [Display(GroupName = "Tổ hợp môn xét tuyển", Name = "Xem thông tin tổ hợp môn xét tuyển")]
        TO_HOP_MON_XET_TUYEN_VIEW,

        [Display(GroupName = "Tổ hợp môn xét tuyển", Name = "Thêm mới tổ hợp môn xét tuyển")]
        TO_HOP_MON_XET_TUYEN_ADD,

        [Display(GroupName = "Tổ hợp môn xét tuyển", Name = "Cập nhật tổ hợp môn xét tuyển")]
        TO_HOP_MON_XET_TUYEN_EDIT,

        [Display(GroupName = "Tổ hợp môn xét tuyển", Name = "Xóa tổ hợp môn xét tuyển")]
        TO_HOP_MON_XET_TUYEN_DELETE,

        [Display(GroupName = "Tổ hợp ngành xét tuyển", Name = "Xem thông tin tổ hợp ngành xét tuyển")]
        TO_HOP_NGANH_XET_TUYEN_VIEW,

        [Display(GroupName = "Tổ hợp ngành xét tuyển", Name = "Thêm mới tổ hợp ngành xét tuyển")]
        TO_HOP_NGANH_XET_TUYEN_ADD,

        [Display(GroupName = "Tổ hợp ngành xét tuyển", Name = "Cập nhật tổ hợp ngành xét tuyển")]
        TO_HOP_NGANH_XET_TUYEN_EDIT,

        [Display(GroupName = "Tổ hợp ngành xét tuyển", Name = "Xóa tổ hợp ngành xét tuyển")]
        TO_HOP_NGANH_XET_TUYEN_DELETE,

        [Display(GroupName = "Chỉ tiêu tuyển sinh", Name = "Xem thông tin chỉ tiêu tuyển sinh")]
        CHI_TIEU_TUYEN_SINH_VIEW,

        [Display(GroupName = "Chỉ tiêu tuyển sinh", Name = "Thêm mới chỉ tiêu tuyển sinh")]
        CHI_TIEU_TUYEN_SINH_ADD,

        [Display(GroupName = "Chỉ tiêu tuyển sinh", Name = "Cập nhật chỉ tiêu tuyển sinh")]
        CHI_TIEU_TUYEN_SINH_EDIT,

        [Display(GroupName = "Chỉ tiêu tuyển sinh", Name = "Xóa chỉ tiêu tuyển sinh")]
        CHI_TIEU_TUYEN_SINH_DELETE,

        [Display(GroupName = "Chỉ tiêu tuyển sinh phương thức", Name = "Xem thông tin chỉ tiêu tuyển sinh phương thức")]
        CHI_TIEU_TUYEN_SINH_PHUONG_THUC_VIEW,

        [Display(GroupName = "Chỉ tiêu tuyển sinh phương thức", Name = "Thêm mới chỉ tiêu tuyển sinh phương thức")]
        CHI_TIEU_TUYEN_SINH_PHUONG_THUC_ADD,

        [Display(GroupName = "Chỉ tiêu tuyển sinh phương thức", Name = "Cập nhật chỉ tiêu tuyển sinh phương thức")]
        CHI_TIEU_TUYEN_SINH_PHUONG_THUC_EDIT,

        [Display(GroupName = "Chỉ tiêu tuyển sinh phương thức", Name = "Xóa chỉ tiêu tuyển sinh phương thức")]
        CHI_TIEU_TUYEN_SINH_PHUONG_THUC_DELETE,

        [Display(GroupName = "Quản lý hồ sơ tuyển sinh", Name = "Xem danh sách hồ sơ tuyển sinh")]
        QUAN_LY_HO_SO_TUYEN_SINH_VIEW,

        [Display(GroupName = "Quản lý hồ sơ tuyển sinh", Name = "Cập nhật hồ sơ tuyển sinh")]
        QUAN_LY_HO_SO_TUYEN_SINH_EDIT,

        [Display(GroupName = "Quản lý hồ sơ tuyển sinh", Name = "Cập nhật trạng thái hồ sơ tuyển sinh")]
        QUAN_LY_HO_SO_TUYEN_DUYET_HO_SO,

        [Display(GroupName = "Quản lý hồ sơ tuyển sinh", Name = "Cập nhật trạng thái mở khóa hồ sơ")]
        QUAN_LY_HO_SO_TUYEN_MO_KHOA_HO_SO,

        [Display(GroupName = "Quản lý hồ sơ tuyển sinh", Name = "Cập nhật trạng thái tài chính")]
        QUAN_LY_HO_SO_TUYEN_DUYET_TAI_CHINH,

        [Display(GroupName = "Quản lý hồ sơ xét trúng tuyển", Name = "Xem danh sách hồ sơ xét trúng tuyển")]
        QUAN_LY_HO_SO_XET_TRUNG_TUYEN_VIEW,

        [Display(GroupName = "Quản lý hồ sơ xét trúng tuyển", Name = "Xét duyệt trúng tuyển")]
        QUAN_LY_HO_SO_XET_TRUNG_TUYEN_EDIT,

        [Display(GroupName = "Loại đối tượng", Name = "Xem thông tin loại đối tượng")]
        LOAI_DOI_TUONG_VIEW,

        [Display(GroupName = "Loại đối tượng", Name = "Thêm mới loại đối tượng")]
        LOAI_DOI_TUONG_ADD,

        [Display(GroupName = "Loại đối tượng", Name = "Cập nhật loại đối tượng")]
        LOAI_DOI_TUONG_EDIT,

        [Display(GroupName = "Loại đối tượng", Name = "Xóa loại đối tượng")]
        LOAI_DOI_TUONG_DELETE,

        [Display(GroupName = "Phương án điểm học bạ môn", Name = "Xem thông tin phương án học bạ môn")]
        PHUONG_AN_MON_VIEW,

        [Display(GroupName = "Phương án điểm học bạ môn", Name = "Thêm mới phương án học bạ môn")]
        PHUONG_AN_MON_ADD,

        [Display(GroupName = "Phương án điểm học bạ môn", Name = "Xóa phương án học bạ môn")]
        PHUONG_AN_MON_DELETE,

        [Display(GroupName = "Khoản thu", Name = "Xem thông tin khoản thu")]
        TUYEN_SINH_KHOAN_THU_VIEW,

        [Display(GroupName = "Thanh toán", Name = "Xem thông tin thanh toán")]
        TUYEN_SINH_THANH_TOAN_VIEW,

        [Display(GroupName = "Thanh toán", Name = "Tạo mới thanh toán")]
        TUYEN_SINH_THANH_TOAN_CREATE,

        [Display(GroupName = "Thanh toán", Name = "Cập nhật trạng thái thanh toán")]
        TUYEN_SINH_THANH_TOAN_UPDATE_STATUS,

        [Display(GroupName = "Khoản thu", Name = "Thêm mới khoản thu")]
        TUYEN_SINH_KHOAN_THU_ADD,

        [Display(GroupName = "Khoản thu", Name = "Cập nhật khoản thu")]
        TUYEN_SINH_KHOAN_THU_EDIT,

        [Display(GroupName = "Khoản thu", Name = "Xóa khoản thu")]
        TUYEN_SINH_KHOAN_THU_DELETE,

        [Display(GroupName = "Chứng chỉ", Name = "Xem thông tin chứng chỉ")]
        TUYEN_SINH_CHUNG_CHI_VIEW,

        [Display(GroupName = "Chứng chỉ", Name = "Thêm mới chứng chỉ")]
        TUYEN_SINH_CHUNG_CHI_ADD,

        [Display(GroupName = "Chứng chỉ", Name = "Cập nhật chứng chỉ")]
        TUYEN_SINH_CHUNG_CHI_EDIT,

        [Display(GroupName = "Chứng chỉ", Name = "Xóa chứng chỉ")]
        TUYEN_SINH_CHUNG_CHI_DELETE
    }
}
