{"Localization.DefaultLanguage": "Default language", "TuyenSinhChiTieuTuyenSinh.Existed": "Enrollment quotas already exist", "InsertData.ChiTieuTuyenSinhPhuongThuc.Duplicate": "Repeat enrollment method", "ChiTieuTuyenSinhPhuongThuc.ChiTieuTuyenSinh.Invalid": "Total target is invalid", "data.not-found": "Data does not exist", "DoiTuongTuyenSinh.Existed": "Enrollment object {0} already exists", "DoiTuongXetTuyen.Existed": "Admission object {0} already exists", "TuyenSinhDotDangKy.DotDangKy.Existed": "Registration batch code {0} already exists", "AccountTuyenSinh.Existed": "Account {0} already exists", "Recaptcha.InValidate": "Recaptcha validation error", "AccountTuyenSinh.NotExisted": "Account does not exist", "Huyen.IdHuyen.Existed": "District code {0} already exists", "KhuVucUuTien.MaKv.Existed": "Priority area code {0} already exists", "GiayTo.MaGiayTo.Existed": "Document code {0} already exists", "TuyenSinhNganh.MaNganh.Existed": "Industry code {0} already exists", "ho-so.NotExist": "Admissions records do not exist", "ho-so.NoDataToUpdate": "No updated data", "InputData.IdHoSo.Duplicate": "Admission status cannot be updated for the same candidate", "TuyenSinhDangKyXetTuyen.TrungTuyen.Accepted": "Candidate {0} has passed the exam for {1} major {2} method {3}", "TuyenSinhDangKyXetTuyen.NotExist": "No candidate wishes found", "TuyenSinhThiSinhDangKyXetTuyen.NotExist": "Candidate's admission registration could not be found", "Tinh.IdTinh.Existed": "The province code already exists", "InsertData.IdTinh.Duplicate": "The input data is duplicated", "TruongTHPT.MaTruongTHPT.Existed": "High School {0} already exists", "MaTinhTHPT.NotExisted": "Province code {0} does not exist", "MaHuyenTHPT.NotExisted": "District code {0} does not exist", "TuyenSinhMauPhieu.Existed": "Voucher form {0} already exists", "TuyenSinhMonXetTuyen.MaMonXetTuyen.Existed": "Subject code {0} already exists", "TuyenSinhPhuongThucXetTuyen.MaPhuongThucXetTuyen.Existed": "Method code {0} already exists", "HoSoTuyenSinh.NotExisted": "Profile {0} does not exist", "InsertData.IdMonXetTuyen.Duplicate": "Duplicate academic subjects for admission", "InsertData.Duplicate": "Duplicate aspirations", "InsertData.ThuTuXet.Invalid": "The admission order is not valid", "TuyenSinhDangKyXetTuyen.Exist": "The desire for admission already exists", "InsertData.IdGiayTo.Duplicate": "Duplicate documents submitted", "InsertData.IdDoiTuongXetTuyen.Duplicate": "Duplicate admission subjects", "TuyenSinhDoiTuongXetTuyen.Exist": "The subject for admission already exists", "TuyenSinhKetQuaThiThpt.Exist": "The results of the National High School Exam already exist", "TuyenSinhKetQuaThiNangKhieu.Exist": "Aptitude test results already exist", "TuyenSinhKetQuaDanhGiaNangLuc.Exist": "Competency assessment test results already exist", "TuyenSinhThiSinhDangKyXetTuyens.Exist": "Candidates have registered for this method of admission", "TuyenSinhToHopMonXetTuyen.Existed": "Admission subject combination {0} already exists", "TuyenSinhToHopNganhXetTuyen.Existed": "Admissions industry complex {0} already exists", "TuyenSinhToHopXetTuyen.MaToHop.Existed": "The combination code {0} already exists", "Xa.TenXa.Existed": "Commune {0} already exists", "XepLoaiHanhKiemTHPT.MaXepLoaiHanhKiemTHPT.Existed": "Conduct rating code {0} already exists", "XepLoaiHocTapTHPT.MaXepLoaiHocTapTHPT.Existed": "Academic rating code {0} already exists", "TuyenSinhKetQuaThacSi.Exist": "The master's admission scores already exist", "TuyenSinhKhoanThu.NotFound": "Fee category not found", "TuyenSinhChungChi.NotFound": "Certificate not found", "TuyenSinhChungChi.Duplicate": "Certificate name and type already exists", "TuyenSinhChungChi.TenChungChi.Required": "Certificate name is required", "TuyenSinhChungChi.TenChungChi.MaxLength": "Certificate name cannot exceed 200 characters", "PhanHe.Not-Existed": "System module does not exist", "diem-hoc-ba.nguong-diem.invalid": "Academic transcript scores do not meet the admission threshold", "diem-hoc-ba.invalid": "Academic transcript scores must be between 0 and 10", "TuyenSinhThanhToan.NotFound": "Payment not found", "TuyenSinhThanhToan.MaGiaoDich.MaxLength": "Transaction code cannot exceed 100 characters", "TuyenSinhThanhToan.NoiDung.MaxLength": "Content cannot exceed 200 characters", "TuyenSinhThanhToan.NganHang.MaxLength": "Bank name cannot exceed 50 characters", "TuyenSinhThanhToan.DonViTinh.MaxLength": "Unit cannot exceed 50 characters", "TuyenSinhThanhToan.Ids.Required": "ID list is required", "TuyenSinhThanhToan.TrangThai.Required": "Status is required"}