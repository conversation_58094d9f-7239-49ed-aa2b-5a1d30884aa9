using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Core.Shared;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/phuong-thuc-xet-tuyen")]
    [ApiExplorerSettings(GroupName = "02. Phương thức xét tuyển")]
    [Authorize]
    public class TuyenSinhPhuongThucXetTuyenController : ApiControllerBase
    {
        public TuyenSinhPhuongThucXetTuyenController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }
        
        /// <summary>
        /// Thêm mới phương thức xét tuyển
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.PHUONG_THUC_TUYEN_SINH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateTuyenSinhPhuongThucXetTuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_PHUONG_THUC_XET_TUYEN_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_PHUONG_THUC_XET_TUYEN_CREATE;
                

                model.CreateUserName = u.UserName;
                return await _mediator.Send(new CreateTuyenSinhPhuongThucXetTuyenCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Chỉnh sửa phương thức xét tuyển
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        /// <param name="model">Thông tin cần cập nhật</param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.PHUONG_THUC_TUYEN_SINH_EDIT))]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] UpdateTuyenSinhPhuongThucXetTuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                model.IdPhuongThucXetTuyen = id;
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_PHUONG_THUC_XET_TUYEN_UPDATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_PHUONG_THUC_XET_TUYEN_UPDATE;
                

                return await _mediator.Send(new UpdateTuyenSinhPhuongThucXetTuyenCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy chi tiết phương thức xét tuyển
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhPhuongThucXetTuyenModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.PHUONG_THUC_TUYEN_SINH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTuyenSinhPhuongThucXetTuyenByIdQuery(id)));
        }

        /// <summary>
        /// Xóa phương thức xét tuyển
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.PHUONG_THUC_TUYEN_SINH_DELETE))]
        public async Task<IActionResult> Detele([FromRoute] int id)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_PHUONG_THUC_XET_TUYEN_DELETE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_PHUONG_THUC_XET_TUYEN_DELETE;
                

                return await _mediator.Send(new DeleteTuyenSinhPhuongThucXetTuyenCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách phương thức xét tuyển theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns>Danh sách phương thức xét tuyển</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TuyenSinhPhuongThucXetTuyenBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.PHUONG_THUC_TUYEN_SINH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TuyenSinhPhuongThucXetTuyenQueryFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterTuyenSinhPhuongThucXetTuyenQuery(filter)));
        }
        
        /// <summary>
        /// Lấy danh sách phương thức xét tuyển cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TuyenSinhPhuongThucXetTuyenSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxTuyenSinhPhuongThucXetTuyenQuery(count, ts));
            });
        }
    }
}