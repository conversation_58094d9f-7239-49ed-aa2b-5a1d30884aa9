using Core.API.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Admissions.Business;
using Admissions.Shared;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/loai-giay-to")]
    [ApiExplorerSettings(GroupName = "18. Loại giấy tờ")]
    [Authorize]
    public class LoaiGiayToController : ApiControllerBase
    {
        public LoaiGiayToController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Thêm mới giấy tờ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.GIAY_TO_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateLoaiGiayToModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_LOAI_GIAY_TO_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_LOAI_GIAY_TO_CREATE;
                

                return await _mediator.Send(new CreateLoaiGiayToCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách giấy tờ có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<LoaiGiayToBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.GIAY_TO_VIEW))]
        public async Task<IActionResult> Filter([FromBody] LoaiGiayToFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterLoaiGiayToQuery(filter)));
        }

        /// <summary>
        /// Xóa giấy tờ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.GIAY_TO_DELETE))]
        public async Task<IActionResult> Delete( int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_LOAI_GIAY_TO_DELETE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_LOAI_GIAY_TO_DELETE;
                

                return await _mediator.Send(new DeleteLoaiGiayToCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa giấy tờ
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.GIAY_TO_EDIT))]
        public async Task<IActionResult> Update( int id, [FromBody] UpdateLoaiGiayToModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_LOAI_GIAY_TO_UPDATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_LOAI_GIAY_TO_UPDATE;
                

                return await _mediator.Send(new UpdateLoaiGiayToCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy chi tiết giấy tờ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<LoaiGiayToModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.GIAY_TO_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetLoaiGiayToByIdQuery(id)));
        }

        /// <summary>
        /// Lấy danh sách giấy tờ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<LoaiGiayToSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxLoaiGiayToQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách giấy tờ yêu cầu
        /// </summary>                                                                                                                                 
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpPost, Route("yeu-cau")]
        [ProducesResponseType(typeof(ResponseObject<List<LoaiGiayToSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(LoaiGiayToYeuCauRequestModel request)
        {
            return await ExecuteFunction(async u =>
            {
                return await _mediator.Send(new GetLoaiGiayToYeuCauHocBaQuery(request, u.SystemLog));
            });
        }
    }
}
