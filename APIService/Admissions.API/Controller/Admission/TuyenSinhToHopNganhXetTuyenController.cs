using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Admissions.API.Controller
{
    /// <summary>
    /// Module tổ hợp ngành xét tuyển
    /// </summary>
    [ApiController]
    [Route("admission/v1/tuyen-sinh-to-hop-nganh-xet-tuyen")]
    [ApiExplorerSettings(GroupName = "27. Tổ hợp ngành xét tuyển")]
    [Authorize]
    public class TuyenSinhToHopNganhXetTuyenController : ApiControllerBase
    {
        public TuyenSinhToHopNganhXetTuyenController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// Thêm mới tổ hợp ngành xét tuyển
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TO_HOP_NGANH_XET_TUYEN_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateTuyenSinhToHopNganhXetTuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TO_HOP_NGANH_XET_TUYEN_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_TO_HOP_NGANH_XET_TUYEN_CREATE;
                
                return await _mediator.Send(new CreateTuyenSinhToHopNganhXetTuyenCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Chỉnh sửa tổ hợp ngành xét tuyển
        /// </summary>
        /// <param name="id">Id tổ hợp ngành xét tuyển</param>
        /// <param name="model">Thông tin cần cập nhật</param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TO_HOP_NGANH_XET_TUYEN_EDIT))]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] UpdateTuyenSinhToHopNganhXetTuyenModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TO_HOP_NGANH_XET_TUYEN_UPDATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_TO_HOP_NGANH_XET_TUYEN_UPDATE;
                

                return await _mediator.Send(new UpdateTuyenSinhToHopNganhXetTuyenCommand(id, model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy chi tiết tổ hợp ngành xét tuyển
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhToHopNganhXetTuyenModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TO_HOP_NGANH_XET_TUYEN_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTuyenSinhToHopNganhXetTuyenByIdQuery(id)));
        }

        /// <summary>
        /// Xóa tổ hợp ngành xét tuyển
        /// </summary>
        /// <param name="id">Id tổ hợp ngành xét tuyển</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TO_HOP_NGANH_XET_TUYEN_DELETE))]
        public async Task<IActionResult> Detele([FromRoute] int id)
        {
            return await ExecuteFunction(async (u) =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TO_HOP_NGANH_XET_TUYEN_DELETE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_TO_HOP_NGANH_XET_TUYEN_DELETE;
                

                return await _mediator.Send(new DeleteTuyenSinhToHopNganhXetTuyenCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách tổ hợp ngành xét tuyển theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns>Danh sách tổ hợp ngành xét tuyển</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TuyenSinhToHopNganhXetTuyenDetailModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TO_HOP_NGANH_XET_TUYEN_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TuyenSinhToHopNganhXetTuyenFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterTuyenSinhToHopNganhXetTuyenQuery(filter)));
        }

        /// <summary>
        /// Lấy danh sách tổ hợp ngành cho combobox
        /// </summary> 
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <returns>Danh sách ngành</returns> 
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TuyenSinhToHopNganhXetTuyenSelectModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetForCombobox(int count = 0, string ts = "", int idHe = 0, int idNganh = 0)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetComboboxTuyenSinhToHopNganhXetTuyenQuery(count, ts, idNganh, idHe)));
        }
    }
}
