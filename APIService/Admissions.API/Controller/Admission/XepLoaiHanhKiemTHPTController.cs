using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/xep-loai-hanh-kiem-thpt")]
    [ApiExplorerSettings(GroupName = "25. Xếp loại hạnh kiểm THPT")]
    [Authorize]
    public class XepLoaiHanhKiemTHPTController : ApiControllerBase
    {
        public XepLoaiHanhKiemTHPTController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Thêm mới xếp loại hạnh kiểm THPT
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.XEP_LOAI_HANH_KIEM_THPT_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXepLoaiHanhKiemTHPTModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_XEP_LOAI_HANH_KIEM_THPT_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_XEP_LOAI_HANH_KIEM_THPT_CREATE;
                

                return await _mediator.Send(new CreateXepLoaiHanhKiemTHPTCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách xếp loại hạnh kiểm THPT có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XepLoaiHanhKiemTHPTBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.XEP_LOAI_HANH_KIEM_THPT_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XepLoaiHanhKiemTHPTFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXepLoaiHanhKiemTHPTQuery(filter)));
        }

        /// <summary>
        /// Xóa xếp loại hạnh kiểm THPT
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.XEP_LOAI_HANH_KIEM_THPT_DELETE))]
        public async Task<IActionResult> Delete( int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_XEP_LOAI_HANH_KIEM_THPT_DELETE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_XEP_LOAI_HANH_KIEM_THPT_DELETE;
                

                return await _mediator.Send(new DeleteXepLoaiHanhKiemTHPTCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa xếp loại hạnh kiểm THPT
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.XEP_LOAI_HANH_KIEM_THPT_EDIT))]
        public async Task<IActionResult> Update( int id, [FromBody] UpdateXepLoaiHanhKiemTHPTModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_XEP_LOAI_HANH_KIEM_THPT_UPDATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_XEP_LOAI_HANH_KIEM_THPT_UPDATE;
                

                /*model.CreateUserName = u.UserName;*/
                return await _mediator.Send(new UpdateXepLoaiHanhKiemTHPTCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy chi tiết xếp loại hạnh kiểm THPT
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XepLoaiHanhKiemTHPTDetailModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.XEP_LOAI_HANH_KIEM_THPT_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXepLoaiHanhKiemTHPTByIdQuery(id)));
        }

        /// <summary>
        /// Lấy danh sách xếp loại hạnh kiểm THPT cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepLoaiHanhKiemTHPTSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepLoaiHanhKiemTHPTQuery(count, ts));
            });
        }
    }
}
