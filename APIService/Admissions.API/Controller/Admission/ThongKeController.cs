using Core.API.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Admissions.Business;
using Admissions.Shared;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/thong-ke")]
    [ApiExplorerSettings(GroupName = "33. Thống kê")]
    [Authorize]
    public class ThongKeController : ApiControllerBase
    {
        public ThongKeController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Lấy danh sách thống kê đăng ký theo ngành
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("dang-ky-theo-nganh")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DangKyTheoNganh([FromBody] ThongKeFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterThongKeDangKyQuery(filter)));
        }

        /// <summary>
        /// Lấy danh sách thống kê đăng ký theo ptxt
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("dang-ky-theo-ptxt")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DangKyTheoPtxt([FromBody] ThongKeFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterThongKeDangKyPhuongThucQuery(filter)));
        }

        /// <summary>
        /// Lấy danh sách thống kê trúng tuyển và chi tieu
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("trung-tuyen-chi-tieu")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ThongKeBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> TrungTuyenChiTieu([FromBody] ThongKeFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterThongKeChiTieuNganhQuery(filter)));
        }
    }
}
