using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/dot-tuyen-sinh")]
    [ApiExplorerSettings(GroupName = "01. Đợt tuyển sinh")]
    [Authorize]
    public class TuyenSinhDotDangKyController : ApiControllerBase
    {
        public TuyenSinhDotDangKyController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Thêm mới đợt tuyển sinh
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.DOT_TUYEN_SINH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateTuyenSinhDotDangKy model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_DOT_TUYEN_SINH_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_DOT_TUYEN_SINH_CREATE;
                

                return await _mediator.Send(new CreateTuyenSinhDotDangKyCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách đợt tuyển sinh có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TuyenSinhDotDangKyBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.DOT_TUYEN_SINH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TuyenSinhDotDangKyFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterTuyenSinhDotDangKyQuery(filter)));
        }

        /// <summary>
        /// Xóa đợt tuyển sinh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.DOT_TUYEN_SINH_DELETE))]
        public async Task<IActionResult> Delete( int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_DOT_TUYEN_SINH_DELETE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_DOT_TUYEN_SINH_DELETE;
                

                return await _mediator.Send(new DeleteTuyenSinhDotDangKyCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa đợt tuyển sinh
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.DOT_TUYEN_SINH_EDIT))]
        public async Task<IActionResult> Update( int id, [FromBody] UpdateTuyenSinhDotDangKy request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_DOT_TUYEN_SINH_UPDATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_DOT_TUYEN_SINH_UPDATE;
                

                /*model.CreateUserName = u.UserName;*/
                return await _mediator.Send(new UpdateTuyenSinhDotDangKyCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy chi tiết đợt tuyển sinh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhDotDangKyModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.DOT_TUYEN_SINH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTuyenSinhDotDangKyByIdQuery(id)));
        }

        /// <summary>
        /// Lấy danh sách đợt tuyển sinh cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TuyenSinhDotDangKySelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxTuyenSinhDotDangKyQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy chi tiết đợt tuyển sinh theo mã
        /// </summary>
        /// <param name="maDotDangKy">Mã đợt đăng ký</param>
        /// <returns></returns>
        [HttpGet, Route("ma-dot-dang-ky")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhDotDangKyModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(string maDotDangKy = "")
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTuyenSinhDotDangKyByMaDotDangKyQuery(maDotDangKy)));
        }
    }
}
