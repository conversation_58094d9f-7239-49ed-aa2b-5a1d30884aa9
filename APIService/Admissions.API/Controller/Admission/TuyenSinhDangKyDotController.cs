using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/tuyen-sinh-dang-ky-dot")]
    [ApiExplorerSettings(GroupName = "20. Tuyển sinh đăng ký đợt")]
    [Authorize]
    public class TuyenSinhDangKyDotController : ApiControllerBase
    {
        public TuyenSinhDangKyDotController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Thêm mới tuyển sinh đăng ký đợt
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_DANG_KY_DOT_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateTuyenSinhDangKyDot model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_DANG_KY_DOT_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_DANG_KY_DOT_CREATE;
                

                return await _mediator.Send(new CreateTuyenSinhDangKyDotCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách tuyển sinh đăng ký đợt có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TuyenSinhDangKyDotBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_DANG_KY_DOT_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TuyenSinhDangKyDotFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterTuyenSinhDangKyDotQuery(filter)));
        }

        /// <summary>
        /// Xóa tuyển sinh đăng ký đợt
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_DANG_KY_DOT_DELETE))]
        public async Task<IActionResult> Delete( int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_DANG_KY_DOT_DELETE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_DANG_KY_DOT_DELETE;
                

                return await _mediator.Send(new DeleteTuyenSinhDangKyDotCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa tuyển sinh đăng ký đợt
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_DANG_KY_DOT_EDIT))]
        public async Task<IActionResult> Update( int id, [FromBody] UpdateTuyenSinhDangKyDot request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_DANG_KY_DOT_UPDATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_DANG_KY_DOT_UPDATE;
                

                /*model.CreateUserName = u.UserName;*/
                return await _mediator.Send(new UpdateTuyenSinhDangKyDotCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy chi tiết tuyển sinh đăng ký đợt
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhDangKyDotModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_DANG_KY_DOT_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTuyenSinhDangKyDotByIdQuery(id)));
        }

    }
}
