using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/tuyen-sinh-thanh-toan")]
    [ApiExplorerSettings(GroupName = "52. Thanh toán")]
    //[Authorize]
    public class TuyenSinhThanhToanController : ApiControllerBaseV2
    {
        public TuyenSinhThanhToanController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {

        }

        /// <summary>
        /// <PERSON><PERSON>y danh sách thanh toán có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TuyenSinhThanhToanBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_THANH_TOAN_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TuyenSinhThanhToanFilterModel filter)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetFilterTuyenSinhThanhToanQuery(filter));
            });
        }

        /// <summary>
        /// Lấy chi tiết thanh toán theo ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhThanhToanModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_THANH_TOAN_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetTuyenSinhThanhToanByIdQuery(id));
            });
        }

        /// <summary>
        /// Cập nhật trạng thái thanh toán từ file
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("update-status-thanh-toan")]
        [ProducesResponseType(typeof(ResponseObject<UpdateStatusThanhToanResult>), StatusCodes.Status200OK)]
        //[ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_THANH_TOAN_UPDATE_STATUS))]
        public async Task<IActionResult> UpdateFromBank([FromBody] UpdateStatusThanhToanModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_THANH_TOAN_UPDATE_TRANG_THAI);
                _contextAccessor.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_THANH_TOAN_UPDATE_TRANG_THAI;

                return await _mediator.Send(new UpdateStatusThanhToanCommand(model));
            });
        }

        /// <summary>
        /// Lấy danh sách ngân hàng thanh toán online cho combobox
        /// </summary>
        /// <param name="count">Số lượng kết quả trả về</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <returns></returns>
        [HttpGet, Route("ngan-hang-thanh-toan-online/for-combobox")]
        [ProducesResponseType(typeof(List<Admissions.Business.NganHangThanhToanOnlineSelectItemModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetComboboxNganHangThanhToanOnline(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetComboboxNganHangThanhToanOnlineQuery(count, ts));
            });
        }

        /// <summary>
        /// Tạo thanh toán mới cho hồ sơ tuyển sinh
        /// </summary>
        /// <param name="model">Thông tin thanh toán</param>
        /// <returns></returns>
        [HttpPost, Route("create")]
        [ProducesResponseType(typeof(ResponseObject<CreateThanhToanResult>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_THANH_TOAN_CREATE))]
        public async Task<IActionResult> CreateThanhToan([FromBody] CreateThanhToanModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_THANH_TOAN_CREATE);
                _contextAccessor.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_THANH_TOAN_CREATE;

                // Set người tạo
                model.NguoiTao = _contextAccessor.UserName;

                return await _mediator.Send(new CreateThanhToanCommand(model));
            });
        }
    }
}
