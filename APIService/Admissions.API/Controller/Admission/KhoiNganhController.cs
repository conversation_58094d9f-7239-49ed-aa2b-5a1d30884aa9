using Core.API.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Admissions.Business;
using Admissions.Shared;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/khoi-nganh")]
    [ApiExplorerSettings(GroupName = "05. Khối ngành")]
    [Authorize]
    public class KhoiNganhController : ApiControllerBase
    {
        public KhoiNganhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Thêm mới khối ngành
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.KHOI_NGANH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateKhoiNganh model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_KHOI_NGANH_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_KHOI_NGANH_CREATE;
                

                return await _mediator.Send(new CreateKhoiNganhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách khối ngành có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<KhoiNganhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.KHOI_NGANH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] KhoiNganhFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterKhoiNganhQuery(filter)));
        }

        /// <summary>
        /// Xóa khối ngành
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.KHOI_NGANH_DELETE))]
        public async Task<IActionResult> Delete( int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_KHOI_NGANH_DELETE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_KHOI_NGANH_DELETE;
                

                return await _mediator.Send(new DeleteKhoiNganhCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa khối ngành
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.KHOI_NGANH_EDIT))]
        public async Task<IActionResult> Update( int id, [FromBody] UpdateKhoiNganh request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_KHOI_NGANH_UPDATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_KHOI_NGANH_UPDATE;
                

                /*model.CreateUserName = u.UserName;*/
                return await _mediator.Send(new UpdateKhoiNganhCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy chi tiết khối ngành
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<KhoiNganhModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.KHOI_NGANH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetKhoiNganhByIdQuery(id)));
        }

        /// <summary>
        /// Lấy danh sách khối ngành cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<KhoiNganhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxKhoiNganhQuery(count, ts));
            });
        }
    }
}
