using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission/v1/tuyen-sinh-chung-chi")]
    [ApiExplorerSettings(GroupName = "51. Chứng chỉ")]
    [Authorize]
    public class TuyenSinhChungChiController : ApiControllerBaseV2
    {
        public TuyenSinhChungChiController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {

        }

        /// <summary>
        /// Thêm mới chứng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_CHUNG_CHI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateTuyenSinhChungChiModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_CHUNG_CHI_CREATE);
                _contextAccessor.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_CHUNG_CHI_CREATE;

                return await _mediator.Send(new CreateTuyenSinhChungChiCommand(model));
            });
        }

        /// <summary>
        /// Import excel chứng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_CHUNG_CHI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyTuyenSinhChungChiModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_CHUNG_CHI_CREATE_MANY);
                _contextAccessor.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_CHUNG_CHI_CREATE_MANY;

                return await _mediator.Send(new CreateManyTuyenSinhChungChiCommand(model));
            });
        }

        /// <summary>
        /// Lấy danh sách chứng chỉ có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TuyenSinhChungChiBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_CHUNG_CHI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TuyenSinhChungChiFilterModel filter)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetFilterTuyenSinhChungChiQuery(filter));
            });
        }

        /// <summary>
        /// Xóa chứng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_CHUNG_CHI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_CHUNG_CHI_DELETE);
                _contextAccessor.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_CHUNG_CHI_DELETE;

                return await _mediator.Send(new DeleteTuyenSinhChungChiCommand(id));
            });
        }

        /// <summary>
        /// Sửa chứng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_CHUNG_CHI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateTuyenSinhChungChiModel request)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_CHUNG_CHI_UPDATE);
                _contextAccessor.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_CHUNG_CHI_UPDATE;

                request.Id = id;
                return await _mediator.Send(new UpdateTuyenSinhChungChiCommand(request));
            });
        }

        /// <summary>
        /// Lấy chi tiết chứng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhChungChiModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionAdmissionEnum.TUYEN_SINH_CHUNG_CHI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async () => await _mediator.Send(new GetTuyenSinhChungChiByIdQuery(id)));
        }

        /// <summary>
        /// Lấy danh sách chứng chỉ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TuyenSinhChungChiSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetComboboxTuyenSinhChungChiQuery(count, ts));
            });
        }
    }
}
