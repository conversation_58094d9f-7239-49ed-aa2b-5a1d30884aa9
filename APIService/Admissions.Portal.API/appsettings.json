{
  "AppSettings": {
    "EnableSwagger": "true",
    "Tittle": "Admissions Portal UniCore Service",
    "Description": "Net Core Framework",
    "TermsOfService": "",
    "Contact": {
      "Name": "HaiND",
      "Email": "<EMAIL>",
      "Url": "https://github.com/duchaindh94"
    },
    "License": {
      "Name": "The MIT License (MIT).",
      "Url": "https://github.com/dotnet/core"
    },
    "Message": {
      "ReturnDetailError500Message": "true"
    },
    "CorsOrigins": "http://localhost:8000",
    "EnableCache": "true",
    "IgnoreLoadPermission": "true",
    "ShowRequestDuration": "true",
    "UploadConfig": {
      "FolderUpload": "files", // Thư mục lưu file upload với trường hợp upload và lưu file v<PERSON><PERSON> thư mục
      "AllowedExtensions": [ ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx" ],
      "MaxFileSize": "10" // Mb
    },
    "IPHeader": "X-Forwarded-For" // Header chứa IP client" - "RemoteIpAddress", "X-Forwarded-For", "X-Real-Ip"
  },
  "Exports": {
    "FormXTHBPath": "Templates/BieuMauDkxts/Form_xet_tuyen_hoc_ba.docx",
    "FormXDGNLPath": "Templates/BieuMauDkxts/Form_danh_gia_nang_luc.docx",
    "FormThacSiPath": "Templates/BieuMauDkxts/Form_thac_si.doc",
    "FormTienSiPath": "Templates/BieuMauDkxts/Form_tien_si.doc",
    "TenDonViHardCode": "Trường Đại học Mở Hà Nội"
  },
  "ThanhToanUniSoft": {
    "Url": "https://coreuat.unisoft.edu.vn/pay/qrCodeView",
    "PrivateKey": "8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081"
  },
  "Database": {
    "System": {
      "ConnectionString": {
        "MSSQLDatabase": "server=**************;database=UNISOFT_FULL6_TEST;User ID=Unisoftadmin;password=*****************;TrustServerCertificate=True;",
        "MSSQLDatabaseRead": "server=**************;database=UNISOFT_FULL6_TEST;User ID=Unisoftadmin;password=*****************;TrustServerCertificate=True;"
      }
    },
    "Admission": {
      "ConnectionString": {
        "MSSQLDatabase": "server=**************;database=UNISOFT_FULL6_DEV;User ID=Unisoftdev;password=*****************;TrustServerCertificate=True;",
        "MSSQLDatabaseRead": "server=**************;database=UNISOFT_FULL6_DEV;User ID=Unisoftdev;password=*****************;TrustServerCertificate=True;"
      }
    }
  },
  "Recaptcha": {
    "SiteKey": "6LfcEzIpAAAAAAWlDFZmSytWd84wQzrnlnsrSmm8",
    "SecretKey": "6LfcEzIpAAAAAK76FBBYR2_gIcC6-PgCpJAztuGV"
  },
  "redis": {
    "enabled": "true",
    "configuration": "************:6379,password=thienan123,defaultDatabase=0",
    "instanceName": "UniSoft:",
    "timeLive": "30000"
  },
  "EmailSettings": {
    "DefaultFromEmail": "<EMAIL>",
    "DefaultFromName": "UniSoft",
    "Host": "smtp.gmail.com",
    "Port": 587,
    "UserName": "<EMAIL>",
    "Password": "",
    "SSL": "1"
  },
  "Authentication": {
    "Jwt": {
      "Enable": "true",
      "Key": "Unable-to-create-KeyedHashAlgorithm-for-algorithm",
      "Issuer": "NETCORE-ORION-CORP",
      "TimeToLive": "3600"
    },
    "IdentityServer": {
      "Enable": "false",
      "Uri": "http://sso.unisoft.edu.vn",
      "ClientId": "uni-hrm-portal-client",
      "Secret": "pt0bM7sY!9*cpT7s$MjGB4s"
    },
    "AdminUser": {
      "Username": "admin",
      "Password": "123456"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Core.API.Shared.CustomAuthHandler": "Error"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.File"
    ],
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Core.API.Shared.CustomAuthHandler": "Error",
        "Microsoft.EntityFrameworkCore.Database.Command": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "File",
              "Args": {
                "path": "/opt/logs_folder/adminssion-portal/log-.txt",
                "rollingInterval": "Day",
                "fileSizeLimitBytes": 10485760,
                "retainedFileCountLimit": 100,
                "rollOnFileSizeLimit": true,
                "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{CorrelationId}][{TraceIdentifier}] <{SourceContext}> {Message}{NewLine}{Exception}"
              }
            },
            {
              "Name": "Console",
              "Args": {
                "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console",
                "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{CorrelationId}][{TraceIdentifier}] <{SourceContext}> {Message}{NewLine}{Exception}"
              }
            },
            {
              "Name": "GrafanaLoki",
              "Args": {
                "uri": "http://localhost:3100",
                "credentials": {
                  "login": "grafanalokiuser",
                  "password": "grafanalokipass"
                },
                "labels": [
                  {
                    "key": "app",
                    "value": "admission-portal-api-test"
                  }
                ]
              }
            }
          ]
        }
      }
    ]
  }
}
