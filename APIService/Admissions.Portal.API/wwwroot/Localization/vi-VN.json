{"redis.test.label": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i redis", "redis.test.current-time": "<PERSON><PERSON><PERSON><PERSON> gian hiện tại", "redis.test.cache-time": "<PERSON>h<PERSON><PERSON> gian l<PERSON>u cache", "Localization.DefaultLanguage": "<PERSON><PERSON><PERSON> ngữ mặc định", "TuyenSinhChiTieuTuyenSinh.Existed": "Chỉ tiêu tuyển sinh đã tồn tại", "InsertData.ChiTieuTuyenSinhPhuongThuc.Duplicate": "<PERSON><PERSON><PERSON><PERSON> thức tuyển sinh lặp lại", "ChiTieuTuyenSinhPhuongThuc.ChiTieuTuyenSinh.Invalid": "Tổng chỉ tiêu không hợp lệ", "data.not-found": "<PERSON><PERSON> liệu không tồn tại", "DoiTuongTuyenSinh.Existed": "<PERSON><PERSON><PERSON> tượng tuyển sinh {0} đã tồn tại", "DoiTuongXetTuyen.Existed": "<PERSON><PERSON><PERSON> tượ<PERSON> xét tuyển {0} đã tồn tại", "TuyenSinhDotDangKy.DotDangKy.Existed": "Mã đợt đ<PERSON>ng ký {0} đã tồn tại", "AccountTuyenSinh.Existed": "<PERSON><PERSON><PERSON> {0} đã tồn tại", "Recaptcha.InValidate": "Lỗi xác thực recaptcha", "AccountTuyenSinh.NotExisted": "<PERSON><PERSON><PERSON> k<PERSON>n không tồn tại", "Huyen.IdHuyen.Existed": "Mã huyện {0} đã tồn tại", "KhuVucUuTien.MaKv.Existed": "Mã khu vực ưu tiên {0} đã tồn tại", "GiayTo.MaGiayTo.Existed": "Mã giấy tờ {0} đã tồn tại", "TuyenSinhNganh.MaNganh.Existed": "<PERSON>ã ngành {0} đã tồn tại", "ho-so.NotExist": "<PERSON><PERSON> sơ tuyển sinh không tồn tại", "ho-so.NoDataToUpdate": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu đ<PERSON><PERSON><PERSON> cập nhật", "InputData.IdHoSo.Duplicate": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> cập nhật trạng thái trúng tuyển cho cùng một thí sinh", "TuyenSinhDangKyXetTuyen.TrungTuyen.Accepted": "<PERSON><PERSON><PERSON> sinh {0} đ<PERSON> trúng tuyển nguyện vọng {1} ng<PERSON><PERSON> {2} ph<PERSON><PERSON><PERSON> thức {3}", "TuyenSinhDangKyXetTuyen.NotExist": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nguyện vọng thí sinh", "TuyenSinhThiSinhDangKyXetTuyen.NotExist": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đăng ký xét tuyển của thí sinh", "Tinh.IdTinh.Existed": "Mã tỉnh đã tồn tại", "InsertData.IdTinh.Duplicate": "<PERSON><PERSON> liệu nhập bị trùng lặp", "TruongTHPT.MaTruongTHPT.Existed": "Trường THPT {0} đã tồn tại", "MaTinhTHPT.NotExisted": "Mã tỉnh {0} không tồn tại", "MaHuyenTHPT.NotExisted": "<PERSON><PERSON> huyện {0} không tồn tại", "TuyenSinhMauPhieu.Existed": "Mẫu phiếu {0} đã tồn tại", "TuyenSinhMonXetTuyen.MaMonXetTuyen.Existed": "Mã môn {0} đã tồn tại", "TuyenSinhPhuongThucXetTuyen.MaPhuongThucXetTuyen.Existed": "<PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức {0} đã tồn tại", "HoSoTuyenSinh.NotExisted": "<PERSON><PERSON> sơ {0} kh<PERSON>ng tồn tại", "InsertData.IdMonXetTuyen.Duplicate": "<PERSON><PERSON>n xét tuyển học bạ trùng lặp", "InsertData.Duplicate": "Nguyện vọng trùng lặp", "InsertData.ThuTuXet.Invalid": "<PERSON><PERSON><PERSON> tự xét tuyển không hợp lệ", "TuyenSinhDangKyXetTuyen.Exist": "Nguyện vọng xét tuyển đã tồn tại", "InsertData.IdGiayTo.Duplicate": "<PERSON><PERSON><PERSON><PERSON> tờ nộp trùng lặp", "InsertData.IdDoiTuongXetTuyen.Duplicate": "<PERSON><PERSON><PERSON> tượng xét tuyển trùng lặp", "TuyenSinhDoiTuongXetTuyen.Exist": "<PERSON><PERSON><PERSON> tượng xét tuyển đã tồn tại", "TuyenSinhKetQuaThiThpt.Exist": "<PERSON><PERSON><PERSON> quả thi THPT Quốc gia đã tồn tại", "TuyenSinhKetQuaThiNangKhieu.Exist": "<PERSON><PERSON><PERSON> quả thi năng khiếu đã tồn tại", "TuyenSinhKetQuaDanhGiaNangLuc.Exist": "<PERSON><PERSON><PERSON> quả thi đánh giá năng lực đã tồn tại", "TuyenSinhThiSinhDangKyXetTuyens.Exist": "<PERSON><PERSON><PERSON> sinh đã đăng ký xét tuyển phư<PERSON><PERSON> thức này", "TuyenSinhToHopMonXetTuyen.Existed": "T<PERSON> hợp môn xét tuyển {0} đã tồn tại", "TuyenSinhToHopNganhXetTuyen.Existed": "<PERSON><PERSON> hợp ngành xét tuyển {0} đã tồn tại", "TuyenSinhToHopXetTuyen.MaToHop.Existed": "Mã tổ hợp {0} đã tồn tại", "Xa.TenXa.Existed": "Xã {0} đã tồn tại", "XepLoaiHanhKiemTHPT.MaXepLoaiHanhKiemTHPT.Existed": "Mã xếp loại hạnh kiểm {0} đã tồn tại", "XepLoaiHocTapTHPT.MaXepLoaiHocTapTHPT.Existed": "Mã xếp loại học tập {0} đã tồn tại", "TuyenSinhKetQuaThacSi.Exist": "<PERSON><PERSON><PERSON> quả điểm thạc sĩ đã tồn tại", "diem-hoc-ba.nguong-diem.invalid": "<PERSON><PERSON><PERSON><PERSON> học bạ dưới ngưỡng đảm bảo chất lượ<PERSON>", "diem-hoc-ba.invalid": "<PERSON><PERSON><PERSON><PERSON> học bạ phải nằm trong khoảng từ 0 - 10"}