using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission-portal/v1/gioi-tinh")]
    [ApiExplorerSettings(GroupName = "04. Giới tính")]
    [Authorize]
    public class GioiTinhController : ApiControllerBase
    {
        public GioiTinhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Get Combobox giới tính
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<GioiTinhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetComboBox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetComboboxGioiTinhQuery(count, ts)));
        }
    }
}
