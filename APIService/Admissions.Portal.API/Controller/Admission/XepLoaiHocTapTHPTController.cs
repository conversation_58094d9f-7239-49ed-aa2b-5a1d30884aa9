using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission-portal/v1/xep-loai-hoc-tap-thpt")]
    [ApiExplorerSettings(GroupName = "13. Xếp loại học tập THPT")]
    [Authorize]
    public class XepLoaiHocTapTHPTController : ApiControllerBase
    {
        public XepLoaiHocTapTHPTController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> danh sách xếp loại học tập THPT cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepLoaiHocTapTHPTSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepLoaiHocTapTHPTQuery(count, ts));
            });
        }
    }
}
