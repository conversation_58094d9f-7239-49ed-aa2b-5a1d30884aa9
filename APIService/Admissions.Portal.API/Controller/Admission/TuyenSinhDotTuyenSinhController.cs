using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission-portal/v1/dot-tuyen-sinh")]
    [ApiExplorerSettings(GroupName = "03. Đợt tuyển sinh")]
    public class TuyenSinhDotDangKyController : ApiControllerBase
    {
        public TuyenSinhDotDangKyController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách đợt tuyển sinh có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="he"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TuyenSinhDotDangKyBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] TuyenSinhDotDangKyFilterModel filter, [FromHeader] List<string> he = null)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                if (filter.maHes == null)
                {
                    filter.maHes = he;
                }
                return await _mediator.Send(new GetTuyenSinhDotDangKyForPortalQuery(filter, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách đợt tuyển sinh theo mã phương thức
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhDotDangKyBaseModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByMaPhuongThuc(string maPhuongThuc = "")
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTuyenSinhDotDangKyByMaPhuongThucQuery(maPhuongThuc)));
        }

        /// <summary>
        /// Lấy chi tiết đợt tuyển sinh
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("ma-dot-dang-ky")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhDotDangKyModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(string maDotDangKy = "")
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTuyenSinhDotDangKyByMaDotDangKyQuery(maDotDangKy)));
        }
    }
}
