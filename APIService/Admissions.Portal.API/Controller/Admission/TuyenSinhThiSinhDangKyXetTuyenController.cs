using Core.API.Shared;
using Admissions.Business;
using Admissions.Shared;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.Business;
using Microsoft.AspNetCore.Hosting;
using System.IO;
using System;
using Microsoft.Extensions.Configuration;

namespace UniAdmission.API.Controllers
{
    [ApiController]
    [Route("admission-portal/v1/tuyen-sinh-thi-sinh-dang-ky-xet-tuyen")]
    [ApiExplorerSettings(GroupName = "04. Tuyển sinh thí sinh đăng ký xét tuyển")]
    [Authorize]
    public class TuyenSinhThiSinhDangKyXetTuyenController : ApiControllerBase
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        public TuyenSinhThiSinhDangKyXetTuyenController(IMediator mediator, IStringLocalizer<Resources> localizer, IWebHostEnvironment webHostEnvironment, IConfiguration config) : base(mediator, localizer, config)
        {
            _webHostEnvironment = webHostEnvironment;
        }
        /// <summary>
        /// Form tuyển sinh học bạ
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("dang-ky-tuyen-sinh")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] CreateThiSinhDangKyXetTuyenHocBaModel filter)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_THI_SINH_DANG_KY_XET_TUYEN_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_THI_SINH_DANG_KY_XET_TUYEN_CREATE;

                return await _mediator.Send(new CreateThiSinhDangKyXetTuyenCommand(filter, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy thông tin hồ sơ
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("thong-tin-ho-so")]
        [ProducesResponseType(typeof(ResponseObject<TuyenSinhThiSinhDangKyXetTuyenDetailModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById([FromBody] TuyenSinhThiSinhDangKyXetTuyenFilterModel filter)
        {
            return await ExecuteFunction(async u => await _mediator.Send(new GetTuyenSinhThiSinhDangKyXetTuyenByIdQuery(filter, u.SystemLog)));
        }

        /// <summary>
        /// Lấy danh sách đăng ký xét tuyển của thí sinh
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet, Route("danh-sach-dang-ky-xet-tuyen")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByIdHoSo(int namTuyenSinh = 0)
        {
            return await ExecuteFunction(async u => await _mediator.Send(new GetTuyenSinhThiSinhDangKyXetTuyenByIdHoSoQuery(u.SystemLog, namTuyenSinh)));
        }

        /// <summary>
        /// Form tuyển sinh học bạ
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPut, Route("cap-nhat-ho-so")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromBody] UpdateTuyenSinhThiSinhDangKyXetTuyenModel filter)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_TUYEN_SINH_THI_SINH_DANG_KY_XET_TUYEN_CREATE);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_TUYEN_SINH_THI_SINH_DANG_KY_XET_TUYEN_CREATE;

                filter.ThiSinh = true;
                return await _mediator.Send(new UpdateThiSinhDangKyXetTuyenCommand(filter, u.SystemLog));
            });
        }


        /// <summary>
        /// Export biểu mẫu cán bộ
        /// </summary>
        /// <param name="request">Thông tin export</param>
        /// <returns></returns>
        [HttpPost, Route("export-bieu-mau")]
        [ProducesResponseType(typeof(Base64FileDataResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportBieuMauCanBoCommand(ExportBieuMauRequest request)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_EXPORT_BIEU_MAU_DANG_KY_XET_TUYEN);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_EXPORT_BIEU_MAU_DANG_KY_XET_TUYEN;

                var dataResult = await _mediator.Send(new ExportBieuMauThiSinhDangKyXetTuyenCommand(request, u.SystemLog));

                var fullFilePath = Path.Combine(_webHostEnvironment.WebRootPath, dataResult.FilePath);

                var memoryStream = AdmissionOfficeUtils.GenDocxFromDocxAndMetaDataHsns(fullFilePath, dataResult);

                return new Base64FileDataResponse
                {
                    FileData = Convert.ToBase64String(memoryStream.ToArray()),
                    FileName = dataResult.FileName
                };
            });
        }

        /// <summary>
        /// Export biểu mẫu cao học
        /// </summary>
        /// <param name="request">Thông tin export</param>
        /// <returns></returns>
        [HttpPost, Route("export-bieu-mau-cao-hoc")]
        [ProducesResponseType(typeof(Base64FileDataResponse), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportBieuMauCaoHocCommand(ExportBieuMauRequest request)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(AdmissionLogConstants.ACTION_EXPORT_BIEU_MAU_DANG_KY_XET_TUYEN);
                u.SystemLog.ActionName = AdmissionLogConstants.ACTION_EXPORT_BIEU_MAU_DANG_KY_XET_TUYEN;

                var dataResult = await _mediator.Send(new ExportBieuMauCaoHocCommand(request, u.SystemLog));

                var fullFilePath = Path.Combine(_webHostEnvironment.WebRootPath, dataResult.FilePath);

                var memoryStream = AdmissionOfficeUtils.GenDocxFromDocxAndMetaDataHsns(fullFilePath, dataResult);

                return new Base64FileDataResponse
                {
                    FileData = Convert.ToBase64String(memoryStream.ToArray()),
                    FileName = dataResult.FileName
                };
            });
        }
    }
}
