using Core.API.Shared;
using Admissions.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Admissions.API.Controller
{
    /// <summary>
    /// Module môn xét tuyển
    /// </summary>
    [ApiController]
    [Route("admission-portal/v1/tuyen-sinh-mon-xet-tuyen")]
    [ApiExplorerSettings(GroupName = "20. Môn xét tuyển")]
    [Authorize]
    public class TuyenSinhMonXetTuyenController : ApiControllerBase
    {
        public TuyenSinhMonXetTuyenController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }
        /// <summary>
        /// <PERSON><PERSON><PERSON> danh sách môn xét tuyển cho combobox
        /// </summary> 
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <returns>Danh sách môn xét tuyển</returns> 
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TuyenSinhMonXetTuyenSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetForCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetComboboxTuyenSinhMonXetTuyenQuery(count, ts)));
        }
    }
}
