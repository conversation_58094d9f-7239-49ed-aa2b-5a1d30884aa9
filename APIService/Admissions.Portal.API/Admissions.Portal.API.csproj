<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>425b4f5f-9a2d-4859-8fc2-b0e291718a67</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Admissions.Business\Admissions.Business.csproj" />
    <ProjectReference Include="..\..\uni-system-api\APIService\Core.API.Shared\Core.API.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.2" />
  </ItemGroup>
</Project>