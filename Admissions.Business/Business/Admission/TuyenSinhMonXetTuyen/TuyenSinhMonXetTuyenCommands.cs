using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhMonXetTuyenCommands
    {
        public class CreateTuyenSinhMonXetTuyenCommand : IRequest<Unit>
        {
            public CreateTuyenSinhMonXetTuyenModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Thêm mới môn xét tuyển
            /// </summary>
            /// <param name="model">Thông tin môn xét tuyển cần thêm mới</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public CreateTuyenSinhMonXetTuyenCommand(CreateTuyenSinhMonXetTuyenModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<CreateTuyenSinhMonXetTuyenCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(CreateTuyenSinhMonXetTuyenCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create {TuyenSinhMonXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateTuyenSinhMonXetTuyenModel, SvTuyenSinhMonXetTuyen>(model);

                    var checkCode = await _dataContext.SvTuyenSinhMonXetTuyens.AnyAsync(x => x.MaMonXetTuyen == entity.MaMonXetTuyen);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["TuyenSinhMonXetTuyen.MaMonXetTuyen.Existed", entity.MaMonXetTuyen]}");
                    }

                    await _dataContext.SvTuyenSinhMonXetTuyens.AddAsync(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Create {TuyenSinhMonXetTuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới môn xét tuyển: {entity.TenMonXetTuyen}",
                        ObjectCode = TuyenSinhMonXetTuyenConstant.CachePrefix,
                        ObjectId = entity.IdMonXetTuyen.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhMonXetTuyenConstant.BuildCacheKey());

                    return Unit.Value;
                }
            }
        }

        public class UpdateTuyenSinhMonXetTuyenCommand : IRequest<Unit>
        {
            public UpdateTuyenSinhMonXetTuyenModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Cập nhật môn xét tuyển
            /// </summary>
            /// <param name="model">Thông tin môn xét tuyển cần cập nhật</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public UpdateTuyenSinhMonXetTuyenCommand(UpdateTuyenSinhMonXetTuyenModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<UpdateTuyenSinhMonXetTuyenCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(UpdateTuyenSinhMonXetTuyenCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Update {TuyenSinhMonXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                    var checkCode = await _dataContext.SvTuyenSinhMonXetTuyens.AnyAsync(x => x.MaMonXetTuyen == model.MaMonXetTuyen && x.IdMonXetTuyen != model.IdMonXetTuyen);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["TuyenSinhMonXetTuyen.MaMonXetTuyen.Existed", model.MaMonXetTuyen]}");
                    }

                    var entity = await _dataContext.SvTuyenSinhMonXetTuyens.FirstOrDefaultAsync(x => x.IdMonXetTuyen == model.IdMonXetTuyen);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }
                    Log.Information($"Before Update {TuyenSinhMonXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    model.UpdateEntity(entity);

                    _dataContext.SvTuyenSinhMonXetTuyens.Update(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"After Update {TuyenSinhMonXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhMonXetTuyenConstant.BuildCacheKey(entity.IdMonXetTuyen.ToString()));
                    _cacheService.Remove(TuyenSinhMonXetTuyenConstant.BuildCacheKey());

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Cập nhập môn xét tuyển: {entity.TenMonXetTuyen}",
                        ObjectCode = TuyenSinhMonXetTuyenConstant.CachePrefix,
                        ObjectId = entity.IdMonXetTuyen.ToString()
                    });

                    return Unit.Value;
                }
            }
        }

        public class DeleteTuyenSinhMonXetTuyenCommand : IRequest<Unit>
        {
            public int Id { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Xóa môn xét tuyển 
            /// </summary>
            /// <param name="id">Id môn xét tuyển cần xóa</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public DeleteTuyenSinhMonXetTuyenCommand(int id, SystemLogModel systemLog)
            {
                Id = id;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<DeleteTuyenSinhMonXetTuyenCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(DeleteTuyenSinhMonXetTuyenCommand request, CancellationToken cancellationToken)
                {
                    var id = request.Id;
                    var systemLog = request.SystemLog;
                    Log.Information($"Delete {TuyenSinhMonXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(id)}");

                    // Check dữ liệu đã dùng

                    var entity = await _dataContext.SvTuyenSinhMonXetTuyens.FindAsync(id);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }
                    _dataContext.SvTuyenSinhMonXetTuyens.Remove(entity);
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa môn xét tuyển: {entity.TenMonXetTuyen}",
                        ObjectCode = TuyenSinhMonXetTuyenConstant.CachePrefix,
                        ObjectId = entity.IdMonXetTuyen.ToString()
                    });

                    Log.Information($"Delete {TuyenSinhMonXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(id)} success");

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhMonXetTuyenConstant.BuildCacheKey(entity.IdMonXetTuyen.ToString()));
                    await _dataContext.SaveChangesAsync();

                    _cacheService.Remove(TuyenSinhMonXetTuyenConstant.BuildCacheKey());

                    return Unit.Value;
                }
            }
        }
    }
}
