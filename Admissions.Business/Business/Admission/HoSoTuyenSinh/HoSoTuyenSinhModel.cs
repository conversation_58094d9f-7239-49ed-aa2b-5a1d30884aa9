using System;
using System.ComponentModel.DataAnnotations;
using Admissions.Data;

namespace Admissions.Business
{
    public class CreateHoSoTuyenSinhModel
    {
        [Required]
        [MaxLength(50)]
        public string HoTen { get; set; }

        [MaxLength(50)]
        public string DienThoaiCa<PERSON>han { get; set; }

        public string CMND { get; set; }

        [MaxLength(50)]
        public string Email { get; set; }

        public string MatKhauThiSinh { get; set; }
    }

    public class CreateHoSoTuyenSinhDetailModel
    {
        public int IdHoSo { get; set; }

        [MaxLength(20)]
        public string MaHoSo { get; set; }

        [MaxLength(50)]
        public string HoTen { get; set; }

        public DateTime NgaySinh { get; set; }

        public int IdGioiTinh { get; set; }

        public int IdQuocTich { get; set; }

        public int IdDanToc { get; set; }

        [MaxLength(5)]
        public string IdTinhNs { get; set; }

        [MaxLength(20)]
        public string CMND { get; set; }

        public DateTime NgayCapCMND { get; set; }

        public string IdNoiCapCMND { get; set; }

        [MaxLength(5)]
        public string IdTinhTt { get; set; }

        [MaxLength(5)]
        public string IdHuyenTt { get; set; }

        [MaxLength(5)]
        public string XaPhuongTt { get; set; }

        [MaxLength(200)]
        public string DiaChiTt { get; set; }

        public bool HoKhauTtKv1 { get; set; }

        public bool HoKhauTtXaKhoKhan { get; set; }

        [MaxLength(50)]
        public string DienThoaiCaNhan { get; set; }

        [MaxLength(50)]
        public string Email { get; set; }

        [MaxLength(200)]
        public string DiaChiBaoTin { get; set; }

        [MaxLength(50)]
        public string HoTenCha { get; set; }

        public int? NamSinhCha { get; set; }

        [MaxLength(50)]
        public string SoDienThoaiBo { get; set; }

        [MaxLength(50)]
        public string HoTenMe { get; set; }

        public int? NamSinhMe { get; set; }

        [MaxLength(50)]
        public string SoDienThoaiMe { get; set; }

        public int IdDoiTuongTS { get; set; }

        public int IdKhuVucTuyenSinh { get; set; }

        public int IdTruongTHPTLop10 { get; set; }

        public int IdTruongTHPTLop11 { get; set; }

        public int IdTruongTHPTLop12 { get; set; }

        public int IdXepLoaiHocTapLop12 { get; set; }

        public int IdXepLoaiHanhKiemLop12 { get; set; }

        public string NamTotNghiep { get; set; }

        [MaxLength(20)]
        public string SBD { get; set; }

        public string MatKhauThiSinh { get; set; }

        public DateTime CreateDate { get; set; }

        [MaxLength(20)]
        public string CreateUserName { get; set; }

        public DateTime? ModifyDate { get; set; }

        [MaxLength(20)]
        public string ModifyUserName { get; set; }
    }

    public class UserInforModel
    {
        public int IdHoSo { get; set; }
        public string CMND { get; set; }
        public string HoTen { get; set; }
        public string DienThoaiCaNhan { get; set; }
        public string Email { get; set; }

    }

    public class ForgotPasswordHoSoTuyenSinhModel
    {
        [Required]
        public string CMND { get; set; }

        [Required]
        [MaxLength(50)]
        public string Email { get; set; }

        [Required]
        public string RecaptchaToken { get; set; }

        public void UpdatePassword(svHoSoTuyenSinh input, string newPassword)
        {
            input.MatKhauThiSinh = newPassword;
            input.ModifyDate = DateTime.Now;
            input.ModifyUserName = this.CMND;
        }
    }

    public class HoSoTuyenSinhBaseModel
    {
        public int IdHoSo { get; set; }
        public string MaHoSo { get; set; }
        public string HoTen { get; set; }
        public DateTime NgaySinh { get; set; }
        public int? IdGioiTinh { get; set; }
        public int? IdQuocTich { get; set; }
        public int? IdDanToc { get; set; }
        public string IdTinhNs { get; set; }
        public string CMND { get; set; }
        public DateTime? NgayCapCMND { get; set; }
        public string IdNoiCapCMND { get; set; }
        public string IdTinhTt { get; set; }
        public string IdHuyenTt { get; set; }
        public string XaPhuongTt { get; set; }
        public string DiaChiTt { get; set; }
        public bool? HoKhauTtKv1 { get; set; }
        public bool? HoKhauTtXaKhoKhan { get; set; }
        public string DienThoaiCaNhan { get; set; }
        public string Email { get; set; }
        public string DiaChiBaoTin { get; set; }
        public string HoTenCha { get; set; }
        public int? NamSinhCha { get; set; }
        public string SoDienThoaiBo { get; set; }
        public string HoTenMe { get; set; }
        public int? NamSinhMe { get; set; }
        public string SoDienThoaiMe { get; set; }
        public int? IdDoiTuongTS { get; set; }
        public int? IdKhuVucTuyenSinh { get; set; }
        public int? IdTruongTHPTLop10 { get; set; }
        public int? IdTruongTHPTLop11 { get; set; }
        public int? IdTruongTHPTLop12 { get; set; }
        public int? IdXepLoaiHocTapLop12 { get; set; }
        public int? IdXepLoaiHanhKiemLop12 { get; set; }
        public string NamTotNghiep { get; set; }
        public string SBD { get; set; }
        public string ChucVuDonViCongTac { get; set; }
        public string DonVi { get; set; }
        public string DienThoaiNR { get; set; }
        public string DienThoaiCQ { get; set; }
        public string NgheNghiep { get; set; }
        public string NamBatDauCongTac { get; set; }
        public int? LoaiHopDongHienTai { get; set; }
        public int? DoiTuongDuThi { get; set; }
        public string LinhVucChuyenMon { get; set; }
        public int? ThamNienNgheNghiep { get; set; }
        public int? SoLuongBaiBao { get; set; }
        public string TenTruongBang1 { get; set; }
        public string NamTotNghiepBang1 { get; set; }
        public int? IdNganhBang1 { get; set; }
        public string HinhThucDaoTaoBang1 { get; set; }
        public string TenTruongBang2 { get; set; }
        public string NamTotNghiepBang2 { get; set; }
        public int? IdNganhBang2 { get; set; }
        public string HinhThucDaoTaoBang2 { get; set; }
        public string LoaiTotNghiepBang1 { get; set; }
        public string LoaiTotNghiepBang2 { get; set; }
        public string TenTruongThacSi { get; set; }
        public string NamTotNghiepThacSi { get; set; }
        public int? IdNganhThacSi { get; set; }
        public string HinhThucDaoTaoThacSi { get; set; }
        public string TrinhDoNgoaiNgu { get; set; }
        public int? BoTucKienThuc { get; set; }
        public int? IdXepLoaiHanhKiemLop10 { get; set; }
        public int? IdXepLoaiHanhKiemLop11 { get; set; }
        public string SoDienThoaiNguoiTiepNhan { get; set; }
        public string NguoiTiepNhan { get; set; }
        public string GhiChuTuyenSinh { get; set; }
        public string EmailBo { get; set; }
        public string DienThoaiCQBo { get; set; }
        public string DiaChiLienHeBo { get; set; }
        public string EmailMe { get; set; }
        public string DienThoaiCQMe { get; set; }
        public string DiaChiLienHeMe { get; set; }
        public string NguoiThamKhao1HoTen { get; set; }
        public string NguoiThamKhao1DienThoai { get; set; }
        public string NguoiThamKhao1Email { get; set; }
        public string NguoiThamKhao1NgheNghiep { get; set; }
        public string NguoiThamKhao1NoiCongTac { get; set; }
        public string NguoiThamKhao1DiaChi { get; set; }
        public string NguoiThamKhao1QuanHe { get; set; }
        public string NguoiThamKhao2HoTen { get; set; }
        public string NguoiThamKhao2DienThoai { get; set; }
        public string NguoiThamKhao2Email { get; set; }
        public string NguoiThamKhao2NgheNghiep { get; set; }
        public string NguoiThamKhao2NoiCongTac { get; set; }
        public string NguoiThamKhao2DiaChi { get; set; }
        public string NguoiThamKhao2QuanHe { get; set; }
        public string HoTenNguoiNhan { get; set; }
        public string SoDienThoaiNguoiNhan { get; set; }
        public int? IdLoaiDoiTuong { get; set; }
        public string IdAnhThiSinh { get; set; }
        public string NgheNghiepCha { get; set; }
        public string NgheNghiepMe { get; set; }
    }

    public class HoSoTuyenSinhDetailModel : HoSoTuyenSinhBaseModel
    {
        public string IdTinhLop10 { get; set; } 
        public string IdTinhLop11 { get; set; } 
        public string IdTinhLop12 { get; set; }
    }
}
