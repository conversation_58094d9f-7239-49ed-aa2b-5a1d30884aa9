using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhToHopNganhXetTuyenBaseModel
    {
        public int IdTuyenSinhToHopNganhXetTuyen { get; set; }
        public int IdHe { get; set; }
        public int IdNganh { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public int IdToHopXetTuyen { get; set; }
    }
    public class TuyenSinhToHopNganhXetTuyenModel : TuyenSinhToHopNganhXetTuyenBaseModel 
    {
        
    }
    public class TuyenSinhToHopNganhXetTuyenDetailModel : TuyenSinhToHopNganhXetTuyenBaseModel
    {
        public string TenHe { get; set; }
        public string TenNganh { get; set; }
        public string TenPhuongThucXetTuyen { get; set; }
        public string TenToHopXetTuyen { get; set; }
    }
    public class CreateTuyenSinhToHopNganhXetTuyenModel 
    {
        public int IdHe { get; set; }
        public int IdNganh { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public int IdToHopXetTuyen { get; set; }
    }
    public class UpdateTuyenSinhToHopNganhXetTuyenModel : CreateTuyenSinhToHopNganhXetTuyenModel
    {
        public void UpdateEntity(SvTuyenSinhToHopNganhXetTuyen input) 
        {
            input.IdToHopXetTuyen = this.IdToHopXetTuyen;
            input.IdPhuongThucXetTuyen = this.IdPhuongThucXetTuyen;
        }
    }
    public class TuyenSinhToHopNganhXetTuyenFilterModel : BaseQueryFilterModel
    {
        public int IdHe { get; set; }   
        public int IdNganh { get; set; }
        public TuyenSinhToHopNganhXetTuyenFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdTuyenSinhToHopNganhXetTuyen";
        }
    }
    public class TuyenSinhToHopNganhXetTuyenSelectModel: TuyenSinhToHopNganhXetTuyenBaseModel
    {
        public string TenPhuongThucXetTuyen { get; set; }
    }
}
