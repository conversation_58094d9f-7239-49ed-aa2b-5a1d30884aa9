using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using MongoDB.Driver.Linq;

namespace Admissions.Business
{
    public class GetFilterTuyenSinhToHopNganhXetTuyenQuery : IRequest<PaginationList<TuyenSinhToHopNganhXetTuyenDetailModel>>
    {
        public TuyenSinhToHopNganhXetTuyenFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách đối tượng xét theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTuyenSinhToHopNganhXetTuyenQuery(TuyenSinhToHopNganhXetTuyenFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTuyenSinhToHopNganhXetTuyenQuery, PaginationList<TuyenSinhToHopNganhXetTuyenDetailModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TuyenSinhToHopNganhXetTuyenDetailModel>> Handle(GetFilterTuyenSinhToHopNganhXetTuyenQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvTuyenSinhToHopNganhXetTuyens
                            join th in _dataContext.SvTuyenSinhToHopXetTuyens
                            on dt.IdToHopXetTuyen equals th.IdToHopXetTuyen
                            join h in _dataContext.SvHes
                            on dt.IdHe equals h.IdHe
                            join pt in _dataContext.SvTuyenSinhPhuongThucXetTuyens
                            on dt.IdPhuongThucXetTuyen equals pt.IdPhuongThucXetTuyen
                            join n in _dataContext.SvNganhs
                            on dt.IdNganh equals n.IdNganh
                            select new TuyenSinhToHopNganhXetTuyenDetailModel
                            {
                                IdTuyenSinhToHopNganhXetTuyen = dt.IdTuyenSinhToHopNganhXetTuyen, 
                                IdHe = dt.IdHe,
                                IdNganh = dt.IdNganh,
                                IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                                IdToHopXetTuyen = dt.IdToHopXetTuyen,
                                TenHe = h.TenHe,
                                TenNganh = n.TenNganh, 
                                TenPhuongThucXetTuyen = pt.TenPhuongThucXetTuyen,
                                TenToHopXetTuyen = th.TenToHopXetTuyen
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenNganh.ToLower().Contains(ts) || x.TenPhuongThucXetTuyen.ToLower().Contains(ts) || x.TenHe.ToLower().Contains(ts) || x.TenToHopXetTuyen.ToLower().Contains(ts));
                }
                if(filter.IdHe > 0)
                {
                    data = data.Where(x => x.IdHe == filter.IdHe);
                }
                if (filter.IdNganh > 0)
                {
                    data = data.Where(x => x.IdNganh == filter.IdNganh);
                }
                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<TuyenSinhToHopNganhXetTuyenDetailModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetTuyenSinhToHopNganhXetTuyenByIdQuery : IRequest<TuyenSinhToHopNganhXetTuyenModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin đối tượng xét tuyển theo id
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        public GetTuyenSinhToHopNganhXetTuyenByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTuyenSinhToHopNganhXetTuyenByIdQuery, TuyenSinhToHopNganhXetTuyenModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TuyenSinhToHopNganhXetTuyenModel> Handle(GetTuyenSinhToHopNganhXetTuyenByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = TuyenSinhToHopNganhXetTuyenConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvTuyenSinhToHopNganhXetTuyens.FirstOrDefaultAsync(x => x.IdTuyenSinhToHopNganhXetTuyen == id);

                    return AutoMapperUtils.AutoMap<SvTuyenSinhToHopNganhXetTuyen, TuyenSinhToHopNganhXetTuyenModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetComboboxToHopNganhXetTuyenQuery : IRequest<List<TuyenSinhToHopNganhXetTuyenSelectModel>>
    {
        public int Count { get; set; }
        /// <summary>
        /// Lấy danh sách tỉnh cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxToHopNganhXetTuyenQuery(int count = 0)
        {
            this.Count = count;
        }

        public class Handler : IRequestHandler<GetComboboxToHopNganhXetTuyenQuery, List<TuyenSinhToHopNganhXetTuyenSelectModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TuyenSinhToHopNganhXetTuyenSelectModel>> Handle(GetComboboxToHopNganhXetTuyenQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;

                //build cache tổ hợp ngành
                string cacheKey = TuyenSinhToHopNganhXetTuyenConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvTuyenSinhToHopNganhXetTuyens.OrderBy(x => x.IdTuyenSinhToHopNganhXetTuyen)
                                select new TuyenSinhToHopNganhXetTuyenSelectModel()
                                {
                                    IdHe = dt.IdHe,
                                    IdNganh = dt.IdNganh,
                                    IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                                    IdToHopXetTuyen = dt.IdToHopXetTuyen
                                });
                    return await data.ToListAsync();
                });

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetComboboxTuyenSinhToHopNganhXetTuyenQuery : IRequest<List<TuyenSinhToHopNganhXetTuyenSelectModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }
        public int IdNganh { get; set; }
        public int IdHe {  get; set; }
        /// <summary>
        /// Lấy danh sách tỉnh cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxTuyenSinhToHopNganhXetTuyenQuery(int count = 0, string textSearch = "", int idNganh = 0, int idHe = 0)
        {
            this.Count = count;
            this.TextSearch = textSearch;
            IdNganh = idNganh;
            IdHe = idHe;
        }

        public class Handler : IRequestHandler<GetComboboxTuyenSinhToHopNganhXetTuyenQuery, List<TuyenSinhToHopNganhXetTuyenSelectModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<List<TuyenSinhToHopNganhXetTuyenSelectModel>> Handle(GetComboboxTuyenSinhToHopNganhXetTuyenQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                var idNganh = request.IdNganh;
                var idHe = request.IdHe;

                var lstToHopNganh = await _mediator.Send(new GetComboboxToHopNganhXetTuyenQuery());

                var lstPhuongThucXetTuyen = await _mediator.Send(new GetComboboxTuyenSinhPhuongThucXetTuyenQuery());
                
                var list = (from item in lstToHopNganh
                            join pt in lstPhuongThucXetTuyen on item.IdPhuongThucXetTuyen equals pt.IdPhuongThucXetTuyen
                            group new { item, pt } by new { item.IdHe, item.IdNganh, item.IdPhuongThucXetTuyen } into grouped
                            select new TuyenSinhToHopNganhXetTuyenSelectModel()
                            {
                                IdHe = grouped.Key.IdHe,
                                IdNganh = grouped.Key.IdNganh,
                                IdPhuongThucXetTuyen = grouped.Key.IdPhuongThucXetTuyen,
                                TenPhuongThucXetTuyen = grouped.Max(x => x.pt.TenPhuongThucXetTuyen)
                            }).ToList();
                
                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenPhuongThucXetTuyen.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                if (idNganh > 0)
                {
                    list = list.Where(x => x.IdNganh == idNganh).ToList();
                }

                if (idHe > 0)
                {
                    list = list.Where(x => x.IdHe == idHe).ToList();
                }

                return list;
            }
        }
    }
}
