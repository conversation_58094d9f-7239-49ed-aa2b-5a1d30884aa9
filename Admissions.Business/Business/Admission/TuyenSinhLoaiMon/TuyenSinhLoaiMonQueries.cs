using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhLoaiMonQueries
    {
        public class GetTuyenSinhLoaiMonByIdQuery : IRequest<TuyenSinhLoaiMonModel>
        {
            public int Id { get; set; }

            /// <summary>
            /// Lấy thông tin loại môn theo id
            /// </summary>
            /// <param name="id">Id loại môn</param>
            public GetTuyenSinhLoaiMonByIdQuery(int id)
            {
                Id = id;
            }

            public class Handler : IRequestHandler<GetTuyenSinhLoaiMonByIdQuery, TuyenSinhLoaiMonModel>
            {
                private readonly AdmissionReadDataContext _dataContext;
                private readonly ICacheService _cacheService;

                public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                }

                public async Task<TuyenSinhLoaiMonModel> Handle(GetTuyenSinhLoaiMonByIdQuery request, CancellationToken cancellationToken)
                {
                    var id = request.Id;
                    string cacheKey = TuyenSinhLoaiMonConstant.BuildCacheKey(id.ToString());
                    var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                    {
                        var entity = await _dataContext.SvTuyenSinhLoaiMons.FirstOrDefaultAsync(x => x.IdLoaiMon == id);

                        return AutoMapperUtils.AutoMap<SvTuyenSinhLoaiMon, TuyenSinhLoaiMonModel>(entity);
                    });
                    return item;
                }
            }
        }

        public class GetFilterTuyenSinhLoaiMonQuery : IRequest<PaginationList<TuyenSinhLoaiMonBaseModel>>
        {
            public TuyenSinhLoaiMonQueryFilterModel Filter { get; set; }

            /// <summary>
            /// Lấy danh sách loại môn theo điều kiện lọc
            /// </summary>
            /// <param name="filter">Thông tin lọc</param>
            public GetFilterTuyenSinhLoaiMonQuery(TuyenSinhLoaiMonQueryFilterModel filter)
            {
                Filter = filter;
            }

            public class Handler : IRequestHandler<GetFilterTuyenSinhLoaiMonQuery, PaginationList<TuyenSinhLoaiMonBaseModel>>
            {
                private readonly AdmissionReadDataContext _dataContext;

                public Handler(AdmissionReadDataContext dataContext)
                {
                    _dataContext = dataContext;
                }

                public async Task<PaginationList<TuyenSinhLoaiMonBaseModel>> Handle(GetFilterTuyenSinhLoaiMonQuery request, CancellationToken cancellationToken)
                {
                    var filter = request.Filter;

                    var data = (from dt in _dataContext.SvTuyenSinhLoaiMons
                                select new TuyenSinhLoaiMonBaseModel()
                                {
                                    IdLoaiMon = dt.IdLoaiMon,
                                    MaLoaiMon = dt.MaLoaiMon,
                                    TenLoaiMon = dt.TenLoaiMon
                                });

                    if (!string.IsNullOrEmpty(filter.TextSearch))
                    {
                        string ts = filter.TextSearch.Trim().ToLower();
                        data = data.Where(x => x.TenLoaiMon.ToLower().Contains(ts));
                    }

                    if (!string.IsNullOrEmpty(filter.MaLoaiMon))
                    {
                        string ts = filter.MaLoaiMon.Trim().ToLower();
                        data = data.Where(x => x.MaLoaiMon.ToLower().Contains(ts));
                    }

                    data = data.OrderByField(filter.PropertyName, filter.Ascending);

                    int totalCount = data.Count();

                    // Pagination
                    if (filter.PageSize <= 0)
                    {
                        filter.PageSize = QueryFilter.DefaultPageSize;
                    }

                    //Calculate nunber of rows to skip on pagesize
                    int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                    if (excludedRows <= 0)
                    {
                        excludedRows = 0;
                    }

                    // Query
                    data = data.Skip(excludedRows).Take(filter.PageSize);
                    int dataCount = data.Count();

                    var listData = await data.ToListAsync();

                    return new PaginationList<TuyenSinhLoaiMonBaseModel>()
                    {
                        DataCount = dataCount,
                        TotalCount = totalCount,
                        PageNumber = filter.PageNumber,
                        PageSize = filter.PageSize,
                        Data = listData
                    };
                }
            }
        }

        public class GetComboboxTuyenSinhLoaiMonQuery : IRequest<List<TuyenSinhLoaiMonSelectItemModel>>
        {
            public int Count { get; set; }
            public string TextSearch { get; set; }

            /// <summary>
            /// Lấy danh sách loại môn cho combobox
            /// </summary>
            /// <param name="count">Số lượng bản ghi cần lấy ra</param>
            /// <param name="textSearch">Từ khóa tìm kiếm</param>
            public GetComboboxTuyenSinhLoaiMonQuery(int count = 0, string textSearch = "")
            {
                this.Count = count;
                this.TextSearch = textSearch;
            }

            public class Handler : IRequestHandler<GetComboboxTuyenSinhLoaiMonQuery, List<TuyenSinhLoaiMonSelectItemModel>>
            {
                private readonly AdmissionReadDataContext _dataContext;
                private readonly ICacheService _cacheService;

                public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                }

                public async Task<List<TuyenSinhLoaiMonSelectItemModel>> Handle(GetComboboxTuyenSinhLoaiMonQuery request, CancellationToken cancellationToken)
                {
                    var count = request.Count;
                    var textSearch = request.TextSearch;

                    string cacheKey = TuyenSinhLoaiMonConstant.BuildCacheKey();
                    var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                    {
                        var data = (from dt in _dataContext.SvTuyenSinhLoaiMons
                                    select new TuyenSinhLoaiMonSelectItemModel()
                                    {
                                        IdLoaiMon = dt.IdLoaiMon,
                                        MaLoaiMon = dt.MaLoaiMon,
                                        TenLoaiMon = dt.TenLoaiMon
                                    });

                        return await data.ToListAsync();
                    });

                    if (!string.IsNullOrEmpty(textSearch))
                    {
                        textSearch = textSearch.ToLower().Trim();
                        list = list.Where(x => x.TenLoaiMon.ToLower().Contains(textSearch)).ToList();
                    }


                    if (count > 0)
                    {
                        list = list.Take(count).ToList();
                    }

                    return list;
                }
            }
        }
    }
}
