using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class CreateTruongTHPTCommand : IRequest<Unit>
    {
        public CreateTruongTHPTModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới trường THPT
        /// </summary>
        /// <param name="model">Thông tin trường THPT cần thêm mới</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public CreateTruongTHPTCommand(CreateTruongTHPTModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateTruongTHPTCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateTruongTHPTCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {TruongTHPTConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateTruongTHPTModel, SvTruongTHPT>(model);

                var checkCode = await _dataContext.SvTruongTHPTs.AnyAsync(x => x.MaTruongTHPT == entity.MaTruongTHPT);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["TruongTHPT.MaTruongTHPT.Existed", entity.MaTruongTHPT]}");
                }

                var tinh = _dataContext.SvTinhs.FirstOrDefault(x => x.IdTinh == entity.MaTinhTHPT);
                if (tinh == null)
                {
                    throw new ArgumentException($"{_localizer["MaTinhTHPT.NotExisted", entity.MaTinhTHPT]}");
                }

                var huyen = _dataContext.SvHuyens.FirstOrDefault(x => x.IdHuyen == entity.MaHuyenTHPT);
                if (tinh == null)
                {
                    throw new ArgumentException($"{_localizer["MaHuyenTHPT.NotExisted", entity.MaHuyenTHPT]}");
                }

                entity.TenTinhTHPT = tinh.TenTinh;
                entity.TenHuyenTHPT = huyen.TenHuyen;

                await _dataContext.SvTruongTHPTs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {TruongTHPTConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới trường THPT: {entity.TenTruongTHPT}",
                    ObjectCode = TruongTHPTConstant.CachePrefix,
                    ObjectId = entity.IdTruongTHPT.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TruongTHPTConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }

    public class UpdateTruongTHPTCommand : IRequest<Unit>
    {
        public UpdateTruongTHPTModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật trường THPT
        /// </summary>
        /// <param name="model">Thông tin trường THPT cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateTruongTHPTCommand(UpdateTruongTHPTModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateTruongTHPTCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdateTruongTHPTCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Update {TruongTHPTConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var checkCode = await _dataContext.SvTruongTHPTs.AnyAsync(x => x.MaTruongTHPT == model.MaTruongTHPT && x.IdTruongTHPT != model.IdTruongTHPT);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["TruongTHPT.MaTruongTHPT.Existed", model.MaTruongTHPT]}");
                }

                var entity = await _dataContext.SvTruongTHPTs.FirstOrDefaultAsync(x => x.IdTruongTHPT == model.IdTruongTHPT);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {TruongTHPTConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                var tinh = _dataContext.SvTinhs.FirstOrDefault(x => x.IdTinh == entity.MaTinhTHPT);
                if (tinh == null)
                {
                    entity.TenTinhTHPT = tinh.TenTinh;
                }

                var huyen = _dataContext.SvHuyens.FirstOrDefault(x => x.IdHuyen == entity.MaHuyenTHPT);
                if (huyen == null)
                {
                    entity.TenHuyenTHPT = huyen.TenHuyen;
                }

                model.UpdateEntity(entity);

                _dataContext.SvTruongTHPTs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {TruongTHPTConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(TruongTHPTConstant.BuildCacheKey(entity.IdTruongTHPT.ToString()));
                _cacheService.Remove(TruongTHPTConstant.BuildCacheKey());

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập trường THPT: {entity.TenTruongTHPT}",
                    ObjectCode = TruongTHPTConstant.CachePrefix,
                    ObjectId = entity.IdTruongTHPT.ToString()
                });

                return Unit.Value;
            }
        }
    }

    public class DeleteTruongTHPTCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa trường THPT 
        /// </summary>
        /// <param name="id">Id trường THPT cần xóa</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public DeleteTruongTHPTCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteTruongTHPTCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteTruongTHPTCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {TruongTHPTConstant.CachePrefix}: {JsonSerializer.Serialize(id)}");

                // Check dữ liệu đã dùng

                var entity = await _dataContext.SvTruongTHPTs.FindAsync(id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.SvTruongTHPTs.Remove(entity);
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa trường THPT: {entity.TenTruongTHPT}",
                    ObjectCode = TruongTHPTConstant.CachePrefix,
                    ObjectId = entity.IdTruongTHPT.ToString()
                });

                Log.Information($"Delete {TruongTHPTConstant.CachePrefix}: {JsonSerializer.Serialize(id)} success");

                //Xóa cache
                _cacheService.Remove(TruongTHPTConstant.BuildCacheKey(entity.IdTruongTHPT.ToString()));
                await _dataContext.SaveChangesAsync();

                _cacheService.Remove(TruongTHPTConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}