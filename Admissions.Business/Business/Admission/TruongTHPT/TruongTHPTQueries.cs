using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class GetTruongTHPTByIdQuery : IRequest<TruongTHPTModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin trường THPT theo id
        /// </summary>
        /// <param name="id">Id trường THPT</param>
        public GetTruongTHPTByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTruongTHPTByIdQuery, TruongTHPTModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TruongTHPTModel> Handle(GetTruongTHPTByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = TruongTHPTConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvTruongTHPTs.FirstOrDefaultAsync(x => x.IdTruongTHPT == id);

                    return AutoMapperUtils.AutoMap<SvTruongTHPT, TruongTHPTModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetFilterTruongTHPTQuery : IRequest<PaginationList<TruongTHPTModel>>
    {
        public TruongTHPTQueryFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách trường THPT theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTruongTHPTQuery(TruongTHPTQueryFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTruongTHPTQuery, PaginationList<TruongTHPTModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TruongTHPTModel>> Handle(GetFilterTruongTHPTQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvTruongTHPTs
                            join kv in _dataContext.SvKhuVucs on dt.IdKVTHPT equals kv.IdKv into truongs
                            from kv in truongs.DefaultIfEmpty()
                            select new TruongTHPTModel()
                            {
                                IdTruongTHPT = dt.IdTruongTHPT,
                                MaTruongTHPT = dt.MaTruongTHPT,
                                TenTruongTHPT = dt.TenTruongTHPT,
                                DiaChiTruongTHPT = dt.DiaChiTruongTHPT,
                                MaTinhTHPT = dt.MaTinhTHPT,
                                TenTinhTHPT = dt.TenTinhTHPT,
                                MaHuyenTHPT = dt.MaHuyenTHPT,
                                TenHuyenTHPT = dt.TenHuyenTHPT,
                                IdKVTHPT = (int)dt.IdKVTHPT,
                                TruongDTNT = dt.TruongDTNT,
                                TenKv = kv.TenKv
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenTruongTHPT.ToLower().Contains(ts));
                }

                if (!string.IsNullOrEmpty(filter.MaTruongTHPT))
                {
                    string ts = filter.MaTruongTHPT.Trim().ToLower();
                    data = data.Where(x => x.MaTruongTHPT.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<TruongTHPTModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetComboboxTruongTHPTQuery : IRequest<List<TruongTHPTSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }
        public string MaTinh { get; set; }
        public string MaHuyen { get; set; }
        public int IdTruongTHPT { get; set; }

        /// <summary>
        /// Lấy danh sách trường THPT cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxTruongTHPTQuery(int count = 0, string textSearch = "", string maTinh = "", string maHuyen = "", int idTruongTHPT = 0)
        {
            this.Count = count;
            this.TextSearch = textSearch;
            this.MaTinh = maTinh;
            this.MaHuyen = maHuyen;
            this.IdTruongTHPT = idTruongTHPT;
        }

        public class Handler : IRequestHandler<GetComboboxTruongTHPTQuery, List<TruongTHPTSelectItemModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TruongTHPTSelectItemModel>> Handle(GetComboboxTruongTHPTQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                var maTinh = request.MaTinh;
                var maHuyen = request.MaHuyen;
                var idTruongTHPT = request.IdTruongTHPT;

                string cacheKey = TruongTHPTConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvTruongTHPTs
                                select new TruongTHPTSelectItemModel()
                                {
                                    IdTruongTHPT = dt.IdTruongTHPT,
                                    IdKVTHPT = dt.IdKVTHPT,
                                    MaTruongTHPT = dt.MaTruongTHPT,
                                    TenTruongTHPT = dt.TenTruongTHPT,
                                    MaHuyenTHPT = dt.MaHuyenTHPT,
                                    MaTinhTHPT = dt.MaTinhTHPT
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenTruongTHPT.ToLower().Contains(textSearch)).ToList();
                }

                if (!string.IsNullOrEmpty(maTinh))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.MaTinhTHPT.ToLower().Contains(maTinh)).ToList();
                }

                if (!string.IsNullOrEmpty(maHuyen))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.MaHuyenTHPT.ToLower().Contains(maHuyen)).ToList();
                }

                if (idTruongTHPT > 0)
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.IdTruongTHPT == idTruongTHPT).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
