using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using Core.DataLog;
using System;

namespace Admissions.Business
{
    public class GetTuyenSinhDangKyXetTuyenByIdQuery : IRequest<List<TuyenSinhDangKyXetTuyenModel>>
    {
        public TuyenSinhDangKyXetTuyenFilter Keys { get; set; }

        /// <summary>
        /// Lấy thông tin đăng ký xét tuyển theo id
        /// </summary>
        /// <param name="idHoSo">id hồ sơ</param>
        /// <param name="idPhuongThucXetTuyen">id phương thức xét tuyển</param>
        public GetTuyenSinhDangKyXetTuyenByIdQuery(TuyenSinhDangKyXetTuyenFilter keys)
        {
            Keys = keys;
        }

        public class Handler : IRequestHandler<GetTuyenSinhDangKyXetTuyenByIdQuery, List<TuyenSinhDangKyXetTuyenModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TuyenSinhDangKyXetTuyenModel>> Handle(GetTuyenSinhDangKyXetTuyenByIdQuery request, CancellationToken cancellationToken)
            {
                var keys = request.Keys;

                var data = await (from dt in _dataContext.SvTuyenSinhDangKyXetTuyens
                                  join nganh in _dataContext.SvNganhs on dt.IdNganh equals nganh.IdNganh
                                  join thxt in _dataContext.SvTuyenSinhToHopXetTuyens on dt.IdToHopXetTuyen equals thxt.IdToHopXetTuyen
                                  where dt.IdHoSo == keys.IdHoSo && dt.IdPhuongThucXetTuyen == keys.IdPhuongThucXetTuyen
                                  select new TuyenSinhDangKyXetTuyenModel()
                                  {
                                      IdTuyenSinhDangKyXetTuyen = dt.IdTuyenSinhDangKyXetTuyen,
                                      IdHoSo = dt.IdHoSo,
                                      IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                                      IdNganh = dt.IdNganh,
                                      IdToHopXetTuyen = dt.IdToHopXetTuyen,
                                      ThuTuXet = dt.ThuTuXet,
                                      TenNganh = nganh.TenNganh,
                                      TenToHopXetTuyen = thxt.TenToHopXetTuyen,
                                      DangKyHocChatLuongCao = dt.DangKyHocChatLuongCao
                                  }).ToListAsync();

                return data;
            }
        }
    }

    public class TraCuuNguyenVongXetTuyenQuery : IRequest<List<TraCuuNguyenVongXetTuyenResponseModel>>
    {
        public TraCuuNguyenVongXetTuyenRequestModel Model { get; set; }

        /// <summary>
        /// Tra cứu nguyện vọng xét tuyển
        /// </summary>
        /// <param name="idHoSo">id hồ sơ</param>
        /// <param name="idPhuongThucXetTuyen">id phương thức xét tuyển</param>
        public TraCuuNguyenVongXetTuyenQuery(TraCuuNguyenVongXetTuyenRequestModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<TraCuuNguyenVongXetTuyenQuery, List<TraCuuNguyenVongXetTuyenResponseModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TraCuuNguyenVongXetTuyenResponseModel>> Handle(TraCuuNguyenVongXetTuyenQuery request, CancellationToken cancellationToken)
            {
                var model = request.Model;

                var data = await (from dt in _dataContext.SvTuyenSinhDangKyXetTuyens
                                  join nganh in _dataContext.SvNganhs on dt.IdNganh equals nganh.IdNganh
                                  join thxt in _dataContext.SvTuyenSinhToHopXetTuyens on dt.IdToHopXetTuyen equals thxt.IdToHopXetTuyen
                                  join hs in _dataContext.SvHoSoTuyenSinhs on dt.IdHoSo equals hs.IdHoSo
                                  join pt in _dataContext.SvTuyenSinhPhuongThucXetTuyens on dt.IdPhuongThucXetTuyen equals pt.IdPhuongThucXetTuyen
                                  join dkxt in _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens on new { dt.IdPhuongThucXetTuyen, dt.IdHoSo } equals new { dkxt.IdPhuongThucXetTuyen, dkxt.IdHoSo }
                                  where hs.CMND == model.CMND
                                  select new TraCuuNguyenVongXetTuyenResponseModel()
                                  {
                                      NgaySinh = hs.NgaySinh,
                                      CMND = hs.CMND,
                                      HoTen = hs.HoTen,
                                      MaNganh = nganh.MaNganh,
                                      TenPhuongThucXetTuyen = pt.TenPhuongThucXetTuyen,
                                      ThuTuXet = dt.ThuTuXet,
                                      TenNganh = nganh.TenNganh,
                                      TenToHopXetTuyen = thxt.TenToHopXetTuyen,
                                      TrungTuyen = dt.TrungTuyen,
                                      GhiChuDuyetHoSo = dkxt.GhiChuDuyetHoSo
                                  }).ToListAsync();

                return data;
            }
        }
    }
}

