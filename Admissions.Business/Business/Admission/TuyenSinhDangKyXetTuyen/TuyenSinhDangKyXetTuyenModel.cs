using Admissions.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Runtime.CompilerServices;

namespace Admissions.Business
{
    public class TuyenSinhDangKyXetTuyenBaseModel
    {
        public int IdTuyenSinhDangKyXetTuyen { get; set; }
        public int IdHoSo { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public int IdNganh { get; set; }
        public int IdToHopXetTuyen { get; set; }
        public int ThuTuXet { get; set; }
        public int TrungTuyen { get; set; }
        public bool? DangKyHocChatLuongCao { get; set; }
    }

    public class CreateTuyenSinhDangKyXetTuyenModel
    {
        [Required]
        public int IdHoSo { get; set; }

        [Required]
        public int IdPhuongThucXetTuyen { get; set; }

        [Required]
        public int IdNganh { get; set; }

        [Required]
        public int IdToHopXetTuyen { get; set; }

        [Required]
        public int ThuTuXet { get; set; }

        public bool? DangKyHocChatLuongCao { get; set; }
    }

    public class UpdateTuyenSinhDangKyXetTuyenModel
    {
        public int IdHoSo { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public List<UpdateTuyenSinhDangKyXetTuyenDetailModel> Details { get; set; }
    }

    public class TuyenSinhDangKyXetTuyenModel : TuyenSinhDangKyXetTuyenBaseModel
    {
        public string MaNganh { get; set; }
        public string MaToHop { get; set; }
        public string TenNganh { get; set; }
        public string TenToHopXetTuyen { get; set; }
        public string GhiChu {  get; set; }
        public List<DiemDetailModel> DiemDetails { get; set; }
    }

    public class TuyenSinhDangKyXetTuyenFilter
    {
        public int IdHoSo { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
    }

    public class UpdateTuyenSinhDangKyXetTuyenDetailModel
    {
        public int IdNganh { get; set; }
        public int IdToHopXetTuyen { get; set; }
        public int ThuTuXet { get; set; }
        public bool? DangKyHocChatLuongCao { get; set; }
    }
    public class TraCuuNguyenVongXetTuyenResponseModel
    {
        public string HoTen { get; set; }
        public DateTime NgaySinh { get; set; }
        public string CMND { get; set; }
        public string TenPhuongThucXetTuyen { get; set; }
        public int ThuTuXet { get; set; }
        public string TenNganh { get; set; }
        public string MaNganh { get; set; }
        public string TenToHopXetTuyen { get; set; }
        public int TrungTuyen { get; set; }
        public string GhiChuDuyetHoSo {  get; set; }
    }
    public class TraCuuNguyenVongXetTuyenRequestModel
    {
        public string CMND { get; set; }
        public DateTime NgaySinh { get; set; }
    }
}
