using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class NganhBaseModel
    {
        public int IdNganh { get; set; }
        public string MaNganh { get; set; }
        public string TenNganh { get; set; }
        public string TenNganhEn { get; set; }
    }

    public class NganhModel : NganhBaseModel
    {
        public bool? NganhChatLuongCao { get; set; }
    }

    public class NganhDetailModel : NganhModel
    {

    }
    public class CreateNganhModel
    {
        public string MaNganh { get; set; }
        public string TenNganh { get; set; }
        public string TenNganhEn { get; set; }
        public bool? NganhChatLuongCao { get; set; }
    }
    public class UpdateNganhModel : NganhModel
    {
        public void UpdateEntity(SvNganh entity)
        {
            entity.TenNganh = this.TenNganh;
            entity.TenNganhEn = this.TenNganhEn;
        }
    }

    public class NganhQueryFilterModel : BaseQueryFilterModel
    {
        public string MaNganh { get; set; }
        public NganhQueryFilterModel()
        {
            PropertyName = "TenNganh";
            Ascending = "desc";
        }
    }

    public class NganhSelectItemModel : NganhModel
    {
        public int IdPhuongThucXetTuyen { get; set; }
        public int IdHe {  get; set; }
    }
}
