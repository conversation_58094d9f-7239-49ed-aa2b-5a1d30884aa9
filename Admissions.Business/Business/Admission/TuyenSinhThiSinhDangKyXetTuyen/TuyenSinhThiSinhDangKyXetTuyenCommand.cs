using Admissions.Data;
using Admissions.Shared;
using Admissions.Shared.EmailTemplate;
using Core.Business;
using Core.Business.System;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Core.Shared.Enums;

namespace Admissions.Business
{
    public class CreateThiSinhDangKyXetTuyenCommand : IRequest<Unit>
    {
        public CreateThiSinhDangKyXetTuyenHocBaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateThiSinhDangKyXetTuyenCommand(CreateThiSinhDangKyXetTuyenHocBaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateThiSinhDangKyXetTuyenCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            protected readonly IMediator _mediator;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _mediator = mediator;
            }
            public async Task<Unit> Handle(CreateThiSinhDangKyXetTuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Create {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(new { model.HaveHoatDongNgoaiKhoa, model.HaveKetQuaThiNangKhieu, model.HaveKetQuaHocBa, model.HaveHoSoNop, model.HaveTuyenSinhDangKyXetTuyen, 
                                                                                                                                  model.HaveTuyenSinhDoiTuongXetTuyen, model.HaveTuyenSinhKetQuaThiThpt, model.HaveKetQuaThacSi}));
                var maPhanHe = AdmissionConstants.MA_PHAN_HE;

                // Lấy IdPh từ mã phân hệ
                var idPhanHe = (await _mediator.Send(new GetComboboxPhanHeQuery()))
                    .FirstOrDefault(x => x.PhanHe == maPhanHe)?.IdPh;

                if (idPhanHe is null)
                    throw new ArgumentException("PhanHe.Not-Existed");

                // Lấy tham số hệ thống theo IdPh
                var lstThamSo = await _mediator.Send(new GetThamSoHeThongByIdPhQuery(idPhanHe.Value));

                using (IDbContextTransaction transaction = _dataContext.Database.BeginTransaction())
                {
                    try
                    {
                        // Thông tin cá nhân
                        var hoSoTuyenSinh = await _dataContext.SvHoSoTuyenSinhs.FirstOrDefaultAsync(x => x.IdHoSo == Int32.Parse(systemLog.UserId));
                        if (hoSoTuyenSinh == null)
                        {
                            throw new ArgumentException($"{_localizer["HoSoTuyenSinh.NotExisted", JsonSerializer.Serialize(model.HoSoTuyenSinh.CMND)]}");
                        }

                        if (model.HaveHoSoTuyenSinh)
                        {
                            Log.Information($"Update thong tin ca nhan {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.HoSoTuyenSinh));
                            model.UpdateHoSoTuyenSinhDetail(hoSoTuyenSinh);
                            _dataContext.SvHoSoTuyenSinhs.Update(hoSoTuyenSinh);
                        }

                        // Điểm học bạ
                        if (model.HaveKetQuaHocBa)
                        {
                            Log.Information($"Create diem hoc ba {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.KetQuaHocBas));

                            #region Check ngưỡng điểm đầu vào

                            var thamSo =  lstThamSo.FirstOrDefault(x => x.IdThamSo == AdmissionsThamSoHeThongEnum.Don_vi_su_dung.ToString());

                            if (thamSo != null && thamSo.GiaTri == AdmissionsDonViSuDungConstants.CDYTBM)
                            {
                                bool nguongDiemValid = model.KetQuaHocBas.Any(x => new decimal?[] { x.Diem1, x.Diem2, x.Diem3, x.Diem4, x.Diem5, x.Diem6 }.Where(x => x.HasValue).Any(d => d.Value < 5.5m));
                                if (nguongDiemValid)
                                    throw new AggregateException("diem-hoc-ba.nguong-diem.invalid");
                            }

                            #endregion

                            bool diemInvalid = model.KetQuaHocBas.Any(x => new[] { x.Diem1, x.Diem2, x.Diem3, x.Diem4, x.Diem5, x.Diem6 }.Where(x => x.HasValue).Any(d => d.Value < 0 || d.Value > 10));
                            if (diemInvalid)
                                throw new AggregateException("diem-hoc-ba.invalid");

                            var listIdMon = model.KetQuaHocBas.Where(x => x.Edit).Select(x => x.IdMonXetTuyen).ToList();
                            var ketQuaHocBaQuery = _dataContext.SvTuyenSinhKetQuaHocTapHocBas.AsQueryable();
                            
                            if (listIdMon.Count() != listIdMon.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }
                            var ketQuaHocBaExist = await ketQuaHocBaQuery.Where(x => listIdMon.Contains(x.IdMonXetTuyen) && x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();

                            // Xóa kết quả học bạ cũ
                            _dataContext.SvTuyenSinhKetQuaHocTapHocBas.RemoveRange(ketQuaHocBaExist);

                            //Thêm mới kết quả học bạ
                            var listKetQuaHocBa = model.KetQuaHocBas.Where(x => x.Edit == true).Select(x => new SvTuyenSinhKetQuaHocTapHocBa()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem1 = x.Diem1,
                                Diem2 = x.Diem2,
                                Diem3 = x.Diem3,
                                Diem4 = x.Diem4,
                                Diem5 = x.Diem5,
                                Diem6 = x.Diem6
                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaHocTapHocBas.AddRangeAsync(listKetQuaHocBa);
                        }

                        // Xét Đăng ký nguyện vọng
                        if (model.HaveTuyenSinhDangKyXetTuyen)
                        {
                            Log.Information($"Create dang ky xet tuyen {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.TuyenSinhDangKyXetTuyens));
                            
                            var checkDuplicate = model.TuyenSinhDangKyXetTuyens.Select(x => new { x.IdNganh, x.IdToHopXetTuyen }).ToList();
                            var listIdNganh = model.TuyenSinhDangKyXetTuyens.Select(x => x.IdNganh).ToList();
                            var listThuTuXet = model.TuyenSinhDangKyXetTuyens.Select(x => x.ThuTuXet).Order().ToList();

                            if (model.TuyenSinhDangKyXetTuyens.Count() != checkDuplicate.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                            }

                            if (model.TuyenSinhDangKyXetTuyens.Count() != listThuTuXet.Distinct().Count() || model.TuyenSinhDangKyXetTuyens.Any(x => x.ThuTuXet < 0) || !CheckInputData.CheckThuTuNguyenVong(listThuTuXet))
                            {
                                throw new ArgumentException($"{_localizer["InsertData.ThuTuXet.Invalid"]}");
                            }

                            if (await _dataContext.SvTuyenSinhDangKyXetTuyens.AnyAsync(x => listIdNganh.Contains(x.IdNganh) && x.IdHoSo == hoSoTuyenSinh.IdHoSo && x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen))
                            {
                                throw new ArgumentException($"{_localizer["TuyenSinhDangKyXetTuyen.Exist"]}");
                            }

                            var listDangKyXetTuyen = model.TuyenSinhDangKyXetTuyens.Select(x => new SvTuyenSinhDangKyXetTuyen()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdNganh = x.IdNganh,
                                IdPhuongThucXetTuyen = model.IdPhuongThucXetTuyen,
                                IdToHopXetTuyen = x.IdToHopXetTuyen,
                                ThuTuXet = x.ThuTuXet,
                                DangKyHocChatLuongCao = x.DangKyHocChatLuongCao
                            }).ToList();

                            await _dataContext.SvTuyenSinhDangKyXetTuyens.AddRangeAsync(listDangKyXetTuyen);
                        }

                        // Hồ sơ giấy tờ
                        if (model.HaveHoSoNop)
                        {
                            Log.Information($"Create ho so nop {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.HoSoNops.Select(x => new { x.IdGiayTo, x.Edit })));
                            
                            var listIdGiayTo = model.HoSoNops.Select(x => x.IdGiayTo).ToList();

                            if (model.HoSoNops.Count() != listIdGiayTo.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdGiayTo.Duplicate"]}");
                            }
                            //Lọc những giấy tờ đã tồn tại trong db
                            var listIdGiayToExists = await _dataContext.SvHoSoNopTuyenSinhs.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).Select(x => x.IdGiayTo).ToListAsync();

                            //Lọc không lưu mới những giấy tờ đã tồn tại
                            var hoSoNops = model.HoSoNops.Where(x => !listIdGiayToExists.Contains(x.IdGiayTo)).Select(x => new SvHoSoNopTuyenSinh()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdGiayTo = x.IdGiayTo,
                                DaTra = false
                            }).ToList();

                            await _dataContext.SvHoSoNopTuyenSinhs.AddRangeAsync(hoSoNops);
                            await _dataContext.SaveChangesAsync();

                            var listHoSoNop = await _dataContext.SvHoSoNopTuyenSinhs.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();

                            var listHoSoNopTuyenSinhUrl = new List<SvHoSoNopTuyenSinhUrl>();

                            foreach (var item in model.HoSoNops)
                            {
                                if (item.Edit)
                                {
                                    var hoSoNop = listHoSoNop.FirstOrDefault(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo && x.IdGiayTo == item.IdGiayTo);
                                    // Xóa những file đã tồn tại
                                    var hoSoNopTuyenSinhExists = await _dataContext.SvHoSoNopTuyenSinhUrls.Where(x => x.IdHoSoNopTuyenSinh == hoSoNop.IdHoSoNopTuyenSinh).ToListAsync();
                                    _dataContext.SvHoSoNopTuyenSinhUrls.RemoveRange(hoSoNopTuyenSinhExists);

                                    var hoSoNopTuyenSinhUrls = item.HoSoNopTuyenSinhUrls.Select(x => new SvHoSoNopTuyenSinhUrl()
                                    {
                                        FileName = x.FileName,
                                        IdHoSoNopTuyenSinh = hoSoNop.IdHoSoNopTuyenSinh,
                                        LoaiFile = x.LoaiFile,
                                        UrlGiayTo = x.UrlGiayTo
                                    }).ToList();

                                    listHoSoNopTuyenSinhUrl.AddRange(hoSoNopTuyenSinhUrls);
                                }
                            }

                            await _dataContext.SvHoSoNopTuyenSinhUrls.AddRangeAsync(listHoSoNopTuyenSinhUrl);
                            await _dataContext.SaveChangesAsync();
                        }

                        // Lưu đối tượng xét tuyển
                        if (model.HaveTuyenSinhDoiTuongXetTuyen)
                        {
                            Log.Information($"Create doi tuong xet tuyen {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.TuyenSinhDoiTuongXetTuyens));
                            
                            var listIdDoiTuong = model.TuyenSinhDoiTuongXetTuyens.Select(x => x.IdDoiTuongXetTuyen).ToList();

                            if (model.TuyenSinhDoiTuongXetTuyens.Count() != listIdDoiTuong.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdDoiTuongXetTuyen.Duplicate"]}");
                            }

                            if (await _dataContext.SvTuyenSinhDoiTuongXetTuyens.AnyAsync(x => listIdDoiTuong.Contains(x.IdDoiTuongXetTuyen) && x.IdHoSo == hoSoTuyenSinh.IdHoSo))
                            {
                                throw new ArgumentException($"{_localizer["TuyenSinhDoiTuongXetTuyen.Exist"]}");
                            }

                            var tuyenSinhDoiTuongXetTuyens = model.TuyenSinhDoiTuongXetTuyens.Select(x => new SvTuyenSinhDoiTuongXetTuyen()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdDoiTuongXetTuyen = x.IdDoiTuongXetTuyen
                            }).ToList();

                            await _dataContext.SvTuyenSinhDoiTuongXetTuyens.AddRangeAsync(tuyenSinhDoiTuongXetTuyens);
                            await _dataContext.SaveChangesAsync();
                        }

                        // Lưu kết quả thi thpt quốc gia 
                        if (model.HaveTuyenSinhKetQuaThiThpt)
                        {
                            Log.Information($"Create ket qua thi thpt {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.TuyenSinhKetQuaThiThpts));
                            var listIdMonXetTuyen = model.TuyenSinhKetQuaThiThpts.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.TuyenSinhKetQuaThiThpts.Count() != listIdMonXetTuyen.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }

                            if (await _dataContext.SvTuyenSinhKetQuaThiTHPTs.AnyAsync(x => listIdMonXetTuyen.Contains(x.IdMonXetTuyen) && x.IdHoSo == hoSoTuyenSinh.IdHoSo))
                            {
                                throw new ArgumentException($"{_localizer["TuyenSinhKetQuaThiThpt.Exist"]}");
                            }

                            var tuyenSinhKetQuaThiThpts = model.TuyenSinhKetQuaThiThpts.Select(x => new SvTuyenSinhKetQuaThiTHPT()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem = x.Diem,
                                CreateUserName = hoSoTuyenSinh.CMND,
                                CreateDate = DateTime.Now

                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaThiTHPTs.AddRangeAsync(tuyenSinhKetQuaThiThpts);
                            await _dataContext.SaveChangesAsync(); 
                        }

                        // Lưu kết quả thi năng khiếu
                        if (model.HaveKetQuaThiNangKhieu)
                        {
                            Log.Information($"Create ket qua thi nang khieu {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.KetQuaThiNangKhieus));
                            var listIdMonXetTuyen = model.KetQuaThiNangKhieus.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.KetQuaThiNangKhieus.Count() != listIdMonXetTuyen.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }

                            if (await _dataContext.SvTuyenSinhKetQuaThiNangKhieus.AnyAsync(x => listIdMonXetTuyen.Contains(x.IdMonXetTuyen) && x.IdHoSo == hoSoTuyenSinh.IdHoSo))
                            {
                                throw new ArgumentException($"{_localizer["TuyenSinhKetQuaThiNangKhieu.Exist"]}");
                            }

                            var tuyenSinhKetQuaThiNangKhieus = model.KetQuaThiNangKhieus.Select(x => new SvTuyenSinhKetQuaThiNangKhieu()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem = x.Diem,
                                CreateUserName = hoSoTuyenSinh.CMND,
                                CreateDate = DateTime.Now

                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaThiNangKhieus.AddRangeAsync(tuyenSinhKetQuaThiNangKhieus);
                            await _dataContext.SaveChangesAsync();
                        }

                        // Lưu kết quả thi đánh giá năng lực
                        if (model.HaveKetQuaDanhGiaNangLuc)
                        {
                            Log.Information($"Create ket qua danh gia nang luc {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.KetQuaDanhGiaNangLucs));
                            
                            var listIdMonXetTuyen = model.KetQuaDanhGiaNangLucs.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.KetQuaDanhGiaNangLucs.Count() != listIdMonXetTuyen.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }

                            if (await _dataContext.SvTuyenSinhKetQuaDanhGiaNangLucs.AnyAsync(x => listIdMonXetTuyen.Contains(x.IdMonXetTuyen) && x.IdHoSo == hoSoTuyenSinh.IdHoSo))
                            {
                                throw new ArgumentException($"{_localizer["TuyenSinhKetQuaDanhGiaNangLuc.Exist"]}");
                            }

                            var tuyenSinhKetQuaDanhGiaNangLucs = model.KetQuaDanhGiaNangLucs.Select(x => new SvTuyenSinhKetQuaDanhGiaNangLuc()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem = x.Diem,
                                CreateUserName = hoSoTuyenSinh.CMND,
                                CreateDate = DateTime.Now
                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaDanhGiaNangLucs.AddRangeAsync(tuyenSinhKetQuaDanhGiaNangLucs);
                            await _dataContext.SaveChangesAsync();
                        }

                        // Lưu kết quả thạc sĩ
                        if (model.HaveKetQuaThacSi)
                        {
                            Log.Information($"Create ket qua thac si {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.KetQuaThacSis));
                            
                            var listIdMonXetTuyen = model.KetQuaThacSis.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.KetQuaThacSis.Count() != listIdMonXetTuyen.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }

                            if (await _dataContext.SvTuyenSinhKetQuaThacSis.AnyAsync(x => listIdMonXetTuyen.Contains(x.IdMonXetTuyen) && x.IdHoSo == hoSoTuyenSinh.IdHoSo))
                            {
                                throw new ArgumentException($"{_localizer["TuyenSinhKetQuaThacSi.Exist"]}");
                            }

                            var ketQuaThacSis = model.KetQuaThacSis.Select(x => new SvTuyenSinhKetQuaThacSi()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem = x.Diem,
                                CreateUserName = hoSoTuyenSinh.CMND,
                                CreateDate = DateTime.Now

                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaThacSis.AddRangeAsync(ketQuaThacSis);
                            await _dataContext.SaveChangesAsync();
                        }

                        // Lưu hoạt động ngoại khóa
                        if (model.HaveHoatDongNgoaiKhoa)
                        {
                            Log.Information($"Create hoat dong ngoai khoa {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.TuyenSinhHoatDongNgoaiKhoas));
                            var lstHoatDongNgoaiKhoa = model.TuyenSinhHoatDongNgoaiKhoas.Select(x => new SvTuyenSinhHoatDongNgoaiKhoa
                            {
                                IdHoSo = Int32.Parse(systemLog.UserId),
                                HoatDongNgoaiKhoa = x.HoatDongNgoaiKhoa,
                                ThanhTichDatDuoc = x.ThanhTichDatDuoc,
                                NgayTao = DateTime.Now,
                                NguoiTao = systemLog.UserName
                            }).ToList();

                            await _dataContext.SvTuyenSinhHoatDongNgoaiKhoas.AddRangeAsync(lstHoatDongNgoaiKhoa);
                            await _dataContext.SaveChangesAsync();
                        }
                        
                        // Lưu chứng chỉ
                        if (model.HaveChungChi)
                        {
                            Log.Information($"Create chung chi thi sinh {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.TuyenSinhChungChis));

                            // Insert vào bảng SvTuyenSinhChungChiThiSinh
                            var lstChungChiThiSinh = model.TuyenSinhChungChis.Select(x => new SvTuyenSinhChungChiThiSinh
                            {
                                IdHoSo = Int32.Parse(systemLog.UserId),
                                IdTuyenSinhChungChi = x.IdChungChi,
                                Diem = x.Diem,
                                DiemThanhPhanThapNhat = x.DiemThanhPhanThapNhat,
                                NgayHetHan = x.NgayHetHan,
                                NgayTao = DateTime.Now,
                                NguoiTao = systemLog.UserName
                            }).ToList();

                            await _dataContext.SvTuyenSinhChungChiThiSinhs.AddRangeAsync(lstChungChiThiSinh);
                            await _dataContext.SaveChangesAsync();

                            // Insert vào bảng SvTuyenSinhChungChiMinhChung (nếu có minh chứng)
                            var lstChungChiMinhChung = new List<SvTuyenSinhChungChiMinhChung>();

                            foreach (var chungChi in model.TuyenSinhChungChis)
                            {
                                if (chungChi.HaveMinhChung && chungChi.MinhChungs?.Any() == true)
                                {
                                    // Lấy ID của chứng chỉ vừa tạo
                                    var chungChiThiSinh = lstChungChiThiSinh.FirstOrDefault(x =>
                                        x.IdTuyenSinhChungChi == chungChi.IdChungChi);

                                    if (chungChiThiSinh != null)
                                    {
                                        var minhChungs = chungChi.MinhChungs.Select(mc => new SvTuyenSinhChungChiMinhChung
                                        {
                                            IdTuyenSinhChungChiThiSinh = chungChiThiSinh.IdTuyenSinhChungChiThiSinh,
                                            IdMinhChung = mc.IdMinhChung,
                                            NgayTao = DateTime.Now,
                                            NguoiTao = systemLog.UserName
                                        }).ToList();

                                        lstChungChiMinhChung.AddRange(minhChungs);
                                    }
                                }
                            }

                            if (lstChungChiMinhChung.Any())
                            {
                                await _dataContext.SvTuyenSinhChungChiMinhChungs.AddRangeAsync(lstChungChiMinhChung);
                                await _dataContext.SaveChangesAsync();
                            }
                        }

                        // Lưu thông tin xét tuyển
                        var thiSinhDangKyXetTuyen = new SvTuyenSinhThiSinhDangKyXetTuyen()
                        {
                            IdHoSo = hoSoTuyenSinh.IdHoSo,
                            IdHe = model.IdHe,
                            IdPhuongThucXetTuyen = model.IdPhuongThucXetTuyen,
                            IdPhuongAnDiemHocBa = model.IdPhuongAnDiemHocBa,
                            NamTuyenSinh = model.NamTuyenSinh,
                            SoTienLePhiPhaiNop = 0,
                            SoTienLePhiDaNop = 0,
                            KetQuaXetTuyen = false,
                            TrangThaiHoSo = 0,
                            TrangThaiTaiChinh = 0,
                            CreateUserName = hoSoTuyenSinh.CMND,
                            CreateDate = DateTime.Now,
                        };

                        if (await _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.AnyAsync(x => x.IdPhuongThucXetTuyen == thiSinhDangKyXetTuyen.IdPhuongThucXetTuyen && x.IdHoSo == thiSinhDangKyXetTuyen.IdHoSo && x.IdHe == thiSinhDangKyXetTuyen.IdHe && thiSinhDangKyXetTuyen.NamTuyenSinh == model.NamTuyenSinh))
                        {
                            throw new ArgumentException($"{_localizer["TuyenSinhThiSinhDangKyXetTuyens.Exist"]}");
                        }

                        await _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.AddAsync(thiSinhDangKyXetTuyen);

                        #region Thêm mới khoản thu - tạo hồ sơ
                        
                        var dotThu = _dataContext.SvTuyenSinhDotDangKys.FirstOrDefault(x => x.DotDangKy == model.DotDangKy);
                        var khoanThus = await _dataContext.SvTuyenSinhKhoanThus.Where(x => dotThu != null && x.IdDotDangKy == dotThu.IdDotDangKy && x.LoaiKhoanThu == 0).ToListAsync();
                        var hinhThucThanhToan = lstThamSo.FirstOrDefault(x => x.IdThamSo == AdmissionsThamSoHeThongEnum.Hinh_thuc_thanh_toan.ToString())?.GiaTri;
                        var hinhThucThanhToanValue = Int32.TryParse(hinhThucThanhToan, out var value) ? value : 0;
                        Log.Information($"Create thanh toan {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(new { model.IdHe, model.IdPhuongThucXetTuyen, khoanThus, dotThu}));
                       
                        if(khoanThus.Count() > 0)
                        {
                            // Kiểm tra xem thanh toán đã tồn tại hay chưa
                            var existingThanhToan = await _dataContext.SvTuyenSinhThanhToans
                                .FirstOrDefaultAsync(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo
                                                       && x.IdHe == model.IdHe
                                                       && x.NamTuyenSinh == model.NamTuyenSinh
                                                       && x.TrangThai == 0
                                                       );

                            SvTuyenSinhThanhToan thanhToan;

                            if (existingThanhToan != null && hinhThucThanhToanValue == 1)
                            {
                                thanhToan = existingThanhToan;
                            }
                            else
                            {
                                // Tạo mới thanh toán
                                thanhToan = new SvTuyenSinhThanhToan()
                                {
                                    IdHoSo = hoSoTuyenSinh.IdHoSo,
                                    IdHe = model.IdHe,
                                    NamTuyenSinh = model.NamTuyenSinh,
                                    HinhThucThanhToan = hinhThucThanhToanValue,
                                    TrangThai = 0,
                                    SoTien = 0,
                                    NgayTao = DateTime.Now
                                };

                                await _dataContext.SvTuyenSinhThanhToans.AddAsync(thanhToan);
                                await _dataContext.SaveChangesAsync();
                            }

                            var thanhToanChiTiets = new List<SvTuyenSinhThanhToanChiTiet>();

                            foreach(var item in khoanThus)
                            {
                                // Lấy số lượng thanh toán
                                var soLuong = model.HaveTuyenSinhDangKyXetTuyen && item.DonViTinh == AdmissionsDonViTinhEnum.NguyenVong.ToString() ? model.TuyenSinhDangKyXetTuyens.Count : 1;

                                thanhToanChiTiets.Add(new SvTuyenSinhThanhToanChiTiet()
                                {
                                    IdTuyenSinhThanhToan = thanhToan.IdTuyenSinhThanhToan, 
                                    IdTuyenSinhKhoanThu = item.IdTuyenSinhKhoanThu, 
                                    IdDot = dotThu.IdDotDangKy, 
                                    DonGia = item.SoTien, 
                                    DonViTinh = CheckInputData.GetDonViTinh(item.DonViTinh), 
                                    SoLuong = soLuong, 
                                    SoTien = item.SoTien * soLuong
                                });
                            }

                            await _dataContext.SvTuyenSinhThanhToanChiTiets.AddRangeAsync(thanhToanChiTiets);
                            thanhToan.SoTien = thanhToan.SoTien + thanhToanChiTiets.Where(x => x.SoTien.HasValue).Sum(x => x.SoTien.Value);
                        }
                        #endregion

                        await _dataContext.SaveChangesAsync();
                        transaction.Commit();

                        var phuongThucXetTuyen = await _dataContext.SvTuyenSinhPhuongThucXetTuyens.FirstOrDefaultAsync(x => x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen);

                        var mailModel = new SendMailUsingTemplateModel()
                        {
                            To = [hoSoTuyenSinh.Email],
                            EmailTemplateCode = EmailCodeConstants.DHM_NHS,
                            Data = new
                            {
                                hoSoTuyenSinh.HoTen,
                                phuongThucXetTuyen.TenPhuongThucXetTuyen
                            }
                        };
                        await _mediator.Send(new SendMailUsingEmailTemplateQuery(mailModel, systemLog));

                        Log.Information($"Create {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix} success");
                        systemLog.ListAction.Add(new ActionDetail()
                        {
                            Description = $"Thêm mới hồ sơ đăng ký tuyển sinh: {model.HoSoTuyenSinh.CMND}, phương thức: {phuongThucXetTuyen?.TenPhuongThucXetTuyen}",
                            ObjectCode = TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix,
                            ObjectId = hoSoTuyenSinh.IdHoSo.ToString()
                        });

                        //Xóa cache
                        _cacheService.Remove(TuyenSinhThiSinhDangKyXetTuyenConstant.BuildCacheKey());
                        _cacheService.Remove(TuyenSinhThanhToanConstant.BuildCacheKey());
                        return Unit.Value;
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new ArgumentException($"{_localizer[$"{ex.Message}"]}");
                    }
                }
            }
        }
    }

    public class UpdateThiSinhDangKyXetTuyenCommand : IRequest<Unit>
    {
        public UpdateTuyenSinhThiSinhDangKyXetTuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateThiSinhDangKyXetTuyenCommand(UpdateTuyenSinhThiSinhDangKyXetTuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateThiSinhDangKyXetTuyenCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            protected readonly IMediator _mediator;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _mediator = mediator;
            }
            public async Task<Unit> Handle(UpdateThiSinhDangKyXetTuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Before Update {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                using (IDbContextTransaction transaction = _dataContext.Database.BeginTransaction())
                {
                    try
                    {
                        // Thông tin đăng ký xét tuyển
                        var thiSinhDangKyXetTuyen = _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.FirstOrDefault(x => x.IdThiSinhDangKyXetTuyen == model.IdThiSinhDangKyXetTuyen);
                        
                        // Thông tin cá nhân
                        var hoSoTuyenSinh = await _dataContext.SvHoSoTuyenSinhs.FirstOrDefaultAsync(x => x.IdHoSo == model.HoSoTuyenSinh.IdHoSo);
                        if (hoSoTuyenSinh == null || thiSinhDangKyXetTuyen == null)
                        {
                            throw new ArgumentException($"{_localizer["HoSoTuyenSinh.NotExisted", JsonSerializer.Serialize(model.HoSoTuyenSinh.CMND)]}");
                        }

                        if (model.HaveHoSoTuyenSinh)
                        {
                            model.UpdateHoSoTuyenSinhDetail(hoSoTuyenSinh);
                            _dataContext.SvHoSoTuyenSinhs.Update(hoSoTuyenSinh);
                        }

                        // Điểm học bạ
                        if (model.HaveKetQuaHocBa)
                        {
                            var maPhanHe = AdmissionConstants.MA_PHAN_HE;

                            // Lấy IdPh từ mã phân hệ
                            var idPhanHe = (await _mediator.Send(new GetComboboxPhanHeQuery()))
                                .FirstOrDefault(x => x.PhanHe == maPhanHe)?.IdPh;

                            if (idPhanHe is null)
                                throw new ArgumentException("PhanHe.Not-Existed");

                            // Lấy tham số hệ thống theo IdPh
                            var thamSo = (await _mediator.Send(new GetThamSoHeThongByIdPhQuery(idPhanHe.Value)))
                                .FirstOrDefault(x => x.IdThamSo == AdmissionsThamSoHeThongEnum.Don_vi_su_dung.ToString());

                            if (thamSo != null && thamSo.GiaTri == AdmissionsDonViSuDungConstants.CDYTBM)
                            {
                                bool nguongDiemValid = model.KetQuaHocBas.Any(x => new decimal?[] { x.Diem1, x.Diem2, x.Diem3, x.Diem4, x.Diem5, x.Diem6 }.Where(x => x.HasValue).Any(d => d.Value < 5.5m));
                                if (nguongDiemValid)
                                    throw new AggregateException("diem-hoc-ba.nguong-diem.invalid");
                            }

                            bool diemInvalid = model.KetQuaHocBas.Any(x => new[] { x.Diem1, x.Diem2, x.Diem3, x.Diem4, x.Diem5, x.Diem6 }.Where(x => x.HasValue).Any(d => d.Value < 0 || d.Value > 10));
                            if (diemInvalid)
                                throw new AggregateException("diem-hoc-ba.invalid");

                            var listIdMon = model.KetQuaHocBas.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.KetQuaHocBas.Count != listIdMon.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }
                            var listIdMonExist = model.KetQuaHocBas.Where(x => x.Edit == true).Select(x => x.IdMonXetTuyen).ToList();
                            var ketQuaHocBaExist = _dataContext.SvTuyenSinhKetQuaHocTapHocBas.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo && listIdMonExist.Contains(x.IdMonXetTuyen));

                            // Xóa kết quả học bạ cũ

                            _dataContext.SvTuyenSinhKetQuaHocTapHocBas.RemoveRange(ketQuaHocBaExist);

                            //Thêm mới kết quả học bạ
                            var listKetQuaHocBa = model.KetQuaHocBas.Where(x => x.Edit == true).Select(x => new SvTuyenSinhKetQuaHocTapHocBa()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem1 = x.Diem1,
                                Diem2 = x.Diem2,
                                Diem3 = x.Diem3,
                                Diem4 = x.Diem4,
                                Diem5 = x.Diem5,
                                Diem6 = x.Diem6
                            }).ToList();

                            // Cập nhật phương án điểm học bạ
                            thiSinhDangKyXetTuyen.IdPhuongAnDiemHocBa = model.IdPhuongAnDiemHocBa;

                            // Lưu kết quả học tập học bạ
                            await _dataContext.SvTuyenSinhKetQuaHocTapHocBas.AddRangeAsync(listKetQuaHocBa);
                        }

                        // Xét Đăng ký nguyện vọng
                        if (model.HaveTuyenSinhDangKyXetTuyen)
                        {
                            // Xóa nguyện vọng đã lưu
                            var dangKyXetTuyenExists = await _dataContext.SvTuyenSinhDangKyXetTuyens.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo && x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen).ToListAsync();
                            _dataContext.SvTuyenSinhDangKyXetTuyens.RemoveRange(dangKyXetTuyenExists);
                            _dataContext.SaveChanges();

                            // Lưu mới nguyện vọng
                            var checkDuplicate = model.TuyenSinhDangKyXetTuyens.Select(x => new { x.IdNganh, x.IdToHopXetTuyen }).ToList();
                            var listThuTuXet = model.TuyenSinhDangKyXetTuyens.Select(x => x.ThuTuXet).Order().ToList();

                            if (model.TuyenSinhDangKyXetTuyens.Count() != checkDuplicate.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                            }

                            if (model.TuyenSinhDangKyXetTuyens.Count() != listThuTuXet.Distinct().Count() || model.TuyenSinhDangKyXetTuyens.Any(x => x.ThuTuXet < 0) || !CheckInputData.CheckThuTuNguyenVong(listThuTuXet))
                            {
                                throw new ArgumentException($"{_localizer["InsertData.ThuTuXet.Invalid"]}");
                            }

                            var listDangKyXetTuyen = model.TuyenSinhDangKyXetTuyens.Select(x => new SvTuyenSinhDangKyXetTuyen()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdNganh = x.IdNganh,
                                IdPhuongThucXetTuyen = model.IdPhuongThucXetTuyen,
                                IdToHopXetTuyen = x.IdToHopXetTuyen,
                                ThuTuXet = x.ThuTuXet,
                                DangKyHocChatLuongCao = x.DangKyHocChatLuongCao
                            }).ToList();

                            await _dataContext.SvTuyenSinhDangKyXetTuyens.AddRangeAsync(listDangKyXetTuyen);
                        }

                        // Hồ sơ giấy tờ
                        if (model.HaveHoSoNop)
                        {
                            var listIdGiayTo = model.HoSoNops.Select(x => x.IdGiayTo).ToList();

                            if (model.HoSoNops.Count() != listIdGiayTo.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdGiayTo.Duplicate"]}");
                            }
                            //Lọc những giấy tờ đã tồn tại trong db
                            var listIdGiayToExists = await _dataContext.SvHoSoNopTuyenSinhs.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).Select(x => x.IdGiayTo).ToListAsync();

                            //Lọc không lưu mới những giấy tờ đã tồn tại
                            var hoSoNops = model.HoSoNops.Where(x => !listIdGiayToExists.Contains(x.IdGiayTo)).Select(x => new SvHoSoNopTuyenSinh()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdGiayTo = x.IdGiayTo,
                                DaTra = false
                            }).ToList();

                            await _dataContext.SvHoSoNopTuyenSinhs.AddRangeAsync(hoSoNops);
                            await _dataContext.SaveChangesAsync();

                            var listHoSoNop = await _dataContext.SvHoSoNopTuyenSinhs.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();

                            var listHoSoNopTuyenSinhUrl = new List<SvHoSoNopTuyenSinhUrl>();

                            foreach (var item in model.HoSoNops)
                            {
                                if (item.Edit)
                                {
                                    var hoSoNop = listHoSoNop.FirstOrDefault(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo && x.IdGiayTo == item.IdGiayTo);
                                    // Xóa những file đã tồn tại
                                    var hoSoNopTuyenSinhExists = await _dataContext.SvHoSoNopTuyenSinhUrls.Where(x => x.IdHoSoNopTuyenSinh == hoSoNop.IdHoSoNopTuyenSinh).ToListAsync();
                                    _dataContext.SvHoSoNopTuyenSinhUrls.RemoveRange(hoSoNopTuyenSinhExists);

                                    var hoSoNopTuyenSinhUrls = item.HoSoNopTuyenSinhUrls.Select(x => new SvHoSoNopTuyenSinhUrl()
                                    {
                                        FileName = x.FileName,
                                        IdHoSoNopTuyenSinh = hoSoNop.IdHoSoNopTuyenSinh,
                                        LoaiFile = x.LoaiFile,
                                        UrlGiayTo = x.UrlGiayTo
                                    }).ToList();

                                    listHoSoNopTuyenSinhUrl.AddRange(hoSoNopTuyenSinhUrls);
                                }
                            }

                            await _dataContext.SvHoSoNopTuyenSinhUrls.AddRangeAsync(listHoSoNopTuyenSinhUrl);
                            await _dataContext.SaveChangesAsync();
                        }

                        // Lưu đối tượng xét tuyển
                        if (model.HaveTuyenSinhDoiTuongXetTuyen)
                        {
                            // Xóa đối tượng xét tuyển đã lưu
                            var doiTuongXetTuyenExists = await _dataContext.SvTuyenSinhDoiTuongXetTuyens.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();
                            _dataContext.SvTuyenSinhDoiTuongXetTuyens.RemoveRange(doiTuongXetTuyenExists);
                            _dataContext.SaveChanges();

                            // Lưu mới đối tượng xét tuyển
                            var listIdDoiTuong = model.TuyenSinhDoiTuongXetTuyens.Select(x => x.IdDoiTuongXetTuyen).ToList();

                            if (model.TuyenSinhDoiTuongXetTuyens.Count() != listIdDoiTuong.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdDoiTuongXetTuyen.Duplicate"]}");
                            }

                            var tuyenSinhDoiTuongXetTuyens = model.TuyenSinhDoiTuongXetTuyens.Select(x => new SvTuyenSinhDoiTuongXetTuyen()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdDoiTuongXetTuyen = x.IdDoiTuongXetTuyen
                            }).ToList();

                            await _dataContext.SvTuyenSinhDoiTuongXetTuyens.AddRangeAsync(tuyenSinhDoiTuongXetTuyens);
                        }

                        // Lưu kết quả thi thpt quốc gia 
                        if (model.HaveTuyenSinhKetQuaThiThpt)
                        {
                            // Xóa kết quả thi thpt đã lưu
                            var ketQuaThiThptExists = await _dataContext.SvTuyenSinhKetQuaThiTHPTs.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();
                            _dataContext.SvTuyenSinhKetQuaThiTHPTs.RemoveRange(ketQuaThiThptExists);
                            _dataContext.SaveChanges();

                            // Lưu mới kết quả thi thpt 
                            var listIdMonXetTuyen = model.TuyenSinhKetQuaThiThpts.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.TuyenSinhKetQuaThiThpts.Count() != listIdMonXetTuyen.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }

                            var tuyenSinhKetQuaThiThpts = model.TuyenSinhKetQuaThiThpts.Select(x => new SvTuyenSinhKetQuaThiTHPT()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem = x.Diem,
                                CreateUserName = hoSoTuyenSinh.CMND,
                                CreateDate = DateTime.Now
                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaThiTHPTs.AddRangeAsync(tuyenSinhKetQuaThiThpts);
                        }

                        // Lưu kết quả thi năng khiếu
                        if (model.HaveKetQuaThiNangKhieu)
                        {
                            // Xóa kết quả thi năng khiếu đã lưu
                            var ketQuaThiNangKhieuExists = await _dataContext.SvTuyenSinhKetQuaThiNangKhieus.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();
                            _dataContext.SvTuyenSinhKetQuaThiNangKhieus.RemoveRange(ketQuaThiNangKhieuExists);
                            _dataContext.SaveChanges();

                            // Lưu mới kết quả thi năng khiếu
                            var listIdMonXetTuyen = model.KetQuaThiNangKhieus.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.KetQuaThiNangKhieus.Count() != listIdMonXetTuyen.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }

                            var tuyenSinhKetQuaThiNangKhieus = model.KetQuaThiNangKhieus.Select(x => new SvTuyenSinhKetQuaThiNangKhieu()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem = x.Diem,
                                CreateUserName = hoSoTuyenSinh.CMND,
                                CreateDate = DateTime.Now

                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaThiNangKhieus.AddRangeAsync(tuyenSinhKetQuaThiNangKhieus);
                        }

                        // Lưu kết quả thi đánh giá năng lực
                        if (model.HaveKetQuaDanhGiaNangLuc)
                        {
                            // Xóa kết quả thi đánh giá năng lực đã lưu
                            var ketQuaDanhGiaNangLucExists = await _dataContext.SvTuyenSinhKetQuaDanhGiaNangLucs.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();
                            _dataContext.SvTuyenSinhKetQuaDanhGiaNangLucs.RemoveRange(ketQuaDanhGiaNangLucExists);
                            _dataContext.SaveChanges();

                            // Lưu mới kết quả thi đánh giá năng lực
                            var listIdMonXetTuyen = model.KetQuaDanhGiaNangLucs.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.KetQuaDanhGiaNangLucs.Count() != listIdMonXetTuyen.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }

                            var tuyenSinhKetQuaDanhGiaNangLucs = model.KetQuaDanhGiaNangLucs.Select(x => new SvTuyenSinhKetQuaDanhGiaNangLuc()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem = x.Diem,
                                CreateUserName = hoSoTuyenSinh.CMND,
                                CreateDate = DateTime.Now

                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaDanhGiaNangLucs.AddRangeAsync(tuyenSinhKetQuaDanhGiaNangLucs);
                        }
                        // Lưu kết quả thạc sĩ
                        if (model.HaveKetQuaThacSi)
                        {
                            // Xóa kết quả thạc sĩ
                            var ketQuaThacSiExists = await _dataContext.SvTuyenSinhKetQuaThacSis.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();
                            _dataContext.SvTuyenSinhKetQuaThacSis.RemoveRange(ketQuaThacSiExists);
                            _dataContext.SaveChanges();

                            // Lưu mới kết quả thạc sĩ
                            var listIdMonXetTuyen = model.KetQuaThacSis.Select(x => x.IdMonXetTuyen).ToList();

                            if (model.KetQuaThacSis.Count() != listIdMonXetTuyen.Distinct().Count())
                            {
                                throw new ArgumentException($"{_localizer["InsertData.IdMonXetTuyen.Duplicate"]}");
                            }

                            var ketQuaThacSis = model.KetQuaThacSis.Select(x => new SvTuyenSinhKetQuaThacSi()
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdMonXetTuyen = x.IdMonXetTuyen,
                                Diem = x.Diem,
                                CreateUserName = hoSoTuyenSinh.CMND,
                                CreateDate = DateTime.Now

                            }).ToList();

                            await _dataContext.SvTuyenSinhKetQuaThacSis.AddRangeAsync(ketQuaThacSis);
                        }

                        // Lưu hoạt động ngoại khóa
                        if (model.HaveHoatDongNgoaiKhoa)
                        {
                            // Xóa hoạt động ngoại khóa
                            var hoatDongNgoaiKhoaExists = await _dataContext.SvTuyenSinhHoatDongNgoaiKhoas.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();
                            _dataContext.SvTuyenSinhHoatDongNgoaiKhoas.RemoveRange(hoatDongNgoaiKhoaExists);
                            _dataContext.SaveChanges();

                            var lstHoatDongNgoaiKhoa = model.TuyenSinhHoatDongNgoaiKhoas.Select(x => new SvTuyenSinhHoatDongNgoaiKhoa
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                HoatDongNgoaiKhoa = x.HoatDongNgoaiKhoa,
                                ThanhTichDatDuoc = x.ThanhTichDatDuoc,
                                NgayTao = DateTime.Now,
                                NguoiTao = systemLog.UserName
                            }).ToList();

                            await _dataContext.SvTuyenSinhHoatDongNgoaiKhoas.AddRangeAsync(lstHoatDongNgoaiKhoa);
                            await _dataContext.SaveChangesAsync();
                        }

                        // Lưu chứng chỉ
                        if (model.HaveChungChi)
                        {
                            // Xóa chứng chỉ cũ
                            var chungChiExists = await _dataContext.SvTuyenSinhChungChiThiSinhs.Where(x => x.IdHoSo == hoSoTuyenSinh.IdHoSo).ToListAsync();
                            if (chungChiExists.Any())
                            {
                                // Xóa minh chứng trước
                                var chungChiIds = chungChiExists.Select(x => x.IdTuyenSinhChungChiThiSinh).ToList();
                                var minhChungExists = await _dataContext.SvTuyenSinhChungChiMinhChungs.Where(x => chungChiIds.Contains(x.IdTuyenSinhChungChiThiSinh)).ToListAsync();
                                _dataContext.SvTuyenSinhChungChiMinhChungs.RemoveRange(minhChungExists);

                                // Xóa chứng chỉ
                                _dataContext.SvTuyenSinhChungChiThiSinhs.RemoveRange(chungChiExists);
                                _dataContext.SaveChanges();
                            }

                            Log.Information($"Update chung chi thi sinh {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model.TuyenSinhChungChis));

                            // Insert chứng chỉ mới
                            var lstChungChiThiSinh = model.TuyenSinhChungChis.Select(x => new SvTuyenSinhChungChiThiSinh
                            {
                                IdHoSo = hoSoTuyenSinh.IdHoSo,
                                IdTuyenSinhChungChi = x.IdChungChi,
                                Diem = x.Diem,
                                DiemThanhPhanThapNhat = x.DiemThanhPhanThapNhat,
                                NgayHetHan = x.NgayHetHan,
                                NgayTao = DateTime.Now,
                                NguoiTao = systemLog.UserName
                            }).ToList();

                            await _dataContext.SvTuyenSinhChungChiThiSinhs.AddRangeAsync(lstChungChiThiSinh);
                            await _dataContext.SaveChangesAsync();

                            // Insert minh chứng mới
                            var lstChungChiMinhChung = new List<SvTuyenSinhChungChiMinhChung>();

                            foreach (var chungChi in model.TuyenSinhChungChis)
                            {
                                if (chungChi.HaveMinhChung && chungChi.MinhChungs?.Any() == true)
                                {
                                    var chungChiThiSinh = lstChungChiThiSinh.FirstOrDefault(x =>
                                        x.IdTuyenSinhChungChi == chungChi.IdChungChi);

                                    if (chungChiThiSinh != null)
                                    {
                                        var minhChungs = chungChi.MinhChungs.Select(mc => new SvTuyenSinhChungChiMinhChung
                                        {
                                            IdTuyenSinhChungChiThiSinh = chungChiThiSinh.IdTuyenSinhChungChiThiSinh,
                                            IdMinhChung = mc.IdMinhChung,
                                            NgayTao = DateTime.Now,
                                            NguoiTao = systemLog.UserName
                                        }).ToList();

                                        lstChungChiMinhChung.AddRange(minhChungs);
                                    }
                                }
                            }

                            if (lstChungChiMinhChung.Any())
                            {
                                await _dataContext.SvTuyenSinhChungChiMinhChungs.AddRangeAsync(lstChungChiMinhChung);
                                await _dataContext.SaveChangesAsync();
                            }
                        }

                        if (thiSinhDangKyXetTuyen != null)
                        {
                            thiSinhDangKyXetTuyen.ModifyDate = DateTime.Now;
                            thiSinhDangKyXetTuyen.ModifyUserName = systemLog.UserName;
                            
                        }
                        if(thiSinhDangKyXetTuyen != null && model.ThiSinh)
                        {
                            thiSinhDangKyXetTuyen.TrangThaiHoSo = 0;
                        }
                        if (model.GhiChuHoSo)
                        {
                            thiSinhDangKyXetTuyen.GhiChuDuyetHoSo = model.GhiChuDuyetHoSo;
                        }
                        await _dataContext.SaveChangesAsync();

                        transaction.Commit();

                        var phuongThucXetTuyen = await _dataContext.SvTuyenSinhPhuongThucXetTuyens.FirstOrDefaultAsync(x => x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen);
                        Log.Information($"Update {TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");
                        systemLog.ListAction.Add(new ActionDetail()
                        {
                            Description = $"Sửa hồ sơ đăng ký tuyển sinh: {model.HoSoTuyenSinh.CMND}, {phuongThucXetTuyen.TenPhuongThucXetTuyen}",
                            ObjectCode = TuyenSinhThiSinhDangKyXetTuyenConstant.CachePrefix,
                            ObjectId = hoSoTuyenSinh.IdHoSo.ToString()
                        });

                        //Xóa cache
                        _cacheService.Remove(TuyenSinhThiSinhDangKyXetTuyenConstant.BuildCacheKey());
                        return Unit.Value;
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new ArgumentException($"{_localizer[$"{ex.Message}"]}");
                    }
                }
            }
        }
    }
    /// <summary>
    /// Biểu mẫu đăng ký xét tuyển
    /// </summary>
    public class ExportBieuMauThiSinhDangKyXetTuyenCommand : IRequest<ExportBieuMauResponse>
    {
        public ExportBieuMauRequest Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public ExportBieuMauThiSinhDangKyXetTuyenCommand(ExportBieuMauRequest model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<ExportBieuMauThiSinhDangKyXetTuyenCommand, ExportBieuMauResponse>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            protected readonly IMediator _mediator;
            private readonly IConfiguration _config;
            public Handler(
                AdmissionDataContext dataContext,
                ICacheService cacheService,
                IStringLocalizer<Resources> localizer,
                IConfiguration config,
                IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _mediator = mediator;
                _config = config;
            }
            public async Task<ExportBieuMauResponse> Handle(ExportBieuMauThiSinhDangKyXetTuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                var response = new ExportBieuMauResponse();
                var idHoSo = model.IdHoSo != 0 ? model.IdHoSo : Int32.Parse(systemLog.UserId);
                var type = await _dataContext.SvTuyenSinhMauPhieus.FirstOrDefaultAsync(x => x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && x.IdHe == model.IdHe);
                switch (type.UrlMauPhieuTuyenSinh)
                {
                    case MauPhieuConstants.XTHB:
                        response.FilePath = _config["Exports:FormXTHBPath"];
                        response.FileName = "XET TUYEN HOC BA_{0}.docx";
                        break;
                    case MauPhieuConstants.XTNL:
                        response.FilePath = _config["Exports:FormXDGNLPath"];
                        response.FileName = "XET TUYEN DANH GIA NANG LUC_{0}.docx";
                        break;
                    case MauPhieuConstants.XTNK:
                        response.FilePath = _config["Exports:FormXTHBPath"];
                        response.FileName = "XET TUYEN THI NANG KHIEU_{0}.docx";
                        break;
                    default:
                        break;
                }

                var data = await (from hs in _dataContext.SvHoSoTuyenSinhs
                                  from dt in _dataContext.SvDanTocs.Where(x => x.IdDanToc == hs.IdDanToc).DefaultIfEmpty()
                                  from ns in _dataContext.SvTinhs.Where(x => x.IdTinh == hs.IdTinhNs).DefaultIfEmpty()
                                  from thpt12 in _dataContext.SvTruongTHPTs.Where(x => x.IdTruongTHPT == hs.IdTruongTHPTLop12).DefaultIfEmpty()
                                  from thpt11 in _dataContext.SvTruongTHPTs.Where(x => x.IdTruongTHPT == hs.IdTruongTHPTLop11).DefaultIfEmpty()
                                  from thpt10 in _dataContext.SvTruongTHPTs.Where(x => x.IdTruongTHPT == hs.IdTruongTHPTLop10).DefaultIfEmpty()
                                  from kv in _dataContext.SvKhuVucs.Where(kv => hs.IdKhuVucTuyenSinh == kv.IdKv).DefaultIfEmpty()
                                  from dtts in _dataContext.SvDoiTuongTuyenSinhs.Where(dtts => hs.IdDoiTuongTS == dtts.IdDoiTuongTuyenSinh).DefaultIfEmpty()
                                  from gt in _dataContext.SvGioiTinhs.Where(x => x.IdGioiTinh == hs.IdGioiTinh).DefaultIfEmpty()
                                  from ttt in _dataContext.SvTinhs.Where(x => x.IdTinh == hs.IdTinhTt).DefaultIfEmpty()
                                  from htt in _dataContext.SvHuyens.Where(x => x.IdHuyen == hs.IdHuyenTt).DefaultIfEmpty()
                                  from xtt in _dataContext.SvXas.Where(x => x.IdXa == hs.XaPhuongTt).DefaultIfEmpty()
                                  where hs.IdHoSo == idHoSo
                                  select new ExportBieuMauHoSoTuyenSinh()
                                  {
                                      MaHoSo = hs.MaHoSo ?? String.Empty,
                                      HoTen = hs.HoTen.ToUpper() ?? String.Empty,
                                      CMND = hs.CMND ?? String.Empty,
                                      KhuVucTuyenSinh = kv.TenKv ?? String.Empty,
                                      MaDoiTuongTS = dtts.MaDoiTuong ?? "xx",
                                      NgaySinh = hs.NgaySinh.ToString("dd/MM/yy"),
                                      GioiTinh = gt.GioiTinh ?? String.Empty,
                                      NgayCapCMND = hs.NgayCapCMND.HasValue ? hs.NgayCapCMND.Value.ToString("dd/MM/yyyy") : String.Empty,
                                      NoiCapCMND = hs.IdNoiCapCMND ?? String.Empty,
                                      Email = hs.Email ?? String.Empty,
                                      DienThoaiCaNhan = hs.DienThoaiCaNhan ?? String.Empty,
                                      IdTinhTt = ttt.MaTinh ?? String.Empty,
                                      IdHuyenTt = htt.IdHuyen ?? String.Empty,
                                      DiaChiTt = hs.DiaChiTt,
                                      XaTt = xtt.TenXa ?? String.Empty,
                                      HuyenTt = htt.TenHuyen ?? String.Empty,
                                      TinhTt = ttt.TenTinh ?? String.Empty,
                                      DanToc = dt.DanToc ?? String.Empty,
                                      HoKhauTtKv1 = hs.HoKhauTtKv1 != null ? "x" : "" ?? String.Empty,
                                      HoKhauTtXaKhoKhan = hs.HoKhauTtXaKhoKhan != null ? "x" : "" ?? String.Empty,
                                      TinhNs = ns.TenTinh ?? String.Empty,
                                      DiaChiBaoTin = hs.DiaChiBaoTin ?? String.Empty,
                                      DiaChiTruongTHPTLop10 = thpt10.DiaChiTruongTHPT ?? String.Empty,
                                      DiaChiTruongTHPTLop11 = thpt11.DiaChiTruongTHPT ?? String.Empty,
                                      DiaChiTruongTHPTLop12 = thpt12.DiaChiTruongTHPT ?? String.Empty,
                                      MaTinhLop10 = thpt10.MaTinhTHPT ?? String.Empty,
                                      MaTinhLop11 = thpt11.MaTinhTHPT ?? String.Empty,
                                      MaTinhLop12 = thpt12.MaTinhTHPT ?? String.Empty,
                                      MaTruongTHPTLop10 = thpt10.MaTruongTHPT ?? String.Empty,
                                      MaTruongTHPTLop11 = thpt11.MaTruongTHPT ?? String.Empty,
                                      MaTruongTHPTLop12 = thpt12.MaTruongTHPT ?? String.Empty,
                                      NamTotNghiep = hs.NamTotNghiep ?? String.Empty,
                                      TenTruongTHPTLop10 = thpt10.TenTruongTHPT ?? String.Empty,
                                      TenTruongTHPTLop11 = thpt11.TenTruongTHPT ?? String.Empty,
                                      TenTruongTHPTLop12 = thpt12.TenTruongTHPT ?? String.Empty
                                  }).FirstOrDefaultAsync();

                var dangKyXetTuyenQuery = _dataContext.SvTuyenSinhDangKyXetTuyens.AsQueryable();
                var ketQuaHocBaQuery = _dataContext.SvTuyenSinhKetQuaHocTapHocBas.AsQueryable();

                if (type != null && type.UrlMauPhieuTuyenSinh == MauPhieuConstants.XTHB)
                {
                    // Nguyện vọng xét tuyển học bạ
                    var query = await (from dt in dangKyXetTuyenQuery
                                       join n in _dataContext.SvNganhs on dt.IdNganh equals n.IdNganh
                                       join th in _dataContext.SvTuyenSinhToHopXetTuyens on dt.IdToHopXetTuyen equals th.IdToHopXetTuyen
                                       join thm in _dataContext.SvTuyenSinhToHopMonXetTuyens on dt.IdToHopXetTuyen equals thm.IdToHopXetTuyen
                                       join m in _dataContext.SvTuyenSinhMonXetTuyens on thm.IdMonXetTuyen equals m.IdMonXetTuyen
                                       join kqhb in ketQuaHocBaQuery on new { dt.IdHoSo, thm.IdMonXetTuyen } equals new { kqhb.IdHoSo, kqhb.IdMonXetTuyen }
                                       where dt.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && dt.IdHoSo == idHoSo
                                       select new
                                       {
                                           dt.IdTuyenSinhDangKyXetTuyen,
                                           n.MaNganh,
                                           n.TenNganh,
                                           dt.ThuTuXet,
                                           th.MaToHop,
                                           Diem = new List<decimal?>() { kqhb.Diem1, kqhb.Diem2, kqhb.Diem3, kqhb.Diem4, kqhb.Diem5, kqhb.Diem6 },
                                           m.TenMonXetTuyen
                                       }).ToListAsync();

                    var group = query.GroupBy(x => x.IdTuyenSinhDangKyXetTuyen);

                    var listDangKyXetTuyen = new List<ExportBieuMauDangKyXetTuyen>();
                    foreach (var item in group)
                    {
                        var monDetail = item.Select(x => new
                        {
                            Diem = Math.Round((x.Diem.Where(x => x.HasValue && x.Value > 0).Sum() ?? 0) / Math.Max(1, x.Diem.Where(x => x.HasValue && x.Value > 0).Count()), 2),
                            TenMon = x.TenMonXetTuyen
                        }).ToList();
                        var listMonDiem = monDetail.Select(x => string.Format("{0}: \n {1}", x.TenMon, x.Diem)).ToList();
                        listDangKyXetTuyen.Add(new ExportBieuMauDangKyXetTuyen()
                        {
                            MaNganh = item.Max(x => x.MaNganh),
                            MaToHop = item.Max(x => x.MaToHop),
                            TenNganh = item.Max(x => x.TenNganh),
                            ThuTuXet = item.Max(x => x.ThuTuXet),
                            Mon1 = listMonDiem.ElementAtOrDefault(0) ?? String.Empty,
                            Mon2 = listMonDiem.ElementAtOrDefault(1) ?? String.Empty,
                            Mon3 = listMonDiem.ElementAtOrDefault(2) ?? String.Empty
                        });
                    }
                    response.DangKyXetTuyens = listDangKyXetTuyen.OrderBy(x => x.ThuTuXet).ToList();
                }

                if (type != null && type.UrlMauPhieuTuyenSinh == MauPhieuConstants.XTNL)
                {
                    // Nguyện vọng xét tuyển đánh giá năng lực HSA/HUST
                    var queryDanhGiaNangLuc = await (from dt in dangKyXetTuyenQuery
                                                     join n in _dataContext.SvNganhs on dt.IdNganh equals n.IdNganh
                                                     join th in _dataContext.SvTuyenSinhToHopXetTuyens on dt.IdToHopXetTuyen equals th.IdToHopXetTuyen
                                                     join thm in _dataContext.SvTuyenSinhToHopMonXetTuyens on dt.IdToHopXetTuyen equals thm.IdToHopXetTuyen
                                                     join m in _dataContext.SvTuyenSinhMonXetTuyens on thm.IdMonXetTuyen equals m.IdMonXetTuyen
                                                     join kq in _dataContext.SvTuyenSinhKetQuaDanhGiaNangLucs on new { dt.IdHoSo, thm.IdMonXetTuyen } equals new { kq.IdHoSo, kq.IdMonXetTuyen }
                                                     where dt.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && dt.IdHoSo == idHoSo
                                                     select new
                                                     {
                                                         dt.IdTuyenSinhDangKyXetTuyen,
                                                         n.MaNganh,
                                                         n.TenNganh,
                                                         dt.ThuTuXet,
                                                         th.MaToHop,
                                                         HustHsa = th.MaToHop == "K00" ? "HUST" : "HSA",
                                                         kq.Diem,
                                                         Mon = $"{m.TenMonXetTuyen}: \n {kq.Diem}"
                                                     }).ToListAsync();

                    var groupDanhGiaNangLuc = queryDanhGiaNangLuc.GroupBy(x => x.IdTuyenSinhDangKyXetTuyen);

                    var listDanhGiaNangLucs = new List<ExportBieuMauDanhGiaNangLuc>();
                    foreach (var item in groupDanhGiaNangLuc)
                    {
                        var listMonDiem = item.Select(x => x.Mon).ToList();
                        var sum = item.Select(x => x.Diem).Sum();
                        listDanhGiaNangLucs.Add(new ExportBieuMauDanhGiaNangLuc()
                        {
                            MaNganh = item.Max(x => x.MaNganh),
                            MaToHop = item.Max(x => x.MaToHop),
                            TenNganh = item.Max(x => x.TenNganh),
                            ThuTuXet = item.Max(x => x.ThuTuXet),
                            HustHsa = item.Max(x => x.HustHsa),
                            Mon1 = listMonDiem.ElementAtOrDefault(0) ?? String.Empty,
                            Mon2 = listMonDiem.ElementAtOrDefault(1) ?? String.Empty,
                            Mon3 = listMonDiem.ElementAtOrDefault(2) ?? String.Empty,
                            TongDiem = sum
                        });
                    }
                    response.DanhGiaNangLucs = listDanhGiaNangLucs.OrderBy(x => x.ThuTuXet).ToList();
                }

                if (type != null && type.UrlMauPhieuTuyenSinh == MauPhieuConstants.XTNK)
                {
                    // Nguyện vọng xét tuyển năng khiếu
                    var queryNangKhieu = await (from dt in dangKyXetTuyenQuery
                                                join n in _dataContext.SvNganhs on dt.IdNganh equals n.IdNganh
                                                join th in _dataContext.SvTuyenSinhToHopXetTuyens on dt.IdToHopXetTuyen equals th.IdToHopXetTuyen
                                                join thm in _dataContext.SvTuyenSinhToHopMonXetTuyens on dt.IdToHopXetTuyen equals thm.IdToHopXetTuyen
                                                join m in _dataContext.SvTuyenSinhMonXetTuyens on thm.IdMonXetTuyen equals m.IdMonXetTuyen
                                                from kq in _dataContext.SvTuyenSinhKetQuaThiNangKhieus.Where(kq => kq.IdHoSo == dt.IdHoSo && kq.IdMonXetTuyen == thm.IdMonXetTuyen).DefaultIfEmpty()
                                                from kqhb in _dataContext.SvTuyenSinhKetQuaHocTapHocBas.Where(hb => hb.IdHoSo == dt.IdHoSo && hb.IdMonXetTuyen == thm.IdMonXetTuyen).DefaultIfEmpty()
                                                where dt.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && dt.IdHoSo == idHoSo
                                                select new
                                                {
                                                    dt.IdTuyenSinhDangKyXetTuyen,
                                                    n.MaNganh,
                                                    n.TenNganh,
                                                    dt.ThuTuXet,
                                                    th.MaToHop,
                                                    Diem = kq.Diem != null ? kq.Diem : 0,
                                                    DiemHocBa = kq.Diem == null ? new List<decimal?>() { kqhb.Diem1, kqhb.Diem2, kqhb.Diem3, kqhb.Diem4, kqhb.Diem5, kqhb.Diem6 } : new List<decimal?>(),
                                                    m.TenMonXetTuyen,
                                                }).ToListAsync();

                    var groupNangKhieu = queryNangKhieu.GroupBy(x => x.IdTuyenSinhDangKyXetTuyen);

                    var listNangKhieus = new List<ExportBieuMauDangKyXetTuyen>();
                    foreach (var item in groupNangKhieu)
                    {
                        var monDetail = item.Select(x => new
                        {
                            Diem = x.Diem != null && x.Diem > 0
                            ? x.Diem
                            : Math.Round(((x.DiemHocBa.Where(x => x.HasValue && x.Value > 0).Sum() ?? 0) / Math.Max(1, x.DiemHocBa.Where(x => x.HasValue && x.Value > 0).Count())), 2),
                            TenMon = x.TenMonXetTuyen
                        }).ToList();
                        var listMonDiem = monDetail.Select(x => string.Format("{0}: \n {1}", x.TenMon, x.Diem)).ToList();
                        listNangKhieus.Add(new ExportBieuMauDangKyXetTuyen()
                        {
                            MaNganh = item.Max(x => x.MaNganh),
                            MaToHop = item.Max(x => x.MaToHop),
                            TenNganh = item.Max(x => x.TenNganh),
                            ThuTuXet = item.Max(x => x.ThuTuXet),
                            Mon1 = listMonDiem.ElementAtOrDefault(0) ?? String.Empty,
                            Mon2 = listMonDiem.ElementAtOrDefault(1) ?? String.Empty,
                            Mon3 = listMonDiem.ElementAtOrDefault(2) ?? String.Empty
                        });
                    }
                    response.NangKhieus = listNangKhieus.OrderBy(x => x.ThuTuXet).ToList();
                }

                var namTuyenSinh = _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.FirstOrDefault(x => x.IdHoSo == idHoSo && x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && x.IdHe == model.IdHe);

                response.HoSoTuyenSinhs = data;
                response.FileName = string.Format(response.FileName ?? String.Empty, data != null ? data.HoTen : String.Empty);
                response.Type = type.UrlMauPhieuTuyenSinh;

                var listKey = new List<KeyValueModel>
                {
                    new KeyValueModel
                    {
                        Key = "{{NamTuyenSinh}}",
                        Value = namTuyenSinh != null ? namTuyenSinh.NamTuyenSinh.ToString() : DateTime.Now.Year.ToString()
                    }
                };
                if (data != null)
                {
                    foreach (var prop in data.GetType().GetProperties())
                    {
                        var key = "{{" + prop.Name + "}}";
                        var value = prop.GetValue(data, null)?.ToString();

                        listKey.Add(new KeyValueModel
                        {
                            Key = key,
                            Value = string.IsNullOrEmpty(value) ? "" : value
                        });
                    }
                }
                else
                {
                    throw new ArgumentException($"{_localizer["HoSoTuyenSinh.NotExisted"]}");
                }
                response.KeyValueModels = listKey;

                return response;
            }

        }
    }
    public class ExportBieuMauCaoHocCommand : IRequest<ExportBieuMauResponse>
    {
        public ExportBieuMauRequest Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public ExportBieuMauCaoHocCommand(ExportBieuMauRequest model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<ExportBieuMauCaoHocCommand, ExportBieuMauResponse>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            protected readonly IMediator _mediator;
            private readonly IConfiguration _config;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IMediator mediator, IConfiguration config)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _mediator = mediator;
                _config = config;
            }
            public async Task<ExportBieuMauResponse> Handle(ExportBieuMauCaoHocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                var response = new ExportBieuMauResponse();
                var idHoSo = model.IdHoSo != 0 ? model.IdHoSo : Int32.Parse(systemLog.UserId);
                var type = await _dataContext.SvTuyenSinhMauPhieus.FirstOrDefaultAsync(x => x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && x.IdHe == model.IdHe);
                switch (type.UrlMauPhieuTuyenSinh)
                {
                    case MauPhieuConstants.XHSCH:
                        response.FilePath = _config["Exports:FormThacSiPath"];
                        response.FileName = "XET TUYEN HO SO CAO HOC_{0}.docx";
                        break;
                    case MauPhieuConstants.XHSTS:
                        response.FilePath = _config["Exports:FormTienSiPath"];
                        response.FileName = "XET TUYEN HO SO TIEN SI_{0}.docx";
                        break;
                    default:
                        break;
                }

                var data = await (from hs in _dataContext.SvHoSoTuyenSinhs
                                  from ns in _dataContext.SvTinhs.Where(x => x.IdTinh == hs.IdTinhNs).DefaultIfEmpty()
                                  from gt in _dataContext.SvGioiTinhs.Where(x => x.IdGioiTinh == hs.IdGioiTinh).DefaultIfEmpty()
                                  from ttt in _dataContext.SvTinhs.Where(x => x.IdTinh == hs.IdTinhTt).DefaultIfEmpty()
                                  from htt in _dataContext.SvHuyens.Where(x => x.IdHuyen == hs.IdHuyenTt).DefaultIfEmpty()
                                  from xtt in _dataContext.SvXas.Where(x => x.IdXa == hs.XaPhuongTt).DefaultIfEmpty()
                                  from n1 in _dataContext.SvNganhs.Where(x => hs.IdNganhBang1.HasValue && x.IdNganh == hs.IdNganhBang1).DefaultIfEmpty()
                                  from n2 in _dataContext.SvNganhs.Where(x => hs.IdNganhBang2.HasValue && x.IdNganh == hs.IdNganhBang2).DefaultIfEmpty()
                                  from dkxt in _dataContext.SvTuyenSinhDangKyXetTuyens.Where(x => x.IdHoSo == hs.IdHoSo && x.IdPhuongThucXetTuyen == type.IdPhuongThucXetTuyen).DefaultIfEmpty()
                                  from n in _dataContext.SvNganhs.Where(x => x.IdNganh == dkxt.IdNganh).DefaultIfEmpty()
                                  from dt in _dataContext.SvDanTocs.Where(x => hs.IdDanToc.HasValue && x.IdDanToc == hs.IdDanToc.Value).DefaultIfEmpty()
                                  from qt in _dataContext.SvQuocTichs.Where(x => hs.IdQuocTich.HasValue && x.IdQuocTich == hs.IdQuocTich.Value).DefaultIfEmpty()
                                  from nts in _dataContext.SvNganhs.Where(x => hs.IdNganhThacSi.HasValue && x.IdNganh == hs.IdNganhThacSi.Value).DefaultIfEmpty()
                                  where hs.IdHoSo == idHoSo
                                  select new ExportBieuMauCaoHoc()
                                  {
                                      HoTen = hs.HoTen.ToUpper() ?? String.Empty,
                                      NgaySinh = hs.NgaySinh.ToString("dd/MM/yyyy"),
                                      GioiTinh = gt.GioiTinh != null && gt.GioiTinh.Equals("nam", StringComparison.CurrentCultureIgnoreCase) ? "0" : "1",
                                      Email = hs.Email ?? String.Empty,
                                      DienThoaiCaNhan = hs.DienThoaiCaNhan ?? String.Empty,
                                      DiaChiTt = hs.DiaChiTt,
                                      XaTt = xtt.TenXa ?? String.Empty,
                                      HuyenTt = htt.TenHuyen ?? String.Empty,
                                      TinhTt = ttt.TenTinh ?? String.Empty,
                                      TinhNs = ns.TenTinh ?? String.Empty,
                                      DiaChiBaoTin = hs.DiaChiBaoTin ?? String.Empty,
                                      NamBatDauCongTac = hs.NamBatDauCongTac ?? String.Empty,
                                      ChucVuDonViCongTac = hs.ChucVuDonViCongTac ?? String.Empty,
                                      DonVi = hs.DonVi ?? String.Empty,
                                      DienThoaiCQ = hs.DienThoaiCQ ?? String.Empty,
                                      DienThoaiNR = hs.DienThoaiNR ?? String.Empty,
                                      HinhThucDaoTaoBang1 = hs.HinhThucDaoTaoBang1 ?? String.Empty,
                                      HinhThucDaoTaoBang2 = hs.HinhThucDaoTaoBang2 ?? String.Empty,
                                      LoaiTotNghiepBang1 = hs.LoaiTotNghiepBang1 ?? String.Empty,
                                      LoaiTotNghiepBang2 = hs.LoaiTotNghiepBang2 ?? String.Empty,
                                      NamTotNghiepBang1 = hs.NamTotNghiepBang1 ?? String.Empty,
                                      NamTotNghiepBang2 = hs.NamTotNghiepBang2 ?? String.Empty,
                                      NganhBang1 = n1 != null ? n1.TenNganh : String.Empty,
                                      NganhBang2 = n2 != null ? n2.TenNganh : String.Empty,
                                      NganhXetTuyen = n != null ? n.TenNganh : String.Empty,
                                      NgheNghiep = hs.NgheNghiep ?? String.Empty,
                                      TenTruongBang1 = hs.TenTruongBang1 ?? String.Empty,
                                      TenTruongBang2 = hs.TenTruongBang2 ?? String.Empty,
                                      ThamNienNgheNghiep = hs.ThamNienNgheNghiep.Value.ToString() ?? String.Empty,
                                      Nam = gt != null && gt.GioiTinh.Equals("nam", StringComparison.CurrentCultureIgnoreCase) ? "x" : String.Empty,
                                      Nu = gt != null && gt.GioiTinh.Equals("nữ", StringComparison.CurrentCultureIgnoreCase) ? "x" : String.Empty,
                                      DanToc = dt != null ? dt.DanToc : String.Empty,
                                      QuocTich = qt != null ? qt.QuocTich : String.Empty,
                                      LinhVucChuyenMon = hs.LinhVucChuyenMon ?? String.Empty,
                                      SoLuongBaiBao = hs.SoLuongBaiBao.HasValue ? hs.SoLuongBaiBao.ToString() : String.Empty,
                                      TenTruongThacSi = hs.TenTruongThacSi ?? String.Empty,
                                      NamTotNghiepThacSi = hs.NamTotNghiepThacSi ?? String.Empty,
                                      NganhThacSi = nts != null ? nts.TenNganh : String.Empty,
                                      HinhThucDaoTaoThacSi = hs.HinhThucDaoTaoThacSi ?? String.Empty,
                                      TrinhDoNgoaiNgu = hs.TrinhDoNgoaiNgu ?? String.Empty,
                                      ChuaHoanThanhBoTuc = hs.BoTucKienThuc.HasValue && hs.BoTucKienThuc.Value == 1 ? "x" : String.Empty,
                                      HoanThanhBoTuc = hs.BoTucKienThuc.HasValue && hs.BoTucKienThuc.Value == 2 ? "x" : String.Empty,
                                      DoiTuongDuThi = hs.DoiTuongDuThi.HasValue ? CheckInputData.CheckDoiTuongDuThi(hs.DoiTuongDuThi.Value) : String.Empty,
                                      LoaiHopDongHienTai = hs.LoaiHopDongHienTai.HasValue ? CheckInputData.CheckLoaiHopDongHienTai(hs.LoaiHopDongHienTai.Value) : String.Empty
                                  }).FirstOrDefaultAsync();

                var namTuyenSinh = _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.FirstOrDefault(x => x.IdHoSo == idHoSo && x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && x.IdHe == model.IdHe);

                response.BieuMauCaoHoc = data;
                response.FileName = string.Format(response.FileName ?? String.Empty, data != null ? data.HoTen : String.Empty);
                response.Type = type.UrlMauPhieuTuyenSinh;

                var listKey = new List<KeyValueModel>()
                {
                     new KeyValueModel
                     {
                         Key = "{{NamTuyenSinh}}",
                         Value = namTuyenSinh != null ? namTuyenSinh.NamTuyenSinh.ToString() : DateTime.Now.Year.ToString()
                     }
                };

                if (data != null)
                {
                    var ngaySinh = data.NgaySinh.Select(x => x.ToString()).ToList();
                    data.NgaySinh1 = ngaySinh.ElementAtOrDefault(0);
                    data.NgaySinh2 = ngaySinh.ElementAtOrDefault(1);
                    data.ThangSinh1 = ngaySinh.ElementAtOrDefault(3);
                    data.ThangSinh2 = ngaySinh.ElementAtOrDefault(4);
                    data.NamSinh1 = ngaySinh.ElementAtOrDefault(6);
                    data.NamSinh2 = ngaySinh.ElementAtOrDefault(7);
                    data.NamSinh3 = ngaySinh.ElementAtOrDefault(8);
                    data.NamSinh4 = ngaySinh.ElementAtOrDefault(9);

                    foreach (var prop in data.GetType().GetProperties())
                    {
                        var key = "{{" + prop.Name + "}}";
                        var value = prop.GetValue(data, null)?.ToString();

                        listKey.Add(new KeyValueModel
                        {
                            Key = key,
                            Value = string.IsNullOrEmpty(value) ? "" : value
                        });
                    }
                }
                else
                {
                    throw new ArgumentException($"{_localizer["HoSoTuyenSinh.NotExisted"]}");
                }
                response.KeyValueModels = listKey;

                return response;
            }

        }
    }
    public static class CheckInputData
    {
        public static bool CheckThuTuNguyenVong(List<int> listThuTus)
        {
            // Kiểm tra nguyện vọng có bắt đầu từ 1
            if (listThuTus.Count == 0 || listThuTus[0] != 1)
            {
                return false;
            }

            // Kiểm tra nguyện vọng có tăng dần theo thứ tự
            for (int i = 1; i < listThuTus.Count; i++)
            {
                if (listThuTus[i] != listThuTus[i - 1] + 1)
                {
                    return false;
                }
            }

            return true;
        }
        public static string CheckDoiTuongDuThi(int doiTuongDuThi)
        {
            return doiTuongDuThi switch
            {
                1 => "Thí sinh tự do",
                2 => "Cơ quan cử",
                3 => "Cơ quan hành chính sự nghiệp",
                4 => "Cơ quan khác",
                _ => String.Empty,
            };
        }
        public static string CheckLoaiHopDongHienTai(int loaiHopDongHienTai)
        {
            return loaiHopDongHienTai switch
            {
                1 => "Cán bộ hợp đồng",
                2 => "Cán bộ biên chế",
                _ => String.Empty,
            };
        }
        public static string GetDonViTinh(string donViTinh)
        {
            if (Enum.TryParse<AdmissionsDonViTinhEnum>(donViTinh, out var enumValue))
            {
                return enumValue.GetDisplayName();
            }

            return string.Empty;
        }
    }
}