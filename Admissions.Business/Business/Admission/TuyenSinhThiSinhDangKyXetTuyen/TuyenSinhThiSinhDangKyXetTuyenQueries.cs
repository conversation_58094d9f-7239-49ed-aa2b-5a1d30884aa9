using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using Admissions.Shared;

namespace Admissions.Business
{
    public class GetTuyenSinhThiSinhDangKyXetTuyenByIdQuery : IRequest<TuyenSinhThiSinhDangKyXetTuyenDetailModel>
    {
        public TuyenSinhThiSinhDangKyXetTuyenFilterModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        /// <summary>
        /// Lấy thông tin đối tượng xét tuyển theo id
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        public GetTuyenSinhThiSinhDangKyXetTuyenByIdQuery(TuyenSinhThiSinhDangKyXetTuyenFilterModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetTuyenSinhThiSinhDangKyXetTuyenByIdQuery, TuyenSinhThiSinhDangKyXetTuyenDetailModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TuyenSinhThiSinhDangKyXetTuyenDetailModel> Handle(GetTuyenSinhThiSinhDangKyXetTuyenByIdQuery request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                var idHoSo = model.IdHoSo > 0 ? model.IdHoSo : Int32.Parse(systemLog.UserId);

                var result = new TuyenSinhThiSinhDangKyXetTuyenDetailModel();
                
                if(model.HaveHoSoTuyenSinh)
                {
                    var hoSoTuyenSinh = await (from dt in _dataContext.SvHoSoTuyenSinhs
                                               join tr10 in _dataContext.SvTruongTHPTs on dt.IdTruongTHPTLop10 equals tr10.IdTruongTHPT into tr10Group
                                               from tr10 in tr10Group.DefaultIfEmpty()
                                               join tr11 in _dataContext.SvTruongTHPTs on dt.IdTruongTHPTLop11 equals tr11.IdTruongTHPT into tr11Group
                                               from tr11 in tr11Group.DefaultIfEmpty()
                                               join tr12 in _dataContext.SvTruongTHPTs on dt.IdTruongTHPTLop12 equals tr12.IdTruongTHPT into tr12Group
                                               from tr12 in tr12Group.DefaultIfEmpty()
                                               where dt.IdHoSo == idHoSo
                                               select new HoSoTuyenSinhDetailModel()
                                               {
                                                   IdHoSo = dt.IdHoSo,
                                                   MaHoSo = dt.MaHoSo,
                                                   NgayCapCMND = dt.NgayCapCMND,
                                                   NamTotNghiep = dt.NamTotNghiep,
                                                   SBD = dt.SBD,
                                                   NgaySinh = dt.NgaySinh,
                                                   XaPhuongTt = dt.XaPhuongTt,
                                                   NamSinhMe = dt.NamSinhMe,
                                                   NamSinhCha = dt.NamSinhCha,
                                                   SoDienThoaiBo = dt.SoDienThoaiBo,
                                                   SoDienThoaiMe = dt.SoDienThoaiMe,
                                                   CMND = dt.CMND,
                                                   DiaChiBaoTin = dt.DiaChiBaoTin,
                                                   DiaChiTt = dt.DiaChiTt,
                                                   DienThoaiCaNhan = dt.DienThoaiCaNhan,
                                                   Email = dt.Email,
                                                   HoKhauTtKv1 = dt.HoKhauTtKv1,
                                                   HoKhauTtXaKhoKhan = dt.HoKhauTtXaKhoKhan,
                                                   HoTen = dt.HoTen,
                                                   HoTenCha = dt.HoTenCha,
                                                   HoTenMe = dt.HoTenMe,
                                                   IdDanToc = dt.IdDanToc,
                                                   IdDoiTuongTS = dt.IdDoiTuongTS,
                                                   IdGioiTinh = dt.IdGioiTinh,
                                                   IdHuyenTt = dt.IdHuyenTt,
                                                   IdKhuVucTuyenSinh = dt.IdKhuVucTuyenSinh,
                                                   IdNoiCapCMND = dt.IdNoiCapCMND,
                                                   IdQuocTich = dt.IdQuocTich,
                                                   IdTinhNs = dt.IdTinhNs,
                                                   IdTinhTt = dt.IdTinhTt,
                                                   IdTruongTHPTLop10 = dt.IdTruongTHPTLop10,
                                                   IdTruongTHPTLop11 = dt.IdTruongTHPTLop11,
                                                   IdTruongTHPTLop12 = dt.IdTruongTHPTLop12,
                                                   IdXepLoaiHanhKiemLop12 = dt.IdXepLoaiHanhKiemLop12,
                                                   IdXepLoaiHocTapLop12 = dt.IdXepLoaiHocTapLop12,
                                                   IdTinhLop10 = tr10.MaTinhTHPT,
                                                   IdTinhLop11 = tr11.MaTinhTHPT,
                                                   IdTinhLop12 = tr12.MaTinhTHPT,
                                                   ChucVuDonViCongTac = dt.ChucVuDonViCongTac,
                                                   DonVi = dt.DonVi,
                                                   DienThoaiNR = dt.DienThoaiNR,
                                                   DienThoaiCQ = dt.DienThoaiCQ,
                                                   NgheNghiep = dt.NgheNghiep,
                                                   NamBatDauCongTac = dt.NamBatDauCongTac,
                                                   LoaiHopDongHienTai = dt.LoaiHopDongHienTai,
                                                   DoiTuongDuThi = dt.DoiTuongDuThi,
                                                   LinhVucChuyenMon = dt.LinhVucChuyenMon,
                                                   ThamNienNgheNghiep = dt.ThamNienNgheNghiep,
                                                   SoLuongBaiBao = dt.SoLuongBaiBao,
                                                   TenTruongBang1 = dt.TenTruongBang1,
                                                   NamTotNghiepBang1 = dt.NamTotNghiepBang1,
                                                   IdNganhBang1 = dt.IdNganhBang1,
                                                   HinhThucDaoTaoBang1 = dt.HinhThucDaoTaoBang1,
                                                   TenTruongBang2 = dt.TenTruongBang2,
                                                   NamTotNghiepBang2 = dt.NamTotNghiepBang2,
                                                   IdNganhBang2 = dt.IdNganhBang2,
                                                   HinhThucDaoTaoBang2 = dt.HinhThucDaoTaoBang2,
                                                   LoaiTotNghiepBang1 = dt.LoaiTotNghiepBang1,
                                                   LoaiTotNghiepBang2 = dt.LoaiTotNghiepBang2,
                                                   TenTruongThacSi = dt.TenTruongThacSi,
                                                   NamTotNghiepThacSi = dt.NamTotNghiepThacSi,
                                                   IdNganhThacSi = dt.IdNganhThacSi,
                                                   HinhThucDaoTaoThacSi = dt.HinhThucDaoTaoThacSi,
                                                   TrinhDoNgoaiNgu = dt.TrinhDoNgoaiNgu,
                                                   BoTucKienThuc = dt.BoTucKienThuc,
                                                   IdXepLoaiHanhKiemLop10 = dt.IdXepLoaiHanhKiemLop10,
                                                   IdXepLoaiHanhKiemLop11 = dt.IdXepLoaiHanhKiemLop11,
                                                   SoDienThoaiNguoiTiepNhan = dt.SoDienThoaiNguoiTiepNhan,
                                                   NguoiTiepNhan = dt.NguoiTiepNhan,
                                                   GhiChuTuyenSinh = dt.GhiChuTuyenSinh,
                                                   DiaChiLienHeBo = dt.DiaChiLienHeBo,
                                                   DiaChiLienHeMe = dt.DiaChiLienHeMe,
                                                   DienThoaiCQBo = dt.DienThoaiCQBo,
                                                   DienThoaiCQMe = dt.DienThoaiCQMe,
                                                   EmailBo = dt.EmailBo,
                                                   EmailMe = dt.EmailMe,
                                                   HoTenNguoiNhan = dt.HoTenNguoiNhan,
                                                   IdAnhThiSinh = dt.IdAnhThiSinh,
                                                   IdLoaiDoiTuong = dt.IdLoaiDoiTuong,
                                                   NgheNghiepCha = dt.NgheNghiepCha,
                                                   NgheNghiepMe = dt.NgheNghiepMe,
                                                   NguoiThamKhao1DiaChi = dt.NguoiThamKhao1DiaChi,
                                                   NguoiThamKhao1DienThoai = dt.NguoiThamKhao1DienThoai,
                                                   NguoiThamKhao1Email = dt.NguoiThamKhao1Email,
                                                   NguoiThamKhao1NgheNghiep = dt.NguoiThamKhao1NgheNghiep,
                                                   NguoiThamKhao1NoiCongTac = dt.NguoiThamKhao1NoiCongTac,
                                                   NguoiThamKhao1HoTen = dt.NguoiThamKhao1HoTen,
                                                   NguoiThamKhao1QuanHe = dt.NguoiThamKhao1QuanHe,
                                                   NguoiThamKhao2DiaChi = dt.NguoiThamKhao2DiaChi,
                                                   NguoiThamKhao2DienThoai = dt.NguoiThamKhao2DienThoai,
                                                   NguoiThamKhao2Email = dt.NguoiThamKhao2Email,
                                                   NguoiThamKhao2HoTen = dt.NguoiThamKhao2HoTen,
                                                   NguoiThamKhao2NgheNghiep = dt.NguoiThamKhao2NgheNghiep,
                                                   NguoiThamKhao2NoiCongTac = dt.NguoiThamKhao2NoiCongTac,
                                                   NguoiThamKhao2QuanHe = dt.NguoiThamKhao2QuanHe,
                                                   SoDienThoaiNguoiNhan = dt.SoDienThoaiNguoiNhan
                                               }).FirstOrDefaultAsync();
                    result.HoSoTuyenSinh = hoSoTuyenSinh;
                }
                
                if (model.HaveTuyenSinhDangKyXetTuyen)
                {
                    var dangKyXetTuyen = await _dataContext.SvTuyenSinhDangKyXetTuyens.Where(x => x.IdHoSo == idHoSo && x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen).OrderBy(x => x.ThuTuXet).ToListAsync();
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhDangKyXetTuyen, TuyenSinhDangKyXetTuyenBaseModel>(dangKyXetTuyen);
                    result.TuyenSinhDangKyXetTuyens = entity;
                }
                
                if (model.HaveKetQuaHocBa)
                {
                    var query = await (from dk in _dataContext.SvTuyenSinhDangKyXetTuyens
                                       join thm in _dataContext.SvTuyenSinhToHopMonXetTuyens on dk.IdToHopXetTuyen equals thm.IdToHopXetTuyen
                                       join m in _dataContext.SvTuyenSinhMonXetTuyens on thm.IdMonXetTuyen equals m.IdMonXetTuyen
                                       join kq in _dataContext.SvTuyenSinhKetQuaHocTapHocBas on thm.IdMonXetTuyen equals kq.IdMonXetTuyen
                                       join ts in _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens on new { dk.IdHoSo, dk.IdPhuongThucXetTuyen, model.IdHe } equals new { ts.IdHoSo, ts.IdPhuongThucXetTuyen, ts.IdHe }
                                       where dk.IdHoSo == idHoSo && kq.IdHoSo == idHoSo && ts.IdHoSo == idHoSo &&  ts.IdHe == model.IdHe && ts.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen
                                       group new {dk, thm, kq, ts, m} by new { thm.IdMonXetTuyen } into g
                                       select new 
                                       {
                                           g.Key.IdMonXetTuyen,
                                           Grouped = g.FirstOrDefault(),
                                           TrangThaiHoSo = g.Max(x => x.ts.TrangThaiHoSo)
                                       }).ToListAsync();

                    var ketQuaHocBa = query.Select(x => new TuyenSinhKetQuaHocTapHocBaDetailModel()
                    {
                        Diem1 = x.Grouped.kq.Diem1,
                        Diem2 = x.Grouped.kq.Diem2,
                        Diem3 = x.Grouped.kq.Diem3,
                        Diem4 = x.Grouped.kq.Diem4,
                        Diem5 = x.Grouped.kq.Diem5,
                        Diem6 = x.Grouped.kq.Diem6,
                        IdHoSo = idHoSo,
                        IdMonXetTuyen = x.IdMonXetTuyen,
                        Edit = x.TrangThaiHoSo == 0 || x.TrangThaiHoSo == 3
                    }).ToList();

                    result.KetQuaHocBas = ketQuaHocBa;
                }

                if (model.HaveHoSoNop)
                {
                    var hoSoNop = await (from dt in _dataContext.SvHoSoNopTuyenSinhs
                                         join hsu in _dataContext.SvHoSoNopTuyenSinhUrls on dt.IdHoSoNopTuyenSinh equals hsu.IdHoSoNopTuyenSinh
                                         join gt in _dataContext.SvLoaiGiayTos on dt.IdGiayTo equals gt.IdGiayTo
                                         where dt.IdHoSo == idHoSo
                                         group new { dt, hsu, gt } by new { dt.IdHoSoNopTuyenSinh, dt.IdGiayTo, dt.IdHoSo } into grouped
                                         select new LoaiGiayToYeuCauResponseModel()
                                         {
                                             Edit = false,
                                             IdGiayTo = grouped.Key.IdGiayTo,
                                             MaGiayTo = grouped.Max(x => x.gt.MaGiayTo),
                                             TenGiayTo = grouped.Max(x => x.gt.TenGiayTo),
                                             GhiChu = grouped.Max(x => x.gt.GhiChu),
                                             SvHoSoNopTuyenSinhUrls = grouped.Select(x => new CreateHoSoNopTuyenSinhUrlModel()
                                             {
                                                 FileName = x.hsu.FileName,
                                                 LoaiFile = x.hsu.LoaiFile,
                                                 UrlGiayTo = x.hsu.UrlGiayTo
                                             }).ToList()
                                         }).ToListAsync();
                    result.HoSoNops = hoSoNop;
                }

                if (model.XetTuyenHocBa)
                {
                    var thiSinhDangKyXetTuyen = await _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.FirstOrDefaultAsync(x => x.IdHoSo == idHoSo && x.IdPhuongAnDiemHocBa > 0);
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhThiSinhDangKyXetTuyen, TuyenSinhThiSinhDangKyXetTuyenBaseModel>(thiSinhDangKyXetTuyen);
                    result.TuyenSinhThiSinhDangKyXetTuyens = new List<TuyenSinhThiSinhDangKyXetTuyenBaseModel>() { entity };
                }

                if (model.HaveTuyenSinhThiSinhDangKyXetTuyen)
                {
                    var thiSinhDangKyXetTuyen = await _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.Where(x => x.IdHoSo == idHoSo && x.IdHe == model.IdHe && x.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen).ToListAsync();
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhThiSinhDangKyXetTuyen, TuyenSinhThiSinhDangKyXetTuyenBaseModel>(thiSinhDangKyXetTuyen);
                    result.TuyenSinhThiSinhDangKyXetTuyens = entity;
                }

                if (model.HaveTuyenSinhDoiTuongXetTuyen)
                {
                    var tuyenSinhDoiTuongXetTuyen = await _dataContext.SvTuyenSinhDoiTuongXetTuyens.Where(x => x.IdHoSo == idHoSo).ToListAsync();
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhDoiTuongXetTuyen, TuyenSinhDoiTuongXetTuyenBaseModel>(tuyenSinhDoiTuongXetTuyen);
                    result.TuyenSinhDoiTuongXetTuyens = entity;
                }

                if (model.HaveTuyenSinhKetQuaThiThpt)
                {
                    var tuyenSinhKetQuaThiThpt = await _dataContext.SvTuyenSinhKetQuaThiTHPTs.Where(x => x.IdHoSo == idHoSo).ToListAsync();
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhKetQuaThiTHPT, TuyenSinhKetQuaThiThptModel>(tuyenSinhKetQuaThiThpt);
                    result.TuyenSinhKetQuaThiThpts = entity;
                }

                if (model.HaveKetQuaDanhGiaNangLuc)
                {
                    var tuyenSinhKetQuaDanhGiaNangLuc = await _dataContext.SvTuyenSinhKetQuaDanhGiaNangLucs.Where(x => x.IdHoSo == idHoSo).ToListAsync();
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhKetQuaDanhGiaNangLuc, TuyenSinhKetQuaDanhGiaNangLucModel>(tuyenSinhKetQuaDanhGiaNangLuc);
                    result.KetQuaDanhGiaNangLucs = entity;
                }

                if (model.HaveKetQuaThiNangKhieu)
                {
                    var tuyenSinhKetQuaThiNangKhieu = await _dataContext.SvTuyenSinhKetQuaThiNangKhieus.Where(x => x.IdHoSo == idHoSo).ToListAsync();
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhKetQuaThiNangKhieu, TuyenSinhKetQuaThiNangKhieuModel>(tuyenSinhKetQuaThiNangKhieu);
                    result.KetQuaThiNangKhieus = entity;
                }

                if (model.HaveKetQuaThacSi)
                {
                    var ketQuaThacSis = await _dataContext.SvTuyenSinhKetQuaThacSis.Where(x => x.IdHoSo == idHoSo).ToListAsync();
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhKetQuaThacSi, TuyenSinhKetQuaThacSiModel>(ketQuaThacSis);
                    result.KetQuaThacSis = entity;
                }

                if (model.HaveHoatDongNgoaiKhoa)
                {
                    var ketQuaThacSis = await _dataContext.SvTuyenSinhHoatDongNgoaiKhoas.Where(x => x.IdHoSo == idHoSo).ToListAsync();
                    var entity = AutoMapperUtils.AutoMap<SvTuyenSinhHoatDongNgoaiKhoa, TuyenSinhHoatDongNgoaiKhoaModel>(ketQuaThacSis);
                    result.TuyenSinhHoatDongNgoaiKhoas = entity;
                }

                return result;
            }
        }
    }

    public class GetTuyenSinhThiSinhDangKyXetTuyenByIdHoSoQuery : IRequest<List<TuyenSinhThiSinhDangKyXetTuyenModel>>
    {
        public int NamTuyenSinh { get; set; }
        public SystemLogModel SystemLog { get; set; }
        /// <summary>
        /// Lấy danh sách đăng ký xét tuyển của thí sinh
        /// </summary>
        public GetTuyenSinhThiSinhDangKyXetTuyenByIdHoSoQuery(SystemLogModel systemLog, int namTuyenSinh = 0)
        {
            SystemLog = systemLog;
            NamTuyenSinh = namTuyenSinh;
        }

        public class Handler : IRequestHandler<GetTuyenSinhThiSinhDangKyXetTuyenByIdHoSoQuery, List<TuyenSinhThiSinhDangKyXetTuyenModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TuyenSinhThiSinhDangKyXetTuyenModel>> Handle(GetTuyenSinhThiSinhDangKyXetTuyenByIdHoSoQuery request, CancellationToken cancellationToken)
            {
                var systemLog = request.SystemLog;
                var namTuyenSinh = request.NamTuyenSinh;
                var idHoSo = Int32.Parse(systemLog.UserId);

                var data = await (from dt in _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens
                                            join ptxt in _dataContext.SvTuyenSinhPhuongThucXetTuyens on dt.IdPhuongThucXetTuyen equals ptxt.IdPhuongThucXetTuyen
                                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                                            join ump in _dataContext.SvTuyenSinhMauPhieus on new { dt.IdPhuongThucXetTuyen, dt.IdHe } equals new { ump.IdPhuongThucXetTuyen, ump.IdHe }
                                            where dt.IdHoSo == idHoSo
                                            select new TuyenSinhThiSinhDangKyXetTuyenModel()
                                            {
                                                IdThiSinhDangKyXetTuyen = dt.IdThiSinhDangKyXetTuyen,
                                                IdHoSo = dt.IdHoSo,
                                                NamTuyenSinh = dt.NamTuyenSinh,
                                                tenHe = he.TenHe,
                                                tenPhuongThuc = ptxt.TenPhuongThucXetTuyen,
                                                TrangThaiHoSo = dt.TrangThaiHoSo,
                                                GhiChuDuyetHoSo = dt.GhiChuDuyetHoSo,
                                                IdHe = dt.IdHe,
                                                IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                                                IdPhuongAnDiemHocBa = dt.IdPhuongAnDiemHocBa,
                                                TrangThaiTaiChinh = dt.TrangThaiTaiChinh,
                                                CreateDate = dt.CreateDate,
                                                KetQuaXetTuyen = dt.KetQuaXetTuyen,
                                                SoTienLePhiPhaiNop = dt.SoTienLePhiPhaiNop,
                                                SoTienLePhiDaNop = dt.SoTienLePhiDaNop,
                                                UrlMauPhieu = ump.UrlMauPhieuTuyenSinh
                                            }).ToListAsync();
                if(namTuyenSinh > 0)
                {
                    data = data.Where(x => x.NamTuyenSinh == namTuyenSinh).ToList();
                }
                return data;
            }
        }
    }
}
