using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhThanhToanBaseModel
    {
        public int IdTuyenSinhThanhToan { get; set; }
        public int? IdHoSo { get; set; }
        public string MaHoSo { get; set; }
        public int? IdHe { get; set; }
        public string TenHe { get; set; }
        public int? NamTuyenSinh { get; set; }
        public DateTime? NgayThangGiaoDich { get; set; }
        public string MaGiaoDich { get; set; }
        public string NoiDung { get; set; }
        public string NganHang { get; set; }
        public decimal? SoTien { get; set; }
        public int? HinhThucThanhToan { get; set; }
        public int? TrangThai { get; set; }
        public DateTime? NgayTao { get; set; }
        public string NguoiTao { get; set; }
        public int? LoaiKhoanThu { get; set; }
        public string HoTen { get; set; }
        public DateTime? NgaySinh { get; set; }
        public string SoBaoDanh { get; set; }
        public string GioiTinh { get; set; }
        public int? LoaiDoiTuong { get; set; }
        public List<int> ListIdDot { get; set; }
    }

    public class TuyenSinhThanhToanModel : TuyenSinhThanhToanBaseModel
    {
        public List<TuyenSinhThanhToanChiTietModel> ChiTiets { get; set; }
        public string SoTaiKhoan { get; set; }
        public string MaNganHang { get; set; }
        public ThanhToanOnlineModel ThanhToanOnline { get; set; }
        public TuyenSinhThanhToanModel()
        {
            ChiTiets = new List<TuyenSinhThanhToanChiTietModel>();
        }
    }

    public class ThanhToanOnlineModel
    {
        public string UrlThanhToan { get; set; }
        public string OrderId { get; set; }
        public string UniCode { get; set; }
        public string SecurityCode { get; set; }
    }
    public class TuyenSinhThanhToanChiTietModel
    {
        public int IdTuyenSinhThanhToanChiTiet { get; set; }
        public int? IdTuyenSinhThanhToan { get; set; }
        public int? IdDot { get; set; }
        public int? IdTuyenSinhKhoanThu { get; set; }
        public string TenKhoanThu { get; set; }
        public string TenDotDangKy { get; set; }
        public int? LoaiKhoanThu { get; set; }
        public int? SoLuong { get; set; }
        public decimal? DonGia { get; set; }
        public string DonViTinh { get; set; }
        public decimal? SoTien { get; set; }
    }
    
    public class UpdateStatusThanhToanModel
    {
        public List<BankTransactionModel> Transactions { get; set; }

        public UpdateStatusThanhToanModel()
        {
            Transactions = new List<BankTransactionModel>();
        }
    }

    public class BankTransactionModel
    {
        public DateTime NgayThangGiaoDich { get; set; }
        public string NganHang { get; set; }
        public string MaGiaoDich { get; set; }
        public string NoiDung { get; set; }
    }

    public class UpdateStatusThanhToanResult
    {
        public string Message { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<BankTransactionFailureModel> FailedTransactions { get; set; }
    }

    public class BankTransactionFailureModel
    {
        public DateTime NgayThangGiaoDich { get; set; }
        public string MaGiaoDich { get; set; }
        public string NoiDung { get; set; }
        public string ErrorMessage { get; set; }
    }
    
    public class TuyenSinhThanhToanFilterModel : BaseQueryFilterModel
    {
        public TuyenSinhThanhToanFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdTuyenSinhThanhToan";
        }
        public int? LoaiKhoanThu { get; set; }
        public int? IdHe { get; set; }
        public int? NamTuyenSinh { get; set; }
        public int? TrangThai { get; set; }
        public DateTime? TuNgay { get; set; }
        public DateTime? DenNgay { get; set; }
        public int? IdDotDangKy { get; set; }
        public int? LoaiDoiTuong { get; set; }
    }
}
