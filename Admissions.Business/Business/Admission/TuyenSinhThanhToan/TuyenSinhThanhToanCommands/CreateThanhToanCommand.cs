using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Admissions.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;

namespace Admissions.Business
{
    /// <summary>
    /// Command để tạo thanh toán mới cho hệ thống tuyển sinh
    /// </summary>
    public class CreateThanhToanCommand : IRequest<CreateThanhToanResult>
    {
        public CreateThanhToanModel Model { get; set; }

        public CreateThanhToanCommand(CreateThanhToanModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateThanhToanCommand, CreateThanhToanResult>
        {
            private readonly Data.AdmissionDataContext _dataContext;
            private readonly IStringLocalizer<AdmissionResources> _localizer;

            public Handler(Data.AdmissionDataContext dataContext, IStringLocalizer<AdmissionResources> localizer)
            {
                _dataContext = dataContext;
                _localizer = localizer;
            }

            public async Task<CreateThanhToanResult> Handle(CreateThanhToanCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create payment {TuyenSinhThanhToanConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                // Validate input data
                if (model == null)
                    throw new ArgumentException("Dữ liệu thanh toán không được để trống");
                if (model.IdHoSo <= 0)
                    throw new ArgumentException("ID hồ sơ không hợp lệ");
                if (model.IdHe <= 0)
                    throw new ArgumentException("ID hệ không hợp lệ");
                if (model.NamTuyenSinh <= 0)
                    throw new ArgumentException("Năm tuyển sinh không hợp lệ");
                if (model.ChiTiets == null || !model.ChiTiets.Any())
                    throw new ArgumentException("Danh sách chi tiết thanh toán không được để trống");

                using var transaction = await _dataContext.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    // Kiểm tra hồ sơ tuyển sinh tồn tại
                    var hoSoTuyenSinh = await _dataContext.SvHoSoTuyenSinhs
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.IdHoSo == model.IdHoSo, cancellationToken);
                    if (hoSoTuyenSinh == null)
                        throw new ArgumentException("Hồ sơ tuyển sinh không tồn tại");
                    
                    // Validate các khoản thu và chi tiết
                    var idKhoanThus = model.ChiTiets.Select(x => x.IdTuyenSinhKhoanThu).Distinct().ToList();
                    var khoanThus = await _dataContext.SvTuyenSinhKhoanThus
                        .AsNoTracking()
                        .Where(x => idKhoanThus.Contains(x.IdTuyenSinhKhoanThu))
                        .ToListAsync(cancellationToken);
                    if (khoanThus.Count != idKhoanThus.Count)
                        throw new ArgumentException("Một số khoản thu không tồn tại trong hệ thống");

                    foreach (var chiTiet in model.ChiTiets)
                    {
                        if (chiTiet.IdTuyenSinhKhoanThu <= 0)
                            throw new ArgumentException("ID khoản thu không hợp lệ");
                        if (chiTiet.SoLuong <= 0)
                            throw new ArgumentException("Số lượng phải lớn hơn 0");
                        if (chiTiet.DonGia <= 0)
                            throw new ArgumentException("Đơn giá phải lớn hơn 0");
                    }

                    // Tạo thanh toán chính
                    var payment = new Data.SvTuyenSinhThanhToan
                    {
                        IdHoSo = model.IdHoSo,
                        IdHe = model.IdHe,
                        NamTuyenSinh = model.NamTuyenSinh,
                        HinhThucThanhToan = model.HinhThucThanhToan,
                        TrangThai = 0, // Chưa thanh toán
                        SoTien = 0, // Sẽ được cập nhật sau
                        NgayTao = DateTime.Now,
                        NguoiTao = model.NguoiTao,
                        NoiDung = $"TS{model.IdHoSo}{hoSoTuyenSinh.MaHoSo}{hoSoTuyenSinh.HoTen?.Replace(" ", "")}"
                    };

                    await _dataContext.SvTuyenSinhThanhToans.AddAsync(payment, cancellationToken);
                    await _dataContext.SaveChangesAsync(cancellationToken); // Lưu để lấy ID

                    // Cập nhật nội dung thanh toán với ID đã có
                    payment.NoiDung = $"TS{payment.IdTuyenSinhThanhToan}{hoSoTuyenSinh.MaHoSo}{hoSoTuyenSinh.HoTen?.Replace(" ", "")}";

                    // Tạo chi tiết thanh toán và tính tổng tiền
                    decimal totalAmount = 0;
                    var paymentDetails = new List<SvTuyenSinhThanhToanChiTiet>();

                    foreach (var chiTiet in model.ChiTiets)
                    {
                        var khoanThu = khoanThus.First(x => x.IdTuyenSinhKhoanThu == chiTiet.IdTuyenSinhKhoanThu);
                        var soTien = chiTiet.SoLuong * chiTiet.DonGia;
                        totalAmount += soTien;

                        var paymentDetail = new SvTuyenSinhThanhToanChiTiet
                        {
                            IdTuyenSinhThanhToan = payment.IdTuyenSinhThanhToan,
                            IdTuyenSinhKhoanThu = chiTiet.IdTuyenSinhKhoanThu,
                            IdDot = chiTiet.IdDot,
                            SoLuong = chiTiet.SoLuong,
                            DonGia = chiTiet.DonGia,
                            DonViTinh = khoanThu.DonViTinh,
                            SoTien = soTien
                        };
                        paymentDetails.Add(paymentDetail);
                    }

                    await _dataContext.SvTuyenSinhThanhToanChiTiets.AddRangeAsync(paymentDetails, cancellationToken);
                    payment.SoTien = totalAmount;

                    // Lưu tất cả thay đổi
                    await _dataContext.SaveChangesAsync(cancellationToken);
                    await transaction.CommitAsync(cancellationToken);

                    Log.Information($"Successfully created payment with ID: {payment.IdTuyenSinhThanhToan}");

                    return new CreateThanhToanResult
                    {
                        IdTuyenSinhThanhToan = payment.IdTuyenSinhThanhToan,
                        SoTien = payment.SoTien ?? 0,
                        Message = "Tạo thanh toán thành công"
                    };
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync(cancellationToken);
                    Log.Error(ex, $"Error creating payment: {ex.Message}");
                    throw new ArgumentException($"{_localizer[ex.Message]}");
                }
            }
        }
        
        public static string HashMd5(string sInput)
        {
            ASCIIEncoding aSCIIEncoding = new ASCIIEncoding();
            byte[] bytes = aSCIIEncoding.GetBytes(sInput);
            
            HashAlgorithm hashAlgorithm  = new MD5CryptoServiceProvider();
            byte[] array = hashAlgorithm.ComputeHash(bytes);
            StringBuilder stringBuilder = new StringBuilder();
            byte[] array2 = array;
            foreach (byte b in array2)
            {
                stringBuilder.AppendFormat("{0:x2}", b);
            }

            return stringBuilder.ToString();
        }
    }
}
