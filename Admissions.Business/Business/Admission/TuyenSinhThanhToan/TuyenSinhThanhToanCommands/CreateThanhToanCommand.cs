using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Admissions.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;

namespace Admissions.Business
{
    /// <summary>
    /// Command để tạo thanh toán mới cho hệ thống tuyển sinh
    /// </summary>
    public class CreateThanhToanCommand : IRequest<CreateThanhToanResult>
    {
        public CreateThanhToanModel Model { get; set; }

        public CreateThanhToanCommand(CreateThanhToanModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateThanhToanCommand, CreateThanhToanResult>
        {
            private readonly Data.AdmissionDataContext _dataContext;
            private readonly IStringLocalizer<AdmissionResources> _localizer;

            public Handler(Data.AdmissionDataContext dataContext, IStringLocalizer<AdmissionResources> localizer)
            {
                _dataContext = dataContext;
                _localizer = localizer;
            }

            public async Task<CreateThanhToanResult> Handle(CreateThanhToanCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create payment {TuyenSinhThanhToanConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                // Validate input data
                await ValidateInputDataAsync(model);

                using var transaction = await _dataContext.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    // Kiểm tra hồ sơ tuyển sinh tồn tại
                    var hoSoTuyenSinh = await ValidateHoSoTuyenSinhAsync(model.IdHoSo);

                    // Kiểm tra không tạo trùng thanh toán
                    await ValidateDuplicatePaymentAsync(model);

                    // Validate các khoản thu
                    var validatedFeeItems = await ValidateFeeItemsAsync(model.ChiTiets);

                    // Tạo thanh toán chính
                    var payment = await CreateMainPaymentAsync(model, hoSoTuyenSinh);

                    // Cập nhật nội dung thanh toán với ID đã có
                    payment.NoiDung = GeneratePaymentContentWithId(payment.IdTuyenSinhThanhToan, model, hoSoTuyenSinh);

                    // Tạo chi tiết thanh toán
                    var paymentDetails = await CreatePaymentDetailsAsync(model.ChiTiets, payment.IdTuyenSinhThanhToan, validatedFeeItems);

                    // Cập nhật tổng tiền thanh toán
                    await UpdatePaymentTotalAmountAsync(payment, paymentDetails);

                    // Lưu tất cả thay đổi
                    await _dataContext.SaveChangesAsync(cancellationToken);
                    await transaction.CommitAsync(cancellationToken);

                    Log.Information($"Successfully created payment with ID: {payment.IdTuyenSinhThanhToan}");

                    return new CreateThanhToanResult
                    {
                        IdTuyenSinhThanhToan = payment.IdTuyenSinhThanhToan,
                        SoTien = payment.SoTien ?? 0,
                        Message = "Tạo thanh toán thành công"
                    };
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync(cancellationToken);
                    Log.Error(ex, $"Error creating payment: {ex.Message}");
                    throw new ArgumentException($"{_localizer[ex.Message]}");
                }
            }

            /// <summary>
            /// Validate dữ liệu đầu vào
            /// </summary>
            private async Task ValidateInputDataAsync(CreateThanhToanModel model)
            {
                if (model == null)
                    throw new ArgumentException("Dữ liệu thanh toán không được để trống");

                if (model.IdHoSo <= 0)
                    throw new ArgumentException("ID hồ sơ không hợp lệ");

                if (model.IdHe <= 0)
                    throw new ArgumentException("ID hệ không hợp lệ");

                if (model.NamTuyenSinh <= 0)
                    throw new ArgumentException("Năm tuyển sinh không hợp lệ");

                if (model.ChiTiets == null || !model.ChiTiets.Any())
                    throw new ArgumentException("Danh sách chi tiết thanh toán không được để trống");

                // Validate từng chi tiết thanh toán
                foreach (var chiTiet in model.ChiTiets)
                {
                    if (chiTiet.IdTuyenSinhKhoanThu <= 0)
                        throw new ArgumentException("ID khoản thu không hợp lệ");

                    if (chiTiet.SoLuong <= 0)
                        throw new ArgumentException("Số lượng phải lớn hơn 0");

                    if (chiTiet.DonGia <= 0)
                        throw new ArgumentException("Đơn giá phải lớn hơn 0");
                }

                await Task.CompletedTask;
            }

            /// <summary>
            /// Kiểm tra hồ sơ tuyển sinh tồn tại
            /// </summary>
            private async Task<Data.svHoSoTuyenSinh> ValidateHoSoTuyenSinhAsync(int idHoSo)
            {
                var hoSo = await _dataContext.SvHoSoTuyenSinhs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.IdHoSo == idHoSo);

                if (hoSo == null)
                    throw new ArgumentException("Hồ sơ tuyển sinh không tồn tại");

                return hoSo;
            }

            /// <summary>
            /// Kiểm tra không tạo trùng thanh toán
            /// </summary>
            private async Task ValidateDuplicatePaymentAsync(CreateThanhToanModel model)
            {
                var existingPayment = await _dataContext.SvTuyenSinhThanhToans
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.IdHoSo == model.IdHoSo
                                           && x.IdHe == model.IdHe
                                           && x.NamTuyenSinh == model.NamTuyenSinh
                                           && x.TrangThai == 0); // Chưa thanh toán

                if (existingPayment != null)
                    throw new ArgumentException("Đã tồn tại thanh toán chưa hoàn thành cho hồ sơ này");
            }

            /// <summary>
            /// Validate các khoản thu hợp lệ
            /// </summary>
            private async Task<List<Data.SvTuyenSinhKhoanThu>> ValidateFeeItemsAsync(List<CreateThanhToanChiTietModel> chiTiets)
            {
                var idKhoanThus = chiTiets.Select(x => x.IdTuyenSinhKhoanThu).Distinct().ToList();

                var khoanThus = await _dataContext.SvTuyenSinhKhoanThus
                    .AsNoTracking()
                    .Where(x => idKhoanThus.Contains(x.IdTuyenSinhKhoanThu))
                    .ToListAsync();

                if (khoanThus.Count != idKhoanThus.Count)
                    throw new ArgumentException("Một số khoản thu không tồn tại trong hệ thống");

                return khoanThus;
            }

            /// <summary>
            /// Tạo thanh toán chính
            /// </summary>
            private async Task<Data.SvTuyenSinhThanhToan> CreateMainPaymentAsync(CreateThanhToanModel model, Data.svHoSoTuyenSinh hoSo)
            {
                var payment = new Data.SvTuyenSinhThanhToan
                {
                    IdHoSo = model.IdHoSo,
                    IdHe = model.IdHe,
                    NamTuyenSinh = model.NamTuyenSinh,
                    HinhThucThanhToan = model.HinhThucThanhToan,
                    TrangThai = 0, // Chưa thanh toán
                    SoTien = 0, // Sẽ được cập nhật sau
                    NgayTao = DateTime.Now,
                    NguoiTao = model.NguoiTao,
                    NoiDung = GeneratePaymentContent(model, hoSo)
                };

                await _dataContext.SvTuyenSinhThanhToans.AddAsync(payment);
                await _dataContext.SaveChangesAsync(); // Lưu để lấy ID

                return payment;
            }

            /// <summary>
            /// Tạo chi tiết thanh toán
            /// </summary>
            private async Task<List<Data.SvTuyenSinhThanhToanChiTiet>> CreatePaymentDetailsAsync(
                List<CreateThanhToanChiTietModel> chiTietModels,
                int idTuyenSinhThanhToan,
                List<Data.SvTuyenSinhKhoanThu> validatedFeeItems)
            {
                var paymentDetails = new List<Data.SvTuyenSinhThanhToanChiTiet>();

                foreach (var chiTiet in chiTietModels)
                {
                    var khoanThu = validatedFeeItems.First(x => x.IdTuyenSinhKhoanThu == chiTiet.IdTuyenSinhKhoanThu);

                    var paymentDetail = new Data.SvTuyenSinhThanhToanChiTiet
                    {
                        IdTuyenSinhThanhToan = idTuyenSinhThanhToan,
                        IdTuyenSinhKhoanThu = chiTiet.IdTuyenSinhKhoanThu,
                        IdDot = chiTiet.IdDot,
                        SoLuong = chiTiet.SoLuong,
                        DonGia = chiTiet.DonGia,
                        DonViTinh = khoanThu.DonViTinh,
                        SoTien = chiTiet.SoLuong * chiTiet.DonGia
                    };

                    paymentDetails.Add(paymentDetail);
                }

                await _dataContext.SvTuyenSinhThanhToanChiTiets.AddRangeAsync(paymentDetails);
                return paymentDetails;
            }

            /// <summary>
            /// Cập nhật tổng tiền thanh toán
            /// </summary>
            private async Task UpdatePaymentTotalAmountAsync(Data.SvTuyenSinhThanhToan payment, List<Data.SvTuyenSinhThanhToanChiTiet> paymentDetails)
            {
                var totalAmount = paymentDetails.Sum(x => x.SoTien ?? 0);
                payment.SoTien = totalAmount;

                await Task.CompletedTask;
            }

            /// <summary>
            /// Tạo nội dung thanh toán
            /// </summary>
            private string GeneratePaymentContent(CreateThanhToanModel model, Data.svHoSoTuyenSinh hoSo)
            {
                // Format tạm thời, sẽ được cập nhật sau khi có ID thanh toán
                return $"TS{model.IdHoSo}{hoSo.MaHoSo}{hoSo.HoTen?.Replace(" ", "")}";
            }

            /// <summary>
            /// Tạo nội dung thanh toán với ID thanh toán
            /// </summary>
            private string GeneratePaymentContentWithId(int idThanhToan, CreateThanhToanModel model, Data.svHoSoTuyenSinh hoSo)
            {
                // Format: TS{IdThanhToan}{MaHoSo}{HoTen}
                return $"TS{idThanhToan}{hoSo.MaHoSo}{hoSo.HoTen?.Replace(" ", "")}";
            }
        }
    }
}
