using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Admissions.Shared;
using Core.Business;
using Core.Business.System;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Admissions.Business
{
    public class GetThanhToanThiSinhQuery : IRequest<List<TuyenSinhThanhToanChiTietModel>>
    {
        public int NamTuyenSinh { get; set; }
        public string MaHe { get; set; }
        /// <summary>
        ///  Lấy khoản thu thí sinh
        /// </summary>
        /// <param name="namTuyenSinh">năm tuyển sinh</param>
        /// <param name="maHe">mã hệ</param>
        public GetThanhToanThiSinhQuery(int namTuyenSinh, string maHe)
        {
            NamTuyenSinh = namTuyenSinh;
            MaHe = maHe;
        }

        public class Handler : IRequestHandler<GetThanhToanThiSinhQuery, List<TuyenSinhThanhToanChiTietModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IContextAccessor _contextAccessor;
            private readonly IMediator _mediator;
            private readonly IConfiguration _config;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService, Func<IContextAccessor> contextAccessorFactory, IMediator mediator, IConfiguration config)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _contextAccessor = contextAccessorFactory();
                _mediator = mediator;
                _config = config;
            }

            public async Task<List<TuyenSinhThanhToanChiTietModel>> Handle(GetThanhToanThiSinhQuery request, CancellationToken cancellationToken)
            {
                var lstHe = await _mediator.Send(new GetComboboxHeQuery());
                var idHe = lstHe.FirstOrDefault(x => x.MaHe == request.MaHe)?.IdHe ?? 0;
                
                var nguyenVongThiSinhs = await _dataContext.SvTuyenSinhDangKyXetTuyens.Where(x => x.IdHoSo == _contextAccessor.UserId).ToListAsync();
                
                var result = await (from dt in _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens
                    join d in _dataContext.SvTuyenSinhDotDangKys on new {dt.IdHe, dt.NamTuyenSinh} equals new {d.IdHe, d.NamTuyenSinh}
                    join kt in _dataContext.SvTuyenSinhKhoanThus on d.IdDotDangKy equals kt.IdDotDangKy
                    where dt.IdHoSo == _contextAccessor.UserId && dt.NamTuyenSinh == request.NamTuyenSinh && dt.IdHe == idHe
                    select new TuyenSinhThanhToanChiTietModel
                    {
                        IdTuyenSinhKhoanThu = kt.IdTuyenSinhKhoanThu,
                        TenKhoanThu = kt.TenKhoanThu,
                        SoLuong = kt.DonViTinh == AdmissionsDonViTinhEnum.HoSo.ToString() ? 1 :  nguyenVongThiSinhs.Select(x => x.IdPhuongThucXetTuyen == dt.IdPhuongThucXetTuyen).Count(),
                        DonGia = kt.SoTien,
                        LoaiKhoanThu = kt.LoaiKhoanThu,
                        TenDotDangKy = d.TenDotDangKy,
                        DonViTinh = kt.DonViTinh
                    }).ToListAsync();
                
                result.ForEach(x => x.SoTien = x.DonGia * x.SoLuong);
                
                return result;
            }
        }
        public static string HashMd5(string sInput)
        {
            ASCIIEncoding aSCIIEncoding = new ASCIIEncoding();
            byte[] bytes = aSCIIEncoding.GetBytes(sInput);
            
            HashAlgorithm hashAlgorithm  = new MD5CryptoServiceProvider();
            byte[] array = hashAlgorithm.ComputeHash(bytes);
            StringBuilder stringBuilder = new StringBuilder();
            byte[] array2 = array;
            foreach (byte b in array2)
            {
                stringBuilder.AppendFormat("{0:x2}", b);
            }

            return stringBuilder.ToString();
        }
    }
}
