using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Admissions.Business
{
    public class GetTuyenSinhThanhToanByIdQuery : IRequest<TuyenSinhThanhToanModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin thanh toán theo id
        /// </summary>
        /// <param name="id">Id thanh toán</param>
        public GetTuyenSinhThanhToanByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTuyenSinhThanhToanByIdQuery, TuyenSinhThanhToanModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TuyenSinhThanhToanModel> Handle(GetTuyenSinhThanhToanByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = TuyenSinhThanhToanConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    // Get main payment record
                    var entity = await _dataContext.SvTuyenSinhThanhToans
                        .AsNoTracking()
                        .Where(x => x.IdTuyenSinhThanhToan == id)
                        .Select(tt => new TuyenSinhThanhToanModel
                        {
                            IdTuyenSinhThanhToan = tt.IdTuyenSinhThanhToan,
                            IdHoSo = tt.IdHoSo,
                            IdHe = tt.IdHe,
                            NamTuyenSinh = tt.NamTuyenSinh,
                            NgayThangGiaoDich = tt.NgayThangGiaoDich,
                            MaGiaoDich = tt.MaGiaoDich,
                            NoiDung = tt.NoiDung,
                            NganHang = tt.NganHang,
                            SoTien = tt.SoTien,
                            HinhThucThanhToan = tt.HinhThucThanhToan
                        })
                        .FirstOrDefaultAsync();

                    if (entity != null)
                    {
                        // Get payment details
                        entity.ChiTiets = await _dataContext.SvTuyenSinhThanhToanChiTiets
                            .AsNoTracking()
                            .Where(ct => ct.IdTuyenSinhThanhToan == id)
                            .Join(_dataContext.SvTuyenSinhKhoanThus,
                                  ct => ct.IdTuyenSinhKhoanThu,
                                  kt => kt.IdTuyenSinhKhoanThu,
                                  (ct, kt) => new TuyenSinhThanhToanChiTietModel
                                  {
                                      IdTuyenSinhThanhToanChiTiet = ct.IdTuyenSinhThanhToanChiTiet,
                                      IdTuyenSinhThanhToan = ct.IdTuyenSinhThanhToan,
                                      IdDot = ct.IdDot,
                                      IdTuyenSinhKhoanThu = ct.IdTuyenSinhKhoanThu,
                                      TenKhoanThu = kt.TenKhoanThu,
                                      LoaiKhoanThu = kt.LoaiKhoanThu,
                                      SoLuong = ct.SoLuong,
                                      DonGia = ct.DonGia,
                                      DonViTinh = ct.DonViTinh,
                                      SoTien = ct.SoTien
                                  })
                            .ToListAsync();
                    }

                    return entity;
                });
                return item;
            }
        }
    }
}
