using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Admissions.Business
{
    public class GetFilterTuyenSinhThanhToanQuery : IRequest<PaginationList<TuyenSinhThanhToanBaseModel>>
    {
        public TuyenSinhThanhToanFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách thanh toán có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTuyenSinhThanhToanQuery(TuyenSinhThanhToanFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTuyenSinhThanhToanQuery, PaginationList<TuyenSinhThanhToanBaseModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TuyenSinhThanhToanBaseModel>> Handle(GetFilterTuyenSinhThanhToanQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;
                
                var thanhToanChiTiets = (from dt in _dataContext.SvTuyenSinhThanhToanChiTiets
                                        join kt in _dataContext.SvTuyenSinhKhoanThus on dt.IdTuyenSinhKhoanThu equals kt.IdTuyenSinhKhoanThu
                                        select new
                                        {
                                            dt.IdTuyenSinhThanhToan,
                                            kt.LoaiKhoanThu,
                                            dt.IdDot
                                        }).AsNoTracking();
                
                var data = (from tt in _dataContext.SvTuyenSinhThanhToans
                            join hs in _dataContext.SvHoSoTuyenSinhs on tt.IdHoSo equals hs.IdHoSo 
                            from gt in _dataContext.SvGioiTinhs.Where(x => x.IdGioiTinh == hs.IdGioiTinh).DefaultIfEmpty()
                            from he in _dataContext.SvHes.Where(x => x.IdHe == tt.IdHe).DefaultIfEmpty()
                            select new TuyenSinhThanhToanBaseModel
                            {
                                IdTuyenSinhThanhToan = tt.IdTuyenSinhThanhToan,
                                IdHoSo = tt.IdHoSo,
                                MaHoSo = hs.MaHoSo,
                                HoTen = hs.HoTen,
                                NgaySinh = hs.NgaySinh,
                                GioiTinh = gt != null ? gt.GioiTinh : string.Empty,
                                IdHe = tt.IdHe,
                                LoaiDoiTuong = hs.IdLoaiDoiTuong,
                                TenHe = he != null ? he.TenHe : "",
                                NamTuyenSinh = tt.NamTuyenSinh,
                                NgayThangGiaoDich = tt.NgayThangGiaoDich,
                                MaGiaoDich = tt.MaGiaoDich,
                                NoiDung = tt.NoiDung,
                                NganHang = tt.NganHang,
                                SoTien = tt.SoTien,
                                HinhThucThanhToan = tt.HinhThucThanhToan,
                                TrangThai = tt.TrangThai,
                                NgayTao = tt.NgayTao,
                                NguoiTao = tt.NguoiTao
                            });
                
                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.HoTen.ToLower().Contains(ts) 
                                        || x.MaHoSo.ToLower().Contains(ts));
                }
                
                // Filter by IdLoaiDoiTuong
                if (filter.LoaiDoiTuong.HasValue)
                {
                    data = data.Where(x => x.LoaiDoiTuong == filter.LoaiDoiTuong.Value);
                }
                
                // Filter by IdDotDangKy
                if (filter.IdDotDangKy.HasValue)
                {
                    var lstIdThanhToan = thanhToanChiTiets.Where(x => x.IdDot == filter.IdDotDangKy.Value).Select(x => x.IdTuyenSinhThanhToan).Distinct().ToList();
                    data = data.Where(x => lstIdThanhToan.Contains(x.IdTuyenSinhThanhToan));
                }
                
                // Filter by LoaiKhoanThu
                if (filter.LoaiKhoanThu.HasValue)
                {
                    var lstIdThanhToan = thanhToanChiTiets.Where(x => x.LoaiKhoanThu == filter.LoaiKhoanThu.Value).Select(x => x.IdTuyenSinhThanhToan).Distinct().ToList();
                    data = data.Where(x => lstIdThanhToan.Contains(x.IdTuyenSinhThanhToan));
                }
                
                // Filter by IdHe
                if (filter.IdHe.HasValue)
                {
                    data = data.Where(x => x.IdHe == filter.IdHe.Value);
                }

                // Filter by NamTuyenSinh
                if (filter.NamTuyenSinh.HasValue)
                {
                    data = data.Where(x => x.NamTuyenSinh == filter.NamTuyenSinh.Value);
                }

                // Filter by TrangThai
                if (filter.TrangThai.HasValue)
                {
                    data = data.Where(x => x.TrangThai == filter.TrangThai.Value);
                }
                
                // Filter by NgayThangGiaoDich - TuNgay
                if (filter.TuNgay.HasValue)
                {
                    data = data.Where(x => x.NgayThangGiaoDich >= filter.TuNgay.Value);
                }

                // Filter by NgayThangGiaoDich - DenNgay
                if (filter.DenNgay.HasValue)
                {
                    // Lấy đến hết ngày kết thúc (23:59:59)
                    var denNgayEndOfDay = filter.DenNgay.Value.Date.AddDays(1).AddTicks(-1);
                    data = data.Where(x => x.NgayThangGiaoDich <= denNgayEndOfDay);
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();


                #region gán loại khoản thu
                listData.ForEach(x =>
                {
                    x.LoaiKhoanThu = thanhToanChiTiets.FirstOrDefault(x => x.IdTuyenSinhThanhToan == x.IdTuyenSinhThanhToan)?.LoaiKhoanThu;
                });
                #endregion

                return new PaginationList<TuyenSinhThanhToanBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }
}
