using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using System.Diagnostics;

namespace Admissions.Business
{
    public class GetFilterLoaiGiayToQuery : IRequest<PaginationList<LoaiGiayToBaseModel>>
    {
        public LoaiGiayToFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách giấy theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiGiayToQuery(LoaiGiayToFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiGiayToQuery, PaginationList<LoaiGiayToBaseModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiGiayToBaseModel>> Handle(GetFilterLoaiGiayToQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvLoaiGiayTos
                            where dt.TuyenSinh == true
                            select new LoaiGiayToBaseModel
                            {
                                IdGiayTo = dt.IdGiayTo, 
                                BatBuoc = dt.BatBuoc,
                                GhiChu = dt.GhiChu,
                                IdHe = dt.IdHe,
                                TuyenSinh = dt.TuyenSinh,
                                MaGiayTo = dt.MaGiayTo,
                                TenGiayTo = dt.TenGiayTo,
                                STT = dt.STT,
                                MacDinh = dt.MacDinh,
                                Nhom = dt.Nhom
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenGiayTo.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<LoaiGiayToBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiGiayToByIdQuery : IRequest<LoaiGiayToModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin giấy tờ theo id
        /// </summary>
        /// <param name="id">Id giấy tờ</param>
        public GetLoaiGiayToByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiGiayToByIdQuery, LoaiGiayToModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiGiayToModel> Handle(GetLoaiGiayToByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiGiayToConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvLoaiGiayTos.FirstOrDefaultAsync(x => x.IdGiayTo == id && x.TuyenSinh == true);

                    return AutoMapperUtils.AutoMap<SvLoaiGiayTo, LoaiGiayToModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetComboboxLoaiGiayToQuery : IRequest<List<LoaiGiayToSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy giấy tờ cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiGiayToQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiGiayToQuery, List<LoaiGiayToSelectItemModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiGiayToSelectItemModel>> Handle(GetComboboxLoaiGiayToQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiGiayToConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvLoaiGiayTos.OrderBy(x => x.TenGiayTo)
                                where dt.TuyenSinh == true
                                select new LoaiGiayToSelectItemModel()
                                {
                                    IdGiayTo = dt.IdGiayTo,
                                    MaGiayTo = dt.MaGiayTo,
                                    TenGiayTo = dt.TenGiayTo
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenGiayTo.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetLoaiGiayToYeuCauHocBaQuery : IRequest<List<LoaiGiayToYeuCauResponseModel>>
    {
        public LoaiGiayToYeuCauRequestModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Lấy giấy tờ yêu cầu cho học bạ
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetLoaiGiayToYeuCauHocBaQuery(LoaiGiayToYeuCauRequestModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetLoaiGiayToYeuCauHocBaQuery, List<LoaiGiayToYeuCauResponseModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiGiayToYeuCauResponseModel>> Handle(GetLoaiGiayToYeuCauHocBaQuery request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var sysLog = request.SystemLog;
                var idHoSo = model.IdHoSo != 0 ? model.IdHoSo : Int32.Parse(sysLog.UserId);

                var result = new List<LoaiGiayToYeuCauResponseModel>();

                //Lấy giấy tờ yêu cầu cho phương thức xét tuyển thẳng
                if(model.XetTuyenThang)
                {
                    // Giấy tờ xét tuyển đã tải file
                    var query = (from dt in _dataContext.SvDoiTuongXetTuyens
                                 join gt in _dataContext.SvLoaiGiayTos on dt.IdHoSoGiayTo equals gt.IdGiayTo
                                 join hsn in _dataContext.SvHoSoNopTuyenSinhs on dt.IdHoSoGiayTo equals hsn.IdGiayTo
                                 join hsnu in _dataContext.SvHoSoNopTuyenSinhUrls on hsn.IdHoSoNopTuyenSinh equals hsnu.IdHoSoNopTuyenSinh
                                 join dtxt in _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens on hsn.IdHoSo equals dtxt.IdHoSo
                                 where dt.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && gt.TuyenSinh == true && gt.IdHe == model.IdHe
                                 && hsn.IdHoSo == idHoSo && dtxt.IdHe == model.IdHe && dtxt.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen
                                 group new { dt, hsnu, dtxt, gt } by new { dt.IdDoiTuongXetTuyen, hsn.IdHoSo, hsnu.IdHoSoNopTuyenSinhUrl, dtxt.IdPhuongThucXetTuyen } into g
                                 select new
                                 {
                                     g.Key.IdDoiTuongXetTuyen,
                                     IdGiayTo = g.Max(x => x.gt.IdGiayTo),
                                     FileName = g.Max(x => x.hsnu.FileName),
                                     PhuongThucXetTuyen = g.Max(x => x.dtxt.IdPhuongThucXetTuyen),
                                     TrangThaiHoSo = g.Max(x => x.dtxt.TrangThaiHoSo),
                                     g.Key.IdHoSo,
                                     MaGiayTo = g.Max(x => x.gt.MaGiayTo),
                                     TenGiayTo = g.Max(x => x.gt.TenGiayTo),
                                     BatBuoc = g.Max(x => (x.gt.BatBuoc != null && x.gt.BatBuoc == true) ? 1 : 0),
                                     UrlGiayTo = g.Max(x => x.hsnu.UrlGiayTo),
                                     LoaiFile = g.Max(x => x.hsnu.LoaiFile),
                                     GhiChu = g.Max(x => x.gt.GhiChu)
                                 }).ToList();

                    var data = query.GroupBy(x => x.IdDoiTuongXetTuyen);

                    foreach (var item in data)
                    {
                        result.Add(new LoaiGiayToYeuCauResponseModel()
                        {
                            Edit = item.Where(x => x.IdHoSo == idHoSo && x.PhuongThucXetTuyen == model.IdPhuongThucXetTuyen && x.TrangThaiHoSo > 0 && x.TrangThaiHoSo != 3).Count() == 0,
                            GiayToXetTuyenThang = true,
                            TenGiayTo = item.Max(x => x.TenGiayTo),
                            GhiChu = item.Max(x => x.GhiChu),
                            IdGiayTo = item.Max(x => x.IdGiayTo),
                            MaGiayTo = item.Max(x => x.MaGiayTo),
                            BatBuoc = item.Max(x => x.BatBuoc),
                            SvHoSoNopTuyenSinhUrls = item.Where(x => x.IdHoSo == idHoSo).Select(x => new CreateHoSoNopTuyenSinhUrlModel()
                            {
                                UrlGiayTo = x.UrlGiayTo,
                                FileName = x.FileName,
                                LoaiFile = x.LoaiFile
                            }).ToList()
                        });
                    }

                    var listIdGiayTo = result.Select(x => x.IdGiayTo).ToList();
                    //Giấy tờ xét tuyển thẳng chưa tải file
                    var giayToXetTuyen = (from dt in _dataContext.SvDoiTuongXetTuyens
                                 join gt in _dataContext.SvLoaiGiayTos on dt.IdHoSoGiayTo equals gt.IdGiayTo
                                 where gt.TuyenSinh == true && dt.IdPhuongThucXetTuyen == model.IdPhuongThucXetTuyen && gt.IdHe == model.IdHe && !listIdGiayTo.Contains(dt.IdHoSoGiayTo)
                                 select new LoaiGiayToYeuCauResponseModel
                                 {
                                     Edit = true,
                                     GiayToXetTuyenThang = true,
                                     IdGiayTo = gt.IdGiayTo,
                                     MaGiayTo = gt.MaGiayTo,
                                     TenGiayTo = gt.TenGiayTo,
                                     BatBuoc = gt.BatBuoc == true ? 1 : 0,
                                     GhiChu = gt.GhiChu
                                 }).ToList();

                    result.AddRange(giayToXetTuyen);
                }

                //Lấy list idGiayTo dùng để xét tuyển
                var listIdGiayToXetTuyen = await _dataContext.SvDoiTuongXetTuyens.Select(x => x.IdHoSoGiayTo).ToListAsync();
                
                //Lấy giấy tờ yêu cầu mặc định đã tải file 
                var queryGiayToMacDinh = (from gt in _dataContext.SvLoaiGiayTos
                             join hsn in _dataContext.SvHoSoNopTuyenSinhs on gt.IdGiayTo equals hsn.IdGiayTo
                             join hsnu in _dataContext.SvHoSoNopTuyenSinhUrls on hsn.IdHoSoNopTuyenSinh equals hsnu.IdHoSoNopTuyenSinh
                             join dtxt in _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens on hsn.IdHoSo equals dtxt.IdHoSo
                             where gt.TuyenSinh == true && gt.IdHe == model.IdHe && dtxt.IdHoSo == idHoSo && dtxt.IdHe == model.IdHe && hsn.IdHoSo == idHoSo 
                                          group new { gt, hsnu, dtxt } by new { gt.IdGiayTo, hsnu.IdHoSoNopTuyenSinhUrl, dtxt.IdHoSo} into g
                             select new
                             {
                                 g.Key.IdGiayTo,
                                 MaGiayTo = g.Max(x => x.gt.MaGiayTo),
                                 TenGiayTo = g.Max(x => x.gt.TenGiayTo),
                                 BatBuoc = g.Max(x => (x.gt.BatBuoc != null && x.gt.BatBuoc == true) ? 1 : 0),
                                 UrlGiayTo = g.Max(x => x.hsnu.UrlGiayTo),
                                 LoaiFile = g.Max(x => x.hsnu.LoaiFile != null ? x.hsnu.LoaiFile : 0),
                                 GiayTo = g.Max(x => x.gt.TenGiayTo),
                                 FileName = g.Max(x => x.hsnu.FileName),
                                 g.Key.IdHoSo,
                                 TrangThaiHoSo = g.Select(x => x.dtxt.TrangThaiHoSo).ToList(),
                                 GhiChu = g.Max(x => x.gt.GhiChu)
                             }).ToList();

                var giayToYeuCauMacDinhs = queryGiayToMacDinh.Where(x => !listIdGiayToXetTuyen.Contains(x.IdGiayTo)).GroupBy(x => x.IdGiayTo);

                foreach (var item in giayToYeuCauMacDinhs)
                {
                    result.Add(new LoaiGiayToYeuCauResponseModel()
                    {
                        Edit = item.FirstOrDefault()?.TrangThaiHoSo?.Where(x => x > 0 && x != 3).Count() == 0,
                        GiayToXetTuyenThang = false,
                        GhiChu = item.Max(x => x.GhiChu),
                        TenGiayTo = item.Max(x => x.TenGiayTo),
                        IdGiayTo = item.Max(x => x.IdGiayTo),
                        MaGiayTo = item.Max(x => x.MaGiayTo),
                        BatBuoc = item.Max(x => x.BatBuoc),
                        SvHoSoNopTuyenSinhUrls = item.Where(x => x.IdHoSo == idHoSo).Select(x => new CreateHoSoNopTuyenSinhUrlModel()
                        {
                            UrlGiayTo = x.UrlGiayTo,
                            FileName = x.FileName,
                            LoaiFile = x.LoaiFile
                        }).ToList()
                    });
                }

                var listIdGiayToMacDinh = result.Select(x => x.IdGiayTo).ToList();
                // List Giấy tờ mặc định chưa tải file
                var giayToMacDinhs = (from gt in _dataContext.SvLoaiGiayTos
                                      where gt.TuyenSinh == true && gt.IdHe == model.IdHe && !listIdGiayToMacDinh.Contains(gt.IdGiayTo) && !listIdGiayToXetTuyen.Contains(gt.IdGiayTo)
                                      select new LoaiGiayToYeuCauResponseModel
                                      {
                                          Edit = true,
                                          GiayToXetTuyenThang = false,
                                          IdGiayTo = gt.IdGiayTo,
                                          MaGiayTo = gt.MaGiayTo,
                                          TenGiayTo = gt.TenGiayTo,
                                          BatBuoc = gt.BatBuoc == true ? 1 : 0,
                                          GhiChu = gt.GhiChu
                                      }).ToList();

                result.AddRange(giayToMacDinhs);

                return result;
            }
        }
    }
}
