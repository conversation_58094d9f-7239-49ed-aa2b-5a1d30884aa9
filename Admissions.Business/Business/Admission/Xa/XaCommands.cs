using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class CreateXaCommand : IRequest<Unit>
    {
        public CreateXaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới xã
        /// </summary>
        /// <param name="model">Thông tin xã cần thêm mới</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public CreateXaCommand(CreateXaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateXaCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateXaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXaModel, SvXa>(model);

                var checkCode = await _dataContext.SvXas.AnyAsync(x => x.TenXa == entity.TenXa);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Xa.TenXa.Existed", entity.TenXa]}");
                }

                await _dataContext.SvXas.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XaConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới xã: {entity.TenXa}",
                    ObjectCode = XaConstant.CachePrefix,
                    ObjectId = entity.IdXa.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XaConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }

    public class UpdateXaCommand : IRequest<Unit>
    {
        public UpdateXaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật xã
        /// </summary>
        /// <param name="model">Thông tin xã cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateXaCommand(UpdateXaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateXaCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdateXaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XaConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var checkCode = await _dataContext.SvXas.AnyAsync(x => x.TenXa == model.TenXa && x.IdXa != model.IdXa);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Xa.TenXa.Existed", model.TenXa]}");
                }

                var entity = await _dataContext.SvXas.FirstOrDefaultAsync(x => x.IdXa == model.IdXa);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {XaConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXas.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XaConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(XaConstant.BuildCacheKey(entity.IdXa.ToString()));
                _cacheService.Remove(XaConstant.BuildCacheKey());

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập xã: {entity.TenXa}",
                    ObjectCode = XaConstant.CachePrefix,
                    ObjectId = entity.IdXa.ToString()
                });

                return Unit.Value;
            }
        }
    }

    public class DeleteXaCommand : IRequest<Unit>
    {
        public string Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa xã 
        /// </summary>
        /// <param name="id">Id xã cần xóa</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public DeleteXaCommand(string id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteXaCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteXaCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XaConstant.CachePrefix}: {JsonSerializer.Serialize(id)}");

                // Check dữ liệu đã dùng

                var entity = await _dataContext.SvXas.FindAsync(id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.SvXas.Remove(entity);
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa xã: {entity.TenXa}",
                    ObjectCode = XaConstant.CachePrefix,
                    ObjectId = entity.IdXa.ToString()
                });

                Log.Information($"Delete {XaConstant.CachePrefix}: {JsonSerializer.Serialize(id)} success");

                //Xóa cache
                _cacheService.Remove(XaConstant.BuildCacheKey(entity.IdXa.ToString()));
                await _dataContext.SaveChangesAsync();

                _cacheService.Remove(XaConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
