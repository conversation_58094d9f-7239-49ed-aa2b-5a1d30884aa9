using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using System.Linq;

namespace Admissions.Business
{
    public class TuyenSinhToHopXetTuyenCommands
    {
        public class CreateTuyenSinhToHopXetTuyenCommand : IRequest<Unit>
        {
            public CreateTuyenSinhToHopXetTuyenModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Thêm mới tổ hợp xét tuyển
            /// </summary>
            /// <param name="model">Thông tin tổ hợp xét tuyển cần thêm mới</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public CreateTuyenSinhToHopXetTuyenCommand(CreateTuyenSinhToHopXetTuyenModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<CreateTuyenSinhToHopXetTuyenCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(CreateTuyenSinhToHopXetTuyenCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create {TuyenSinhToHopXetTuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateTuyenSinhToHopXetTuyenModel, SvTuyenSinhToHopXetTuyen>(model);

                    var checkCode = await _dataContext.SvTuyenSinhToHopXetTuyens.AnyAsync(x => x.MaToHop == entity.MaToHop);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["TuyenSinhToHopXetTuyen.MaToHop.Existed", entity.MaToHop]}");
                    }

                    await _dataContext.SvTuyenSinhToHopXetTuyens.AddAsync(entity);
                    await _dataContext.SaveChangesAsync();
                    foreach (var item in model.LstMon)
                    {
                        SvTuyenSinhToHopMonXetTuyen thm = new SvTuyenSinhToHopMonXetTuyen();
                        thm.IdToHopXetTuyen = entity.IdToHopXetTuyen;
                        thm.IdMonXetTuyen = item.IdMonXetTuyen;
                        thm.HeSo = item.HeSo;
                        await _dataContext.SvTuyenSinhToHopMonXetTuyens.AddAsync(thm);
                    }
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Create {TuyenSinhToHopXetTuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới tổ hợp xét tuyển: {entity.TenToHopXetTuyen}",
                        ObjectCode = TuyenSinhToHopXetTuyenConstant.CachePrefix,
                        ObjectId = entity.IdToHopXetTuyen.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhToHopXetTuyenConstant.BuildCacheKey());
                    _cacheService.Remove(TuyenSinhToHopMonXetTuyenConstant.BuildCacheKey());

                    return Unit.Value;
                }
            }
        }

        public class UpdateTuyenSinhToHopXetTuyenCommand : IRequest<Unit>
        {
            public UpdateTuyenSinhToHopXetTuyenModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Cập nhật tổ hợp xét tuyển
            /// </summary>
            /// <param name="model">Thông tin tổ hợp xét tuyển cần cập nhật</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public UpdateTuyenSinhToHopXetTuyenCommand(UpdateTuyenSinhToHopXetTuyenModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<UpdateTuyenSinhToHopXetTuyenCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(UpdateTuyenSinhToHopXetTuyenCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Update {TuyenSinhToHopXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                    var checkCode = await _dataContext.SvTuyenSinhToHopXetTuyens.AnyAsync(x => x.MaToHop == model.MaToHop && x.IdToHopXetTuyen != model.IdToHopXetTuyen);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["TuyenSinhToHopXetTuyen.MaToHop.Existed", model.MaToHop]}");
                    }

                    var entity = await _dataContext.SvTuyenSinhToHopXetTuyens.FirstOrDefaultAsync(x => x.IdToHopXetTuyen == model.IdToHopXetTuyen);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }
                    Log.Information($"Before Update {TuyenSinhToHopXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    model.UpdateEntity(entity);

                    _dataContext.SvTuyenSinhToHopXetTuyens.Update(entity);

                    var list = await _dataContext.SvTuyenSinhToHopMonXetTuyens.Where(x => x.IdToHopXetTuyen == model.IdToHopXetTuyen).ToListAsync();
                    _dataContext.SvTuyenSinhToHopMonXetTuyens.RemoveRange(list);

                    foreach (var item in model.LstMon)
                    {
                        SvTuyenSinhToHopMonXetTuyen thm = new SvTuyenSinhToHopMonXetTuyen();
                        thm.IdToHopXetTuyen = model.IdToHopXetTuyen;
                        thm.IdMonXetTuyen = item.IdMonXetTuyen;
                        thm.HeSo = item.HeSo;
                        await _dataContext.SvTuyenSinhToHopMonXetTuyens.AddAsync(thm);
                    }

                    await _dataContext.SaveChangesAsync();

                    Log.Information($"After Update {TuyenSinhToHopXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhToHopXetTuyenConstant.BuildCacheKey(entity.IdToHopXetTuyen.ToString()));
                    _cacheService.Remove(TuyenSinhToHopXetTuyenConstant.BuildCacheKey());
                    _cacheService.Remove(TuyenSinhToHopMonXetTuyenConstant.BuildCacheKey());

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Cập nhập tổ hợp xét tuyển: {entity.TenToHopXetTuyen}",
                        ObjectCode = TuyenSinhToHopXetTuyenConstant.CachePrefix,
                        ObjectId = entity.IdToHopXetTuyen.ToString()
                    });

                    return Unit.Value;
                }
            }
        }

        public class DeleteTuyenSinhToHopXetTuyenCommand : IRequest<Unit>
        {
            public int Id { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Xóa tổ hợp xét tuyển 
            /// </summary>
            /// <param name="id">Id tổ hợp xét tuyển cần xóa</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public DeleteTuyenSinhToHopXetTuyenCommand(int id, SystemLogModel systemLog)
            {
                Id = id;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<DeleteTuyenSinhToHopXetTuyenCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(DeleteTuyenSinhToHopXetTuyenCommand request, CancellationToken cancellationToken)
                {
                    var id = request.Id;
                    var systemLog = request.SystemLog;
                    Log.Information($"Delete {TuyenSinhToHopXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(id)}");

                    // Check dữ liệu đã dùng

                    var entity = await _dataContext.SvTuyenSinhToHopXetTuyens.FindAsync(id);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }
                    _dataContext.SvTuyenSinhToHopXetTuyens.Remove(entity);
                    var list = await _dataContext.SvTuyenSinhToHopMonXetTuyens.Where(x => x.IdToHopXetTuyen == id).ToListAsync();
                    _dataContext.SvTuyenSinhToHopMonXetTuyens.RemoveRange(list);

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa tổ hợp xét tuyển: {entity.TenToHopXetTuyen}",
                        ObjectCode = TuyenSinhToHopXetTuyenConstant.CachePrefix,
                        ObjectId = entity.IdToHopXetTuyen.ToString()
                    });

                    Log.Information($"Delete {TuyenSinhToHopXetTuyenConstant.CachePrefix}: {JsonSerializer.Serialize(id)} success");

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhToHopXetTuyenConstant.BuildCacheKey(entity.IdToHopXetTuyen.ToString()));
                    await _dataContext.SaveChangesAsync();

                    _cacheService.Remove(TuyenSinhToHopXetTuyenConstant.BuildCacheKey());
                    _cacheService.Remove(TuyenSinhToHopMonXetTuyenConstant.BuildCacheKey());

                    return Unit.Value;
                }
            }
        }
    }
}
