using System;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhNhomNganhBaseModel
    {
        public int IdNhomNganh { get; set; }
        public string MaNhomNganh { get; set; }
        public string TenNhomNganh { get; set; }
    }

    public class TuyenSinhNhomNganhModel : TuyenSinhNhomNganhBaseModel
    {
    }

    public class TuyenSinhNhomNganhDetailModel : TuyenSinhNhomNganhModel
    {
        public string ModifyUserName { get; set; }
        public DateTime? ModifyDate { get; set; }
    }
    public class CreateTuyenSinhNhomNganhModel : TuyenSinhNhomNganhDetailModel
    {
        public string CreateUserName { get; set; }
    }
    public class UpdateTuyenSinhNhomNganhModel : TuyenSinhNhomNganhDetailModel
    {
        public void UpdateEntity(SvTuyenSinhNhomNganh entity)
        {
            entity.TenNhomNganh = this.TenNhomNganh;
        }
        public int? CreatedUserId { get; set; }
    }

    public class TuyenSinhNhomNganhQueryFilterModel : BaseQueryFilterModel
    {
        public string MaNhomNganh { get; set; }
        public TuyenSinhNhomNganhQueryFilterModel()
        {
            PropertyName = "TenNhomNganh";
            Ascending = "desc";
        }
    }

    public class TuyenSinhNhomNganhSelectItemModel
    {
        public int IdNhomNganh { get; set; }
        public string MaNhomNganh { get; set; }
        public string TenNhomNganh { get; set; }
    }
}
