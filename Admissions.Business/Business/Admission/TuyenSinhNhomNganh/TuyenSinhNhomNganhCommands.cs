using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhNhomNganhCommands
    {
        public class CreateTuyenSinhNhomNganhCommand : IRequest<Unit>
        {
            public CreateTuyenSinhNhomNganhModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Thêm mới nhóm ngành
            /// </summary>
            /// <param name="model">Thông tin nhóm ngành cần thêm mới</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public CreateTuyenSinhNhomNganhCommand(CreateTuyenSinhNhomNganhModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<CreateTuyenSinhNhomNganhCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(CreateTuyenSinhNhomNganhCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create {TuyenSinhNhomNganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateTuyenSinhNhomNganhModel, SvTuyenSinhNhomNganh>(model);

                    var checkCode = await _dataContext.SvTuyenSinhNhomNganhs.AnyAsync(x => x.MaNhomNganh == entity.MaNhomNganh);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["TuyenSinhNhomNganh.MaNhomNganh.Existed", entity.MaNhomNganh]}");
                    }

                    await _dataContext.SvTuyenSinhNhomNganhs.AddAsync(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Create {TuyenSinhNhomNganhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới nhóm ngành: {entity.TenNhomNganh}",
                        ObjectCode = TuyenSinhNhomNganhConstant.CachePrefix,
                        ObjectId = entity.IdNhomNganh.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhNhomNganhConstant.BuildCacheKey());

                    return Unit.Value;
                }
            }
        }

        public class UpdateTuyenSinhNhomNganhCommand : IRequest<Unit>
        {
            public UpdateTuyenSinhNhomNganhModel Model { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Cập nhật nhóm ngành
            /// </summary>
            /// <param name="model">Thông tin nhóm ngành cần cập nhật</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public UpdateTuyenSinhNhomNganhCommand(UpdateTuyenSinhNhomNganhModel model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<UpdateTuyenSinhNhomNganhCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(UpdateTuyenSinhNhomNganhCommand request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Update {TuyenSinhNhomNganhConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                    var checkCode = await _dataContext.SvTuyenSinhNhomNganhs.AnyAsync(x => x.MaNhomNganh == model.MaNhomNganh && x.IdNhomNganh != model.IdNhomNganh);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["TuyenSinhNhomNganh.MaNhomNganh.Existed", model.MaNhomNganh]}");
                    }

                    var entity = await _dataContext.SvTuyenSinhNhomNganhs.FirstOrDefaultAsync(x => x.IdNhomNganh == model.IdNhomNganh);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }
                    Log.Information($"Before Update {TuyenSinhNhomNganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    model.UpdateEntity(entity);

                    _dataContext.SvTuyenSinhNhomNganhs.Update(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"After Update {TuyenSinhNhomNganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhNhomNganhConstant.BuildCacheKey(entity.IdNhomNganh.ToString()));
                    _cacheService.Remove(TuyenSinhNhomNganhConstant.BuildCacheKey());

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Cập nhập nhóm ngành: {entity.TenNhomNganh}",
                        ObjectCode = TuyenSinhNhomNganhConstant.CachePrefix,
                        ObjectId = entity.IdNhomNganh.ToString()
                    });

                    return Unit.Value;
                }
            }
        }

        public class DeleteTuyenSinhNhomNganhCommand : IRequest<Unit>
        {
            public int Id { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Xóa nhóm ngành 
            /// </summary>
            /// <param name="id">Id nhóm ngành cần xóa</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public DeleteTuyenSinhNhomNganhCommand(int id, SystemLogModel systemLog)
            {
                Id = id;
                SystemLog = systemLog;
            }

            public class Handler : IRequestHandler<DeleteTuyenSinhNhomNganhCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(DeleteTuyenSinhNhomNganhCommand request, CancellationToken cancellationToken)
                {
                    var id = request.Id;
                    var systemLog = request.SystemLog;
                    Log.Information($"Delete {TuyenSinhNhomNganhConstant.CachePrefix}: {JsonSerializer.Serialize(id)}");

                    // Check dữ liệu đã dùng

                    var entity = await _dataContext.SvTuyenSinhNhomNganhs.FindAsync(id);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }
                    _dataContext.SvTuyenSinhNhomNganhs.Remove(entity);
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa nhóm ngành: {entity.TenNhomNganh}",
                        ObjectCode = TuyenSinhNhomNganhConstant.CachePrefix,
                        ObjectId = entity.IdNhomNganh.ToString()
                    });

                    Log.Information($"Delete {TuyenSinhNhomNganhConstant.CachePrefix}: {JsonSerializer.Serialize(id)} success");

                    //Xóa cache
                    _cacheService.Remove(TuyenSinhNhomNganhConstant.BuildCacheKey(entity.IdNhomNganh.ToString()));
                    await _dataContext.SaveChangesAsync();

                    _cacheService.Remove(TuyenSinhNhomNganhConstant.BuildCacheKey());

                    return Unit.Value;
                }
            }
        }
    }
}
