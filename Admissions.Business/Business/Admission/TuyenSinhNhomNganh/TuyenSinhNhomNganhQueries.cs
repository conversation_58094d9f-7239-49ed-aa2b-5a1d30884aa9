using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhNhomNganhQueries
    {
        public class GetTuyenSinhNhomNganhByIdQuery : IRequest<TuyenSinhNhomNganhModel>
        {
            public int Id { get; set; }

            /// <summary>
            /// Lấy thông tin nhóm ngành theo id
            /// </summary>
            /// <param name="id">Id nhóm ngành</param>
            public GetTuyenSinhNhomNganhByIdQuery(int id)
            {
                Id = id;
            }

            public class Handler : IRequestHandler<GetTuyenSinhNhomNganhByIdQuery, TuyenSinhNhomNganhModel>
            {
                private readonly AdmissionReadDataContext _dataContext;
                private readonly ICacheService _cacheService;

                public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                }

                public async Task<TuyenSinhNhomNganhModel> Handle(GetTuyenSinhNhomNganhByIdQuery request, CancellationToken cancellationToken)
                {
                    var id = request.Id;
                    string cacheKey = TuyenSinhNhomNganhConstant.BuildCacheKey(id.ToString());
                    var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                    {
                        var entity = await _dataContext.SvTuyenSinhNhomNganhs.FirstOrDefaultAsync(x => x.IdNhomNganh == id);

                        return AutoMapperUtils.AutoMap<SvTuyenSinhNhomNganh, TuyenSinhNhomNganhModel>(entity);
                    });
                    return item;
                }
            }
        }

        public class GetFilterTuyenSinhNhomNganhQuery : IRequest<PaginationList<TuyenSinhNhomNganhBaseModel>>
        {
            public TuyenSinhNhomNganhQueryFilterModel Filter { get; set; }

            /// <summary>
            /// Lấy danh sách nhóm ngành theo điều kiện lọc
            /// </summary>
            /// <param name="filter">Thông tin lọc</param>
            public GetFilterTuyenSinhNhomNganhQuery(TuyenSinhNhomNganhQueryFilterModel filter)
            {
                Filter = filter;
            }

            public class Handler : IRequestHandler<GetFilterTuyenSinhNhomNganhQuery, PaginationList<TuyenSinhNhomNganhBaseModel>>
            {
                private readonly AdmissionReadDataContext _dataContext;

                public Handler(AdmissionReadDataContext dataContext)
                {
                    _dataContext = dataContext;
                }

                public async Task<PaginationList<TuyenSinhNhomNganhBaseModel>> Handle(GetFilterTuyenSinhNhomNganhQuery request, CancellationToken cancellationToken)
                {
                    var filter = request.Filter;

                    var data = (from dt in _dataContext.SvTuyenSinhNhomNganhs
                                select new TuyenSinhNhomNganhBaseModel()
                                {
                                    IdNhomNganh = dt.IdNhomNganh,
                                    MaNhomNganh = dt.MaNhomNganh,
                                    TenNhomNganh = dt.TenNhomNganh
                                });

                    if (!string.IsNullOrEmpty(filter.TextSearch))
                    {
                        string ts = filter.TextSearch.Trim().ToLower();
                        data = data.Where(x => x.TenNhomNganh.ToLower().Contains(ts));
                    }

                    if (!string.IsNullOrEmpty(filter.MaNhomNganh))
                    {
                        string ts = filter.MaNhomNganh.Trim().ToLower();
                        data = data.Where(x => x.MaNhomNganh.ToLower().Contains(ts));
                    }

                    data = data.OrderByField(filter.PropertyName, filter.Ascending);

                    int totalCount = data.Count();

                    // Pagination
                    if (filter.PageSize <= 0)
                    {
                        filter.PageSize = QueryFilter.DefaultPageSize;
                    }

                    //Calculate nunber of rows to skip on pagesize
                    int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                    if (excludedRows <= 0)
                    {
                        excludedRows = 0;
                    }

                    // Query
                    data = data.Skip(excludedRows).Take(filter.PageSize);
                    int dataCount = data.Count();

                    var listData = await data.ToListAsync();

                    return new PaginationList<TuyenSinhNhomNganhBaseModel>()
                    {
                        DataCount = dataCount,
                        TotalCount = totalCount,
                        PageNumber = filter.PageNumber,
                        PageSize = filter.PageSize,
                        Data = listData
                    };
                }
            }
        }

        public class GetComboboxTuyenSinhNhomNganhQuery : IRequest<List<TuyenSinhNhomNganhSelectItemModel>>
        {
            public int Count { get; set; }
            public string TextSearch { get; set; }

            /// <summary>
            /// Lấy danh sách nhóm ngành cho combobox
            /// </summary>
            /// <param name="count">Số lượng bản ghi cần lấy ra</param>
            /// <param name="textSearch">Từ khóa tìm kiếm</param>
            public GetComboboxTuyenSinhNhomNganhQuery(int count = 0, string textSearch = "")
            {
                this.Count = count;
                this.TextSearch = textSearch;
            }

            public class Handler : IRequestHandler<GetComboboxTuyenSinhNhomNganhQuery, List<TuyenSinhNhomNganhSelectItemModel>>
            {
                private readonly AdmissionReadDataContext _dataContext;
                private readonly ICacheService _cacheService;

                public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                }

                public async Task<List<TuyenSinhNhomNganhSelectItemModel>> Handle(GetComboboxTuyenSinhNhomNganhQuery request, CancellationToken cancellationToken)
                {
                    var count = request.Count;
                    var textSearch = request.TextSearch;

                    string cacheKey = TuyenSinhNhomNganhConstant.BuildCacheKey();
                    var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                    {
                        var data = (from dt in _dataContext.SvTuyenSinhNhomNganhs
                                    select new TuyenSinhNhomNganhSelectItemModel()
                                    {
                                        IdNhomNganh = dt.IdNhomNganh,
                                        MaNhomNganh = dt.MaNhomNganh,
                                        TenNhomNganh = dt.TenNhomNganh
                                    });

                        return await data.ToListAsync();
                    });

                    if (!string.IsNullOrEmpty(textSearch))
                    {
                        textSearch = textSearch.ToLower().Trim();
                        list = list.Where(x => x.TenNhomNganh.ToLower().Contains(textSearch)).ToList();
                    }


                    if (count > 0)
                    {
                        list = list.Take(count).ToList();
                    }

                    return list;
                }
            }
        }
    }
}
