using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhDangKyDotBaseModel
    {
        public int IdDotDangKy { get; set; }
        public int IdDotTuyenSinh { get; set; }
        public int IdHe { get; set; }
        public int IdNganh { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public int IdToHopXetTuyen { get; set; }
        public int ChiTieu { get; set; }
        public string GhiChu { get; set; }
    }
    public class TuyenSinhDangKyDotModel : TuyenSinhDangKyDotBaseModel 
    {
        
    }
    public class TuyenSinhDangKyDotDetailModel : TuyenSinhDangKyDotBaseModel
    {
        public string TenDotTuyenSinh { get; set; } 
        public string TenHe { get; set; }
        public string TenNganh { get; set; }
        public string TenPhuongThucXetTuyen { get; set; }
        public string MaPhuongThucXetTuyen { get; set; }
        public string TenToHopXetTuyen { get; set; }
    }
    public class CreateTuyenSinhDangKyDot 
    {
        public int IdDotTuyenSinh { get; set; }
        public int IdHe { get; set; }
        public int IdNganh { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public int IdToHopXetTuyen { get; set; }
        public int ChiTieu { get; set; }
        public string GhiChu { get; set; }
    }
    public class UpdateTuyenSinhDangKyDot : CreateTuyenSinhDangKyDot
    {
        public void UpdateEntity(SvTuyenSinhDangKyDot input) 
        {
            input.IdDotTuyenSinh = this.IdDotTuyenSinh;
            input.IdHe = this.IdHe;
            input.IdNganh = this.IdNganh;
            input.IdPhuongThucXetTuyen = this.IdPhuongThucXetTuyen;
            input.IdToHopXetTuyen = this.IdToHopXetTuyen;
            input.ChiTieu = this.ChiTieu;
            input.GhiChu = this.GhiChu;
        }
    }
    public class TuyenSinhDangKyDotFilterModel : BaseQueryFilterModel
    {
        public TuyenSinhDangKyDotFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdDotDangKy";
        }
    }
}
