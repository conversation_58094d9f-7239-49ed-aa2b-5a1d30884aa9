using Admissions.Data;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Admissions.Business
{
    public class GetFilterTuyenSinhDangKyDotQuery : IRequest<PaginationList<TuyenSinhDangKyDotDetailModel>>
    {
        public TuyenSinhDangKyDotFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy tuyển sinh đợt đăng ký theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTuyenSinhDangKyDotQuery(TuyenSinhDangKyDotFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTuyenSinhDangKyDotQuery, PaginationList<TuyenSinhDangKyDotDetailModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TuyenSinhDangKyDotDetailModel>> Handle(GetFilterTuyenSinhDangKyDotQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvTuyenSinhDangKyDots
                            join dts in _dataContext.SvTuyenSinhDotTuyenSinhs
                            on dt.IdDotTuyenSinh equals dts.IdDotTuyenSinh
                            join he in _dataContext.SvHes
                            on dt.IdHe equals he.IdHe
                            join ng in _dataContext.SvTuyenSinhNganhs
                            on dt.IdNganh equals ng.IdNganh
                            join pt in _dataContext.SvTuyenSinhPhuongThucXetTuyens  
                            on dt.IdPhuongThucXetTuyen equals pt.IdPhuongThucXetTuyen
                            join th in _dataContext.SvTuyenSinhToHopXetTuyens
                            on dt.IdToHopXetTuyen equals th.IdToHopXetTuyen
                            select new TuyenSinhDangKyDotDetailModel
                            {
                                IdDotDangKy = dt.IdDotDangKy, 
                                IdHe = dt.IdHe,
                                IdNganh = dt.IdNganh,
                                IdDotTuyenSinh = dt.IdDotTuyenSinh, 
                                IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen, 
                                IdToHopXetTuyen = dt.IdToHopXetTuyen, 
                                ChiTieu = dt.ChiTieu,
                                GhiChu = dt.GhiChu, 
                                TenDotTuyenSinh = dts.TenDotTuyenSinh,
                                TenHe = he.TenHe, 
                                TenNganh = ng.TenNganhDaoTao, 
                                TenPhuongThucXetTuyen = pt.TenPhuongThucXetTuyen, 
                                TenToHopXetTuyen = th.TenToHopXetTuyen,
                                MaPhuongThucXetTuyen = pt.MaPhuongThucXetTuyen
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenDotTuyenSinh.ToLower().Contains(ts) || x.TenNganh.ToLower().Contains(ts) || x.TenPhuongThucXetTuyen.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<TuyenSinhDangKyDotDetailModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetTuyenSinhDangKyDotByIdQuery : IRequest<TuyenSinhDangKyDotModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin khối ngành theo id
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        public GetTuyenSinhDangKyDotByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTuyenSinhDangKyDotByIdQuery, TuyenSinhDangKyDotModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TuyenSinhDangKyDotModel> Handle(GetTuyenSinhDangKyDotByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = TuyenSinhDangKyDotConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvTuyenSinhDangKyDots.FirstOrDefaultAsync(x => x.IdDotDangKy == id);

                    return AutoMapperUtils.AutoMap<SvTuyenSinhDangKyDot, TuyenSinhDangKyDotModel>(entity);
                });
                return item;
            }
        }
    }
}
