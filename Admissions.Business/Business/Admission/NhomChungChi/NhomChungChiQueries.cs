using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using Admissions.Data;
using Core.Business;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Admissions.Business
{
    public class GetComboboxNhomChungChiQuery : IRequest<List<NhomChungChiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy nhóm chứng chỉ cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxNhomChungChiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxNhomChungChiQuery, List<NhomChungChiSelectItemModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<NhomChungChiSelectItemModel>> Handle(GetComboboxNhomChungChiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacNhomChungChiKey = NhomChungChiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacNhomChungChiKey, async () =>
                {
                    var data = (from dt in _dataContext.SvNhomChungChis.OrderBy(x => x.NhomChungChi)
                                select new NhomChungChiSelectItemModel()
                                {
                                    IdNhomChungChi = dt.IdNhomChungChi, 
                                    KyHieuNhom = dt.KyHieuNhom,
                                    NhomChungChi = dt.NhomChungChi

                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.NhomChungChi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
