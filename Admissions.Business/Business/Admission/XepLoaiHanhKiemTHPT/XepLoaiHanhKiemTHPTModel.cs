using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class XepLoaiHanhKiemTHPTBaseModel
    {
        public int IdXepLoaiHanhKiemLop12 { get; set; }
        public string MaXepLoaiHanhKiemLop12 { get; set; }
        public string TenXepLoaiHanhKiemLop12 { get; set; }
        public string Ghi<PERSON>hu { get; set; }
    }
    public class XepLoaiHanhKiemTHPTModel : XepLoaiHanhKiemTHPTBaseModel 
    {
        
    }
    public class XepLoaiHanhKiemTHPTDetailModel : XepLoaiHanhKiemTHPTBaseModel
    {

    }
    public class CreateXepLoaiHanhKiemTHPTModel 
    {
        public string MaXepLoaiHanhKiemLop12 { get; set; }
        public string TenXepLoaiHanhKiemLop12 { get; set; }
        public string GhiChu { get; set; }
    }
    public class UpdateXepLoaiHanhKiemTHPTModel : CreateXepLoaiHanhKiemTHPTModel
    {
        public void UpdateEntity(SvXepLoaiHanhKiemTHPT input) 
        {
            input.TenXepLoaiHanhKiemLop12 = this.TenXepLoaiHanhKiemLop12;
            input.GhiChu = this.GhiChu;
        }
    }
    public class XepLoaiHanhKiemTHPTFilterModel : BaseQueryFilterModel
    {
        public XepLoaiHanhKiemTHPTFilterModel()
        {
            Ascending = "desc";
            PropertyName = "TenXepLoaiHanhKiemLop12";
        }
    }
    public class XepLoaiHanhKiemTHPTSelectItemModel
    {
        public int IdXepLoaiHanhKiemLop12 { get; set; }
        public string MaXepLoaiHanhKiemLop12 { get; set; }
        public string TenXepLoaiHanhKiemLop12 { get; set; }
    }
}
