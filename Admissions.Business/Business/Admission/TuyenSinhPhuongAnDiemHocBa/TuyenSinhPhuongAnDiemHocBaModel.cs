using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhPhuongAnDiemHocBaBaseModel
    {
        public int IdPhuongAnDiemHocBa { get; set; }
        public string MaPhuongAnDiemHocBa { get; set; }
        public string TenPhuongAnDiemHocBa { get; set; }
        public string TieuDePhuongAnDiemHocBa { get; set; }
        public string TenDiem1 { get; set; }
        public string TenDiem2 { get; set; }
        public string TenDiem3 { get; set; }
        public string TenDiem4 { get; set; }
        public string TenDiem5 { get; set; }
        public string TenDiem6 { get; set; }
        public string GhiChu { get; set; }
        public string CongThucTinh { get; set; }
    }
    public class TuyenSinhPhuongAnDiemHocBaModel : TuyenSinhPhuongAnDiemHocBaBaseModel 
    {
        
    }
    public class TuyenSinhPhuongAnDiemHocBaDetailModel : TuyenSinhPhuongAnDiemHocBaBaseModel
    {

    }
    public class CreateTuyenSinhPhuongAnDiemHocBaModel
    {
        public string MaPhuongAnDiemHocBa { get; set; }
        public string TenPhuongAnDiemHocBa { get; set; }
        public string TieuDePhuongAnDiemHocBa { get; set; }
        public string TenDiem1 { get; set; }
        public string TenDiem2 { get; set; }
        public string TenDiem3 { get; set; }
        public string TenDiem4 { get; set; }
        public string TenDiem5 { get; set; }
        public string TenDiem6 { get; set; }
        public string GhiChu { get; set; }
        public string CongThucTinh { get; set; }
    }
    public class UpdateTuyenSinhPhuongAnDiemHocBaModel : CreateTuyenSinhPhuongAnDiemHocBaModel
    {
        public void UpdateEntity(SvTuyenSinhPhuongAnDiemHocBa input) 
        {
            input.MaPhuongAnDiemHocBa = this.MaPhuongAnDiemHocBa;
            input.TenPhuongAnDiemHocBa = this.TenPhuongAnDiemHocBa;
            input.TieuDePhuongAnDiemHocBa = this.TieuDePhuongAnDiemHocBa;
            input.TenDiem1 = this.TenDiem1;
            input.TenDiem2 = this.TenDiem2;
            input.TenDiem3 = this.TenDiem3;
            input.TenDiem4 = this.TenDiem4;
            input.TenDiem5 = this.TenDiem5;
            input.TenDiem6 = this.TenDiem6;
            input.CongThucTinh = this.CongThucTinh;
            input.GhiChu = this.GhiChu;   
        }
    }
    public class TuyenSinhPhuongAnDiemHocBaFilterModel : BaseQueryFilterModel
    {
        public TuyenSinhPhuongAnDiemHocBaFilterModel()
        {
            Ascending = "desc";
            PropertyName = "TenPhuongAnDiemHocBa";
        }
    }
    public class TuyenSinhPhuongAnDiemHocBaSelectItemModel
    {
        public int IdPhuongAnDiemHocBa { get; set; }
        public string MaPhuongAnDiemHocBa { get; set; }
        public string TenPhuongAnDiemHocBa { get; set; }
    }

    public class TuyenSinhPhuongAnDiemHocBaForPortalSelectItemModel
    {
        public int IdPhuongAnDiemHocBa { get; set; }
        public string MaPhuongAnDiemHocBa { get; set; }
        public string TenPhuongAnDiemHocBa { get; set; }
        public string TenDiem1 { get; set; }
        public string TenDiem2 { get; set; }
        public string TenDiem3 { get; set; }
        public string TenDiem4 { get; set; }
        public string TenDiem5 { get; set; }
        public string TenDiem6 { get; set; }
        public string CongThucTinh { get; set; }
    }
}
