using Admissions.Data;
using Core.Business;
using System;

namespace Admissions.Business
{
    public class TuyenSinhLoaiDoiTuongBaseModel
    {
        public int IdTuyenSinhLoaiDoiTuong { get; set; }
        public string MaLoaiDoiTuong { get; set; }
        public string TenLoaiDoiTuong { get; set; }
    }

    public class TuyenSinhLoaiDoiTuongModel : TuyenSinhLoaiDoiTuongBaseModel
    {
        public string NguoiTao { get; set; }
        public DateTime? NgayTao { get; set; }
        public string NguoiSua { get; set; }
        public DateTime? NgaySua { get; set; }
    }

    public class CreateTuyenSinhLoaiDoiTuongModel : TuyenSinhLoaiDoiTuongBaseModel
    {
        public string NguoiTao { get; set; }
        public DateTime? NgayTao { get; set; }
    }

    public class UpdateTuyenSinhLoaiDoiTuongModel : CreateTuyenSinhLoaiDoiTuongModel
    {
        public string NguoiSua { get; set; }
        public DateTime? NgaySua { get; set; }
        public void UpdateEntity(SvTuyenSinhLoaiDoiTuong input)
        {
            input.TenLoaiDoiTuong = TenLoaiDoiTuong;
            input.NgaySua = DateTime.Now;
        }
    }

    public class TuyenSinhLoaiDoiTuongFilterModel : BaseQueryFilterModel
    {
        public TuyenSinhLoaiDoiTuongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdTuyenSinhLoaiDoiTuong";
        }
    }

    public class TuyenSinhLoaiDoiTuongSelectItemModel : TuyenSinhLoaiDoiTuongBaseModel
    {
    }
}
