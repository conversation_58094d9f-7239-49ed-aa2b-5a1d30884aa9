using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class CreateTuyenSinhLoaiDoiTuongCommand : IRequest<Unit>
    {
        public CreateTuyenSinhLoaiDoiTuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateTuyenSinhLoaiDoiTuongCommand(CreateTuyenSinhLoaiDoiTuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateTuyenSinhLoaiDoiTuongCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateTuyenSinhLoaiDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {TuyenSinhLoaiDoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateTuyenSinhLoaiDoiTuongModel, SvTuyenSinhLoaiDoiTuong>(model);

                var checkCode = await _dataContext.SvTuyenSinhLoaiDoiTuongs.AnyAsync(x => x.MaLoaiDoiTuong == entity.MaLoaiDoiTuong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["TuyenSinhLoaiDoiTuong.MaLoaiDoiTuong.Existed", entity.MaLoaiDoiTuong.ToString()]}");
                }
                entity.NgayTao = DateTime.Now;
                entity.NguoiTao = systemLog.UserName;
                await _dataContext.SvTuyenSinhLoaiDoiTuongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {TuyenSinhLoaiDoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới loại đối tượng: {entity.TenLoaiDoiTuong}",
                    ObjectCode = TuyenSinhLoaiDoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdTuyenSinhLoaiDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TuyenSinhLoaiDoiTuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateTuyenSinhLoaiDoiTuongCommand : IRequest<Unit>
    {
        public UpdateTuyenSinhLoaiDoiTuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateTuyenSinhLoaiDoiTuongCommand(UpdateTuyenSinhLoaiDoiTuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateTuyenSinhLoaiDoiTuongCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateTuyenSinhLoaiDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Update {TuyenSinhLoaiDoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvTuyenSinhLoaiDoiTuongs.FirstOrDefaultAsync(dt => dt.IdTuyenSinhLoaiDoiTuong == model.IdTuyenSinhLoaiDoiTuong);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                Log.Information($"Before Update {TuyenSinhLoaiDoiTuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");
                entity.NguoiSua = systemLog.UserName;
                model.UpdateEntity(entity);

                _dataContext.SvTuyenSinhLoaiDoiTuongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {TuyenSinhLoaiDoiTuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {TuyenSinhLoaiDoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật loại đối tượng: {entity.TenLoaiDoiTuong}",
                    ObjectCode = TuyenSinhLoaiDoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdTuyenSinhLoaiDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TuyenSinhLoaiDoiTuongConstant.BuildCacheKey(entity.IdTuyenSinhLoaiDoiTuong.ToString()));
                _cacheService.Remove(TuyenSinhLoaiDoiTuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteTuyenSinhLoaiDoiTuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteTuyenSinhLoaiDoiTuongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteTuyenSinhLoaiDoiTuongCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteTuyenSinhLoaiDoiTuongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {TuyenSinhLoaiDoiTuongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvTuyenSinhLoaiDoiTuongs.FirstOrDefaultAsync(x => x.IdTuyenSinhLoaiDoiTuong == id);

                _dataContext.SvTuyenSinhLoaiDoiTuongs.Remove(entity);

                Log.Information($"Delete {TuyenSinhLoaiDoiTuongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa loại đối tượng: {entity.TenLoaiDoiTuong}",
                    ObjectCode = TuyenSinhLoaiDoiTuongConstant.CachePrefix,
                    ObjectId = entity.IdTuyenSinhLoaiDoiTuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TuyenSinhLoaiDoiTuongConstant.BuildCacheKey());
                _cacheService.Remove(TuyenSinhLoaiDoiTuongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
