using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class GetFilterTuyenSinhLoaiDoiTuongQuery : IRequest<PaginationList<TuyenSinhLoaiDoiTuongModel>>
    {
        public TuyenSinhLoaiDoiTuongFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tỉnh kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTuyenSinhLoaiDoiTuongQuery(TuyenSinhLoaiDoiTuongFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTuyenSinhLoaiDoiTuongQuery, PaginationList<TuyenSinhLoaiDoiTuongModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TuyenSinhLoaiDoiTuongModel>> Handle(GetFilterTuyenSinhLoaiDoiTuongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvTuyenSinhLoaiDoiTuongs
                            select new TuyenSinhLoaiDoiTuongModel
                            {
                                IdTuyenSinhLoaiDoiTuong = dt.IdTuyenSinhLoaiDoiTuong,
                                MaLoaiDoiTuong = dt.MaLoaiDoiTuong, 
                                TenLoaiDoiTuong = dt.TenLoaiDoiTuong,
                                NgaySua = dt.NgaySua,
                                NguoiSua = dt.NguoiSua,
                                NgayTao = dt.NgayTao,
                                NguoiTao = dt.NguoiTao
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenLoaiDoiTuong.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<TuyenSinhLoaiDoiTuongModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetTuyenSinhLoaiDoiTuongByIdQuery : IRequest<TuyenSinhLoaiDoiTuongModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin tỉnh theo id
        /// </summary>
        /// <param name="id">Id tỉnh</param>
        public GetTuyenSinhLoaiDoiTuongByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTuyenSinhLoaiDoiTuongByIdQuery, TuyenSinhLoaiDoiTuongModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TuyenSinhLoaiDoiTuongModel> Handle(GetTuyenSinhLoaiDoiTuongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = TuyenSinhLoaiDoiTuongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvTuyenSinhLoaiDoiTuongs.FirstOrDefaultAsync(x => x.IdTuyenSinhLoaiDoiTuong == id);

                    return AutoMapperUtils.AutoMap<SvTuyenSinhLoaiDoiTuong, TuyenSinhLoaiDoiTuongModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetComboboxTuyenSinhLoaiDoiTuongQuery : IRequest<List<TuyenSinhLoaiDoiTuongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách tỉnh cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxTuyenSinhLoaiDoiTuongQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxTuyenSinhLoaiDoiTuongQuery, List<TuyenSinhLoaiDoiTuongSelectItemModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TuyenSinhLoaiDoiTuongSelectItemModel>> Handle(GetComboboxTuyenSinhLoaiDoiTuongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                
                string cacheKey = TuyenSinhLoaiDoiTuongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.SvTuyenSinhLoaiDoiTuongs.OrderBy(x => x.TenLoaiDoiTuong)
                                select new TuyenSinhLoaiDoiTuongSelectItemModel()
                                {
                                    IdTuyenSinhLoaiDoiTuong = item.IdTuyenSinhLoaiDoiTuong,
                                    MaLoaiDoiTuong = item.MaLoaiDoiTuong,
                                    TenLoaiDoiTuong = item.TenLoaiDoiTuong
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenLoaiDoiTuong.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
