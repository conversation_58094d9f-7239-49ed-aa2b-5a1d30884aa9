using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Core.Business;

namespace Admissions.Business
{
    public class QuanLyHoSoTuyenSinhBaseModel
    {
        public int IdHoSo { get; set; }

        [MaxLength(20)]
        public string <PERSON>HoSo { get; set; }

        [MaxLength(50)]
        public string HoTen { get; set; }

        public DateTime NgaySinh { get; set; }

        public int? IdGioiTinh { get; set; }

        public int IdQuocTich { get; set; }

        public int IdDanToc { get; set; }

        [MaxLength(5)]
        public string IdTinhNs { get; set; }

        [MaxLength(20)]
        public string CMND { get; set; }

        public DateTime? NgayCapCMND { get; set; }

        public string IdNoiCapCMND { get; set; }

        [MaxLength(5)]
        public string IdTinhTt { get; set; }

        [MaxLength(5)]
        public string IdHuyenTt { get; set; }

        [MaxLength(5)]
        public string XaPhuongTt { get; set; }

        [MaxLength(200)]
        public string DiaChiTt { get; set; }

        public bool? HoKhauTtKv1 { get; set; }

        public bool? HoKhauTtXaKhoKhan { get; set; }

        [MaxLength(50)]
        public string DienThoaiCaNhan { get; set; }

        [MaxLength(50)]
        public string Email { get; set; }

        [MaxLength(200)]
        public string DiaChiBaoTin { get; set; }

        [MaxLength(50)]
        public string HoTenCha { get; set; }

        public int? NamSinhCha { get; set; }

        [MaxLength(50)]
        public string SoDienThoaiBo { get; set; }

        [MaxLength(50)]
        public string HoTenMe { get; set; }

        public int? NamSinhMe { get; set; }

        [MaxLength(50)]
        public string SoDienThoaiMe { get; set; }

        public int? IdDoiTuongTS { get; set; }

        public int? IdKhuVucTuyenSinh { get; set; }

        public int IdTruongTHPTLop10 { get; set; }

        public int IdTruongTHPTLop11 { get; set; }

        public int IdTruongTHPTLop12 { get; set; }

        public int IdXepLoaiHocTapLop12 { get; set; }

        public int IdXepLoaiHanhKiemLop12 { get; set; }

        public string NamTotNghiep { get; set; }

        [MaxLength(20)]
        public string SBD { get; set; }

        public string MatKhauThiSinh { get; set; }

        public DateTime CreateDate { get; set; }

        [MaxLength(20)]
        public string CreateUserName { get; set; }

        public DateTime? ModifyDate { get; set; }

        [MaxLength(20)]
        public string ModifyUserName { get; set; }
    }
    public class HoSoTuyenSinhModel : QuanLyHoSoTuyenSinhBaseModel
    {
        public string NoiCapCMND { get; set; }
        public string TinhTT { get; set; }
        public string HuyenTT { get; set; }
        public string XaTT { get; set; }
        public string DanToc { get; set; }
        public string TinhNS { get; set; }
        public string TruongTHPT { get; set; }
        public string KhuVuc { get; set; }
        public DateTime NgayDangKy { get; set; }
        public DateTime? NgayPheDuyet { get; set; }
        public int? TrangThaiHoSo { get; set; }
        public int? TrangThaiTaiChinh { get; set; }
        public string GhiChuDuyetHoSo { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int IdNganh { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public int NamTuyenSinh { get; set; }
        public int IdDotDangKy { get; set; }
        public string DoiTuongTuyenSinh { get; set; }
        public string GioiTinh { get; set; }
        public int IdThiSinhDangKyXetTuyen { get; set; }
        public string TenPhuongThucXetTuyen { get; set; }
        public string ListIdNganh { get; set; }
        public string UrlMauPhieuTuyenSinh { get; set; }
        public string MaPhuongThucXetTuyen { get; set; }
        public decimal? DiemCongKv { get; set; }
        public decimal? DiemCongDoiTuong { get; set; }
        public string TruongTHPTLop10 {  get; set; }
        public string TruongTHPTLop11 {  get; set; }
        public string XepLoaiHocTap {  get; set; }  
        public string XepLoaiHanhKiem {  get; set; }    
        public string TenTinhLop10 { get; set; }    
        public string TenTinhLop11 { get; set; }    
        public string TenTinhLop12 { get; set; }
        public string MaHuyenLop10 { get; set; }
        public string MaHuyenLop11 { get; set; }
        public string MaHuyenLop12 { get; set; }
        public string QuocTich {  get; set; }
        public string MaTruong10 { get; set; }
        public string MaTruong11 { get; set; }
        public string MaTruong12 { get; set; }
        public string NguoiDuyetTaiChinh { get; set; }
        public string NguoiDuyetHoSo { get; set; }
        public List<HoSoNopTuyenSinhDetail> HoSoNop { get; set; }
        public string HoKhauKv1 {  get; set; }
        public string HoKhauXaKhoKhan {  get; set; }
        public string NamBatDauCongTac {  get; set; }
        public string DonViCongTac { get; set; }
        public string NgheNghiep {  get; set; }
        public string ChucVu { get; set; }
        public int? ThamNienNgheNghiep { get; set;}
        public string LoaiCanBoHienTai {  get; set; }
        public string DoiTuongDuThi {  get; set; }
        public string LinhVucChuyenMon { get; set; }
        public string TrinhDoNgoaiNgu {  get; set; }
        public int? SoLuongBaiBao { get; set; }
        public string BoTucKienThuc { get; set; }
        public string TruongVanBang1 {  get; set; }
        public string NganhVanBang1 { get; set; }
        public string HinhThucVanBang1 { get; set; }
        public string TruongVanBang2 { get; set; }
        public string NganhVanBang2 { get; set; }
        public string HinhThucVanBang2 { get; set; }
        public string TruongThacSi { get; set; }
        public string NganhThacSi { get; set; }
        public string HinhThucThacSi { get; set; }
        public string NamTotNghiepThacSi { get; set; }
        public string NamTotNghiepVanBang1 { get; set; }
        public string NamTotNghiepVanBang2 { get; set; }
        public string DienThoaiNR { get; set; }
        public string DienThoaiCQ { get; set; }
        public string MaTinhTruong10 { get; set; }
        public string MaTinhTruong11 { get; set; }
        public string MaTinhTruong12 { get; set; }
        public string XepLoaiHanhKiem10 { get; set; }
        public string XepLoaiHanhKiem11 { get; set; }
        public string XepLoaiHanhKiem12 { get; set; }
        public string HoTenNguoiNhan { get; set; }
        public string SoDienThoaiNguoiNhan { get; set; }
        public int IdPhuongAnDiemHocBa { get; set; }
        public List<TuyenSinhKetQuaHocTapHocBaDetailModel> DiemHocBaDetails { get; set; }
        public List<TuyenSinhDangKyXetTuyenModel> NguyenVongs { get; set; }
        public List<string> PhuongAnDiemHocBaDetail { get; set; }
        public decimal TongDiemHocBa { get; set; }
    }
    public class HoSoTuyenSinhQueryFilter : BaseQueryFilterModel
    {
        public string MaHoSo { get; set; }
        public string HoTen { get; set; }
        public string CMND { get; set; }
        public int IdKhuVucTuyenSinh { get; set; }
        public int IdDoiTuongTS { get; set; }
        public int IdPhuongThucXetTuyen { get ; set; }
        public int? TrangThaiHoSo { get; set; }
        public int? TrangThaiTaiChinh { get; set; }
        public int IdHe { get; set; }
        public int IdNganh { get; set; }
        public DateTime? TuNgay { get; set; }
        public DateTime? DenNgay { get; set; }
        public int NamTuyenSinh { get; set; }
        public string UrlMauPhieu { get; set; } 
    }
    public class UpdateTrangThaiModel
    {
        public List<int> ListId { get; set; }
        public int Status { get; set; }
        public string Message { get; set; }
    }

    public class DanhSachXetDuyetTrungTuyenResponseModel
    {
        public HoSoTuyenSinhModel HoSoTuyenSinh { get; set; }
        public TuyenSinhDangKyXetTuyenModel DangKyXetTuyen { get; set;}
        public List<TuyenSinhDangKyXetTuyenModel> ListDangKyXetTuyen { get; set; }
        public TuyenSinhThiSinhDangKyXetTuyenBaseModel ThiSinhDangKyXetTuyen { get; set; }
        public List<DiemDetailModel> DiemDetails { get; set; }
        public List<TuyenSinhKetQuaHocTapHocBaDetailModel> DiemHocBaDetails { get; set; }
        public List<HoSoNopTuyenSinhDetail> HoSoNop {  get; set; }
        public decimal TongDiem { get; set; } 
        public string UrlMauPhieu { get; set; }
        public TuyenSinhMauPhieuDetailModel MauPhieuDetailModel { get; set; }
        public List<string> PhuongAnDiemHocBaDetail { get; set; }
    }

    public class DanhSachXetDuyetTrungTuyenRequestModel : HoSoTuyenSinhQueryFilter
    {
        public decimal? DiemThi { get; set; }
        public int? NguyenVong { get; set; }
        public int? TrungTuyen { get; set; }
    }

    public class UpdateTrangThaiTrungTuyenModel : UpdateTrangThaiModel
    {
        public List<int> ListIdThiSinhDangKyXetTuyen { get; set; }
    }
    public class DiemDetailModel
    {
        public int IdMonXetTuyen { get; set; }
        public string TenMonXetTuyen { get; set; }
        public decimal? Diem {  get; set; }
    }
    public class HoSoNopTuyenSinhDetail
    {
        public int IdGiayTo { get; set; }
        public string TenGiayTo { get; set; }
        public bool Exist { get; set; }
    }
}
