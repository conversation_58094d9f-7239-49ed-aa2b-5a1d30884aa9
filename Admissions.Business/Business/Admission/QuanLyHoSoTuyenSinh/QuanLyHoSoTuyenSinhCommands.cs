using Admissions.Data;
using Admissions.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Business.System;

namespace Admissions.Business
{
    public class QuanLyHoSoTuyenSinhCommands
    {
        public class UpdateTrangThaiHoSoCommand : IRequest<Unit>
        {
            public UpdateTrangThaiModel Data { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Cập nhật trạng thái hồ sơ
            /// </summary>
            /// <param name="data">Thông tin trạng thái hồ sơ cần cập nhật</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public UpdateTrangThaiHoSoCommand(UpdateTrangThaiModel data, SystemLogModel systemLog)
            {
                Data = data;
                SystemLog = systemLog;
            }

            /// <summary>
            /// Cập nhật trạng thái hồ sơ
            /// </summary>
            public class Handler : IRequestHandler<UpdateTrangThaiHoSoCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                private readonly IMediator _mediator;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IMediator mediator)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                    _mediator = mediator;
                }

                public async Task<Unit> Handle(UpdateTrangThaiHoSoCommand request, CancellationToken cancellationToken)
                {
                    var data = request.Data;
                    var systemLog = request.SystemLog;
                    
                    var maPhanHe = AdmissionConstants.MA_PHAN_HE;

                    // Lấy IdPh từ mã phân hệ
                    var idPhanHe = (await _mediator.Send(new GetComboboxPhanHeQuery())).FirstOrDefault(x => x.PhanHe == maPhanHe)?.IdPh;

                    if (idPhanHe is null)
                        throw new ArgumentException("PhanHe.Not-Existed");

                    // Lấy tham số hệ thống theo IdPh
                    var lstThamSo = await _mediator.Send(new GetThamSoHeThongByIdPhQuery(idPhanHe.Value));
                    
                    List<SvTuyenSinhThiSinhDangKyXetTuyen> tuyenSinhThiSinhDangKyXetTuyens = await _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.Where(x => data.ListId.Contains(x.IdThiSinhDangKyXetTuyen)).ToListAsync();
                    List<SvTuyenSinhThiSinhDangKyXetTuyen> entityUpdates = new List<SvTuyenSinhThiSinhDangKyXetTuyen>();
                    List<ActionDetail> actionDetails = new List<ActionDetail>();
                    foreach(var id in data.ListId)
                    {
                        var tuyenSinhThiSinhDangKyXetTuyen = tuyenSinhThiSinhDangKyXetTuyens.Find(x => x.IdThiSinhDangKyXetTuyen == id);
                        if (tuyenSinhThiSinhDangKyXetTuyen == null)
                        {
                            throw new ArgumentException(_localizer["ho-so.NotExist"]);
                        }

                        if (tuyenSinhThiSinhDangKyXetTuyen.TrangThaiHoSo == data.Status 
                            || (tuyenSinhThiSinhDangKyXetTuyen.TrangThaiHoSo == 2 && data.Status > 0))
                            continue;
                        
                        var donViSuDung = lstThamSo.FirstOrDefault(x => x.IdThamSo == AdmissionsThamSoHeThongEnum.Don_vi_su_dung.ToString());
                        var isCDYTBM = donViSuDung?.GiaTri == AdmissionsDonViSuDungConstants.CDYTBM;
                        var shouldUpdateApprovalInfo = !isCDYTBM || (tuyenSinhThiSinhDangKyXetTuyen.TrangThaiHoSo == 0 && string.IsNullOrEmpty(tuyenSinhThiSinhDangKyXetTuyen.NguoiDuyetHoSo));

                        if (shouldUpdateApprovalInfo)
                        {
                            tuyenSinhThiSinhDangKyXetTuyen.GhiChuDuyetHoSo = data.Message;
                            tuyenSinhThiSinhDangKyXetTuyen.NguoiDuyetHoSo = systemLog.UserName;
                        }
                        tuyenSinhThiSinhDangKyXetTuyen.TrangThaiHoSo = data.Status;
                        
                        entityUpdates.Add(tuyenSinhThiSinhDangKyXetTuyen);
                        actionDetails.Add(new ActionDetail()
                        {
                            Description = $"{AdmissionLogConstants.ACTION_CAP_NHAP_TRANG_THAI_HO_SO}: {tuyenSinhThiSinhDangKyXetTuyen.IdThiSinhDangKyXetTuyen}-{tuyenSinhThiSinhDangKyXetTuyen.IdHoSo}",
                            ObjectCode = QuanLyHoSoTuyenSinhConstant.CachePrefix,
                            ObjectId = tuyenSinhThiSinhDangKyXetTuyen.IdThiSinhDangKyXetTuyen.ToString()
                        });
                    }
                    if (entityUpdates.Count() == 0)
                        throw new ArgumentException(_localizer["ho-so.NoDataToUpdate"]);
                    
                    _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.UpdateRange(entityUpdates);
                    await _dataContext.SaveChangesAsync();

                    var lstIdUpdate = entityUpdates.Select(x => x.IdThiSinhDangKyXetTuyen).ToList();
                    var queryDataEmail = (from dt in tuyenSinhThiSinhDangKyXetTuyens
                                         join hs in _dataContext.SvHoSoTuyenSinhs on dt.IdHoSo equals hs.IdHoSo
                                         join pt in _dataContext.SvTuyenSinhPhuongThucXetTuyens on dt.IdPhuongThucXetTuyen equals pt.IdPhuongThucXetTuyen
                                         where lstIdUpdate.Contains(dt.IdThiSinhDangKyXetTuyen)
                                         select new
                                         {
                                             hs.Email,
                                             pt.TenPhuongThucXetTuyen,
                                             hs.HoTen
                                         }).ToList();
                    if (data.Status == 2)
                    {
                        foreach (var item in queryDataEmail)
                        {
                            var mailModel = new SendMailUsingTemplateModel()
                            {
                                To = [item.Email],
                                EmailTemplateCode = EmailCodeConstants.DHM_DHS,
                                Data = new
                                {
                                    item.HoTen,
                                    item.TenPhuongThucXetTuyen
                                }
                            };
                            _mediator.Send(new SendMailUsingEmailTemplateQuery(mailModel, systemLog));
                        }
                    }
                    else if (data.Status == 3)
                    {
                        foreach (var item in queryDataEmail)
                        {
                            var mailModel = new SendMailUsingTemplateModel()
                            {
                                To = [item.Email],
                                EmailTemplateCode = EmailCodeConstants.DHM_TCHS,
                                Data = new
                                {
                                    item.HoTen,
                                    item.TenPhuongThucXetTuyen,
                                    LyDo = data.Message
                                }
                            };
                            _mediator.Send(new SendMailUsingEmailTemplateQuery(mailModel, systemLog));
                        }
                    }

                    systemLog.ListAction.AddRange(actionDetails);

                    //Xóa cache
                    _cacheService.Remove(QuanLyHoSoTuyenSinhConstant.BuildCacheKey());

                    return Unit.Value;
                }
            }
        }

        public class UpdateTrangThaiTaiChinhCommand : IRequest<Unit>
        {
            public UpdateTrangThaiModel Data { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Cập nhật trạng thái tài chính
            /// </summary>
            /// <param name="data">Thông tin trạng thái tài chính cần cập nhật</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public UpdateTrangThaiTaiChinhCommand(UpdateTrangThaiModel data, SystemLogModel systemLog)
            {
                Data = data;
                SystemLog = systemLog;
            }

            /// <summary>
            /// Cập nhật trạng thái tài chính
            /// </summary>
            public class Handler : IRequestHandler<UpdateTrangThaiTaiChinhCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }

                public async Task<Unit> Handle(UpdateTrangThaiTaiChinhCommand request, CancellationToken cancellationToken)
                {
                    var data = request.Data;
                    var systemLog = request.SystemLog;

                    List<SvTuyenSinhThiSinhDangKyXetTuyen> tuyenSinhThiSinhDangKyXetTuyens = await _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.Where(x => data.ListId.Contains(x.IdThiSinhDangKyXetTuyen)).ToListAsync();
                    List<SvTuyenSinhThiSinhDangKyXetTuyen> entityUpdates = new List<SvTuyenSinhThiSinhDangKyXetTuyen>();
                    List<ActionDetail> actionDetails = new List<ActionDetail>();
                    foreach (var id in data.ListId)
                    {
                        var tuyenSinhThiSinhDangKyXetTuyen = tuyenSinhThiSinhDangKyXetTuyens.Find(x => x.IdThiSinhDangKyXetTuyen == id);
                        if (tuyenSinhThiSinhDangKyXetTuyen == null)
                        {
                            throw new ArgumentException(_localizer["ho-so.NotExist"]);
                        }

                        if (tuyenSinhThiSinhDangKyXetTuyen.TrangThaiTaiChinh == data.Status && tuyenSinhThiSinhDangKyXetTuyen.GhiChuDuyetHoSo == data.Message)
                            continue;

                        tuyenSinhThiSinhDangKyXetTuyen.TrangThaiTaiChinh = data.Status;
                        tuyenSinhThiSinhDangKyXetTuyen.NguoiDuyetTaiChinh = systemLog.UserName;

                        entityUpdates.Add(tuyenSinhThiSinhDangKyXetTuyen);
                        actionDetails.Add(new ActionDetail()
                        {
                            Description = $"{AdmissionLogConstants.ACTION_CAP_NHAP_TRANG_THAI_HO_SO}: {tuyenSinhThiSinhDangKyXetTuyen.IdThiSinhDangKyXetTuyen}-{tuyenSinhThiSinhDangKyXetTuyen.IdHoSo}",
                            ObjectCode = QuanLyHoSoTuyenSinhConstant.CachePrefix,
                            ObjectId = tuyenSinhThiSinhDangKyXetTuyen.IdThiSinhDangKyXetTuyen.ToString()
                        });
                    }
                    if (entityUpdates.Count() == 0)
                        throw new ArgumentException(_localizer["ho-so.NoDataToUpdate"]);

                    _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.UpdateRange(entityUpdates);
                    await _dataContext.SaveChangesAsync();
                    systemLog.ListAction.AddRange(actionDetails);

                    //Xóa cache
                    _cacheService.Remove(QuanLyHoSoTuyenSinhConstant.BuildCacheKey());

                    return Unit.Value;
                }
            }
        }

        public class UpdateTrangThaiTrungTuyenCommand : IRequest<Unit>
        {
            public UpdateTrangThaiTrungTuyenModel Data { get; set; }
            public SystemLogModel SystemLog { get; set; }

            /// <summary>
            /// Cập nhật trạng thái trúng tuyển
            /// </summary>
            /// <param name="data">Thông tin trạng thái tài chính cần cập nhật</param>
            /// <param name="systemLog">Thông tin lưu log</param>
            public UpdateTrangThaiTrungTuyenCommand(UpdateTrangThaiTrungTuyenModel data, SystemLogModel systemLog)
            {
                Data = data;
                SystemLog = systemLog;
            }

            /// <summary>
            /// Cập nhật trạng thái trúng tuyển
            /// </summary>
            public class Handler : IRequestHandler<UpdateTrangThaiTrungTuyenCommand, Unit>
            {
                private readonly AdmissionDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                private readonly IMediator _mediator;

                public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, IMediator mediator)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                    _mediator = mediator;
                }

                public async Task<Unit> Handle(UpdateTrangThaiTrungTuyenCommand request, CancellationToken cancellationToken)
                {
                    var data = request.Data;
                    var systemLog = request.SystemLog;

                    var entityUpdates = new List<SvTuyenSinhDangKyXetTuyen>();
                    var entityThiSinhDangKyXetTuyenUpdates = new List<SvTuyenSinhThiSinhDangKyXetTuyen>();
                    var actionDetails = new List<ActionDetail>();

                    //Danh sách nguyện vọng
                    var tuyenSinhDangKyXetTuyens = _dataContext.SvTuyenSinhDangKyXetTuyens.Where(x => data.ListId.Contains(x.IdTuyenSinhDangKyXetTuyen)).ToList();

                    //kiểm tra trùng
                    var listIdHoSo = tuyenSinhDangKyXetTuyens.Select(x => x.IdHoSo).ToList();

                    var listThiSinhDangKyXetTuyen = _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.Where(x => data.ListIdThiSinhDangKyXetTuyen.Contains(x.IdThiSinhDangKyXetTuyen));

                    var thiSinhXetTuyens = (from dt in _dataContext.SvTuyenSinhDangKyXetTuyens
                                            join hs in _dataContext.SvHoSoTuyenSinhs on dt.IdHoSo equals hs.IdHoSo
                                            join n in _dataContext.SvNganhs on dt.IdNganh equals n.IdNganh
                                            join pt in _dataContext.SvTuyenSinhPhuongThucXetTuyens on dt.IdPhuongThucXetTuyen equals pt.IdPhuongThucXetTuyen
                                            where data.ListId.Contains(dt.IdTuyenSinhDangKyXetTuyen) || listIdHoSo.Contains(dt.IdHoSo)
                                            select new
                                            {
                                                dt.IdTuyenSinhDangKyXetTuyen,
                                                dt.IdPhuongThucXetTuyen,
                                                hs.MaHoSo,
                                                hs.HoTen,
                                                hs.Email,
                                                dt.ThuTuXet,
                                                dt.IdHoSo,
                                                n.TenNganh,
                                                pt.TenPhuongThucXetTuyen,
                                                dt.TrungTuyen,
                                                dt.DangKyHocChatLuongCao
                                            });
                    
                    if (listIdHoSo.Distinct().Count() != data.ListId.Count && data.Status == 1)
                    {
                        throw new ArgumentException(_localizer["InputData.IdHoSo.Duplicate"]);
                    }

                    //Lấy danh sách thí sinh trúng tuyển
                    var thiSinhTrungTuyens = thiSinhXetTuyens.Where(x => x.TrungTuyen == 1).ToList();
                    var listIdThiSinhTrungTuyen = thiSinhTrungTuyens.Select(x => x.IdHoSo).ToList();

                    foreach (var id in data.ListId)
                    {
                        var tuyenSinhDangKyXetTuyen = tuyenSinhDangKyXetTuyens.FirstOrDefault(x => x.IdTuyenSinhDangKyXetTuyen == id) ?? throw new ArgumentException(_localizer[$"TuyenSinhDangKyXetTuyen.NotExist"]);

                        if (listIdThiSinhTrungTuyen.Contains(tuyenSinhDangKyXetTuyen.IdHoSo) && data.Status == 1)
                        {
                            var thiSinhTrungTuyen = thiSinhTrungTuyens.FirstOrDefault(x => x.IdHoSo == tuyenSinhDangKyXetTuyen.IdHoSo);
                            throw new ArgumentException(_localizer[$"TuyenSinhDangKyXetTuyen.TrungTuyen.Accepted", thiSinhTrungTuyen.MaHoSo, thiSinhTrungTuyen.ThuTuXet, thiSinhTrungTuyen.TenNganh, thiSinhTrungTuyen.TenPhuongThucXetTuyen]);
                        }

                        var thiSinhDangKyXetTuyen = listThiSinhDangKyXetTuyen.FirstOrDefault(x => x.IdHoSo == tuyenSinhDangKyXetTuyen.IdHoSo) ?? throw new ArgumentException(_localizer["TuyenSinhThiSinhDangKyXetTuyen.NotExist"]);
                        
                        thiSinhDangKyXetTuyen.KetQuaXetTuyen = data.Status == 1 ? true : false;
                        tuyenSinhDangKyXetTuyen.TrungTuyen = data.Status;
                        tuyenSinhDangKyXetTuyen.GhiChu = data.Message;

                        entityThiSinhDangKyXetTuyenUpdates.Add(thiSinhDangKyXetTuyen);
                        entityUpdates.Add(tuyenSinhDangKyXetTuyen);
                        //Log
                        var entity = thiSinhXetTuyens.FirstOrDefault(x => x.IdTuyenSinhDangKyXetTuyen == tuyenSinhDangKyXetTuyen.IdTuyenSinhDangKyXetTuyen);
                        actionDetails.Add(new ActionDetail()
                        {
                            Description = $"{AdmissionLogConstants.ACTION_CAP_NHAP_TRANG_THAI_TRUNG_TUYEN}: Mã hồ sơ {entity.MaHoSo}-Nguyện vọng {entity.ThuTuXet}-Ngành {entity.TenNganh}-Phương thức {entity.TenPhuongThucXetTuyen}",
                            ObjectCode = QuanLyHoSoTuyenSinhConstant.CachePrefix,
                            ObjectId = tuyenSinhDangKyXetTuyen.IdTuyenSinhDangKyXetTuyen.ToString()
                        });
                    }
                    if (entityUpdates.Count() == 0)
                        throw new ArgumentException(_localizer["ho-so.NoDataToUpdate"]);

                    _dataContext.SvTuyenSinhDangKyXetTuyens.UpdateRange(entityUpdates);
                    _dataContext.SvTuyenSinhThiSinhDangKyXetTuyens.UpdateRange(entityThiSinhDangKyXetTuyenUpdates);
                    await _dataContext.SaveChangesAsync();

                    var lstIdUpdate = entityUpdates.Select(x => x.IdTuyenSinhDangKyXetTuyen).ToList();
                    var queryDataEmail = (from tsdk in listThiSinhDangKyXetTuyen
                                          join dt in thiSinhXetTuyens on new { tsdk.IdHoSo, tsdk.IdPhuongThucXetTuyen } equals new { dt.IdHoSo, dt.IdPhuongThucXetTuyen }
                                          join h in _dataContext.SvHes on tsdk.IdHe equals h.IdHe
                                          where lstIdUpdate.Contains(dt.IdTuyenSinhDangKyXetTuyen)
                                          select new
                                          {
                                              dt.HoTen,
                                              dt.Email,
                                              dt.TenNganh,
                                              h.TenHe

                                          });
                    if (data.Status == 1)
                    {
                        foreach (var item in queryDataEmail)
                        {
                            var mailModel = new SendMailUsingTemplateModel()
                            {
                                To = [item.Email],
                                EmailTemplateCode = EmailCodeConstants.DHM_DTT,
                                Data = new
                                {
                                    item.HoTen,
                                    item.TenNganh,
                                    item.TenHe
                                }
                            };
                            _mediator.Send(new SendMailUsingEmailTemplateQuery(mailModel, systemLog));
                        }
                    }
                    else if (data.Status == 2)
                    {
                        foreach (var item in queryDataEmail)
                        {
                            var mailModel = new SendMailUsingTemplateModel()
                            {
                                To = [item.Email],
                                EmailTemplateCode = EmailCodeConstants.DHM_TCTT,
                                Data = new
                                {
                                    item.HoTen,
                                    item.TenNganh,
                                    LyDo = data.Message
                                }
                            };
                            _mediator.Send(new SendMailUsingEmailTemplateQuery(mailModel, systemLog));
                        }
                    }

                    systemLog.ListAction.AddRange(actionDetails);

                    //Xóa cache
                    _cacheService.Remove(QuanLyHoSoTuyenSinhConstant.BuildCacheKey());

                    return Unit.Value;
                    
                }
            }
        }
    }
}
