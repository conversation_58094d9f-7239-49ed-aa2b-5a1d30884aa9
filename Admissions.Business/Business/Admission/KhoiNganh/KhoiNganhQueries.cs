using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class GetFilterKhoiNganhQuery : IRequest<PaginationList<KhoiNganhBaseModel>>
    {
        public KhoiNganhFilterModel Filter { get; set; }

        /// <summary>
        /// L<PERSON>y danh sách khối ngành theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterKhoiNganhQuery(KhoiNganhFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterKhoiNganhQuery, PaginationList<KhoiNganhBaseModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<KhoiNganhBaseModel>> Handle(GetFilterKhoiNganhQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvTuyenSinhKhoiNganhs
                            select new KhoiNganhBaseModel
                            {
                                IdKhoiNganh = dt.IdKhoiNganh, 
                                MaKhoiNganh = dt.MaKhoiNganh,
                                TenKhoiNganh = dt.TenKhoiNganh
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenKhoiNganh.ToLower().Contains(ts) || x.MaKhoiNganh.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<KhoiNganhBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetKhoiNganhByIdQuery : IRequest<KhoiNganhModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin khối ngành theo id
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        public GetKhoiNganhByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetKhoiNganhByIdQuery, KhoiNganhModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<KhoiNganhModel> Handle(GetKhoiNganhByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = KhoiNganhConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvTuyenSinhKhoiNganhs.FirstOrDefaultAsync(x => x.IdKhoiNganh == id);

                    return AutoMapperUtils.AutoMap<SvTuyenSinhKhoiNganh, KhoiNganhModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetComboboxKhoiNganhQuery : IRequest<List<KhoiNganhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy khối ngành cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxKhoiNganhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxKhoiNganhQuery, List<KhoiNganhSelectItemModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<KhoiNganhSelectItemModel>> Handle(GetComboboxKhoiNganhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = KhoiNganhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvTuyenSinhKhoiNganhs.OrderBy(x => x.TenKhoiNganh)
                                select new KhoiNganhSelectItemModel()
                                {
                                    MaKhoiNganh = dt.MaKhoiNganh,
                                    IdKhoiNganh = dt.IdKhoiNganh,
                                    TenKhoiNganh = dt.TenKhoiNganh
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenKhoiNganh.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
