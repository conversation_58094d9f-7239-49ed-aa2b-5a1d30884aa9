using System.ComponentModel.DataAnnotations;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class KhoiNganhBaseModel
    {
        public int IdKhoiNganh { get; set; }
        public string MaKhoiNganh { get; set; }
        public string TenKhoiNganh { get; set; }
    }
    public class KhoiNganhModel : KhoiNganhBaseModel 
    {
        
    }
    public class KhoiNganhDetailModel : KhoiNganhBaseModel
    {

    }
    public class CreateKhoiNganh 
    {
        [Required]
        [MaxLength(20, ErrorMessage = "KhoiNganh.MaKhoiNganh.Maxlength(20)")]
        public string MaKhoiNganh { get; set; }

        [Required]
        [MaxLength(500, ErrorMessage = "KhoiNganh.TenKhoiNganh.Maxlength(500)")]
        public string TenKhoiNganh { get; set; }
    }
    public class UpdateKhoiNganh : CreateKhoiNganh
    {
        public void UpdateEntity(SvTuyenSinhKhoiNganh input) 
        {
            input.TenKhoiNganh = this.TenKhoiNganh;
        }
    }
    public class KhoiNganhFilterModel : BaseQueryFilterModel
    {
        public KhoiNganhFilterModel()
        {
            Ascending = "desc";
            PropertyName = "TenKhoiNganh";
        }
    }
    public class KhoiNganhSelectItemModel
    {
        public int IdKhoiNganh { get; set; }
        public string MaKhoiNganh { get; set; }
        public string TenKhoiNganh { get; set; }
    }
}
