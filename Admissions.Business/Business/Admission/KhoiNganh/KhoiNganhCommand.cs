using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class CreateKhoiNganhCommand : IRequest<Unit>
    {
        public CreateKhoiNganh Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateKhoiNganhCommand(CreateKhoiNganh model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateKhoiNganhCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateKhoiNganhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {KhoiNganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateKhoiNganh, SvTuyenSinhKhoiNganh>(model);
                
                var checkCode = await _dataContext.SvTuyenSinhKhoiNganhs.AnyAsync(x => x.MaKhoiNganh == entity.MaKhoiNganh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["KhoiNganh.Existed", JsonSerializer.Serialize(entity)]}");
                }

                await _dataContext.SvTuyenSinhKhoiNganhs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {KhoiNganhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới khối ngành: {entity.TenKhoiNganh}",
                    ObjectCode = KhoiNganhConstant.CachePrefix,
                    ObjectId = entity.IdKhoiNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhoiNganhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateKhoiNganhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateKhoiNganh Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateKhoiNganhCommand(int id, UpdateKhoiNganh model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateKhoiNganhCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateKhoiNganhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;    
                var systemLog = request.SystemLog;
                Log.Information($"Update {KhoiNganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvTuyenSinhKhoiNganhs.FirstOrDefaultAsync(dt => dt.IdKhoiNganh == id);

                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                Log.Information($"Before Update {KhoiNganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvTuyenSinhKhoiNganhs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {KhoiNganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {KhoiNganhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật khối ngành: {entity.TenKhoiNganh}",
                    ObjectCode = KhoiNganhConstant.CachePrefix,
                    ObjectId = entity.IdKhoiNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhoiNganhConstant.BuildCacheKey(entity.IdKhoiNganh.ToString()));
                _cacheService.Remove(KhoiNganhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteKhoiNganhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteKhoiNganhCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteKhoiNganhCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteKhoiNganhCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {KhoiNganhConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var KhoiNganh = await _dataContext.SvTuyenSinhKhoiNganhs.FindAsync(id);
                
                if (KhoiNganh == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                _dataContext.SvTuyenSinhKhoiNganhs.Remove(KhoiNganh);

                Log.Information($"Delete {KhoiNganhConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa khối ngành: {KhoiNganh.TenKhoiNganh}",
                    ObjectCode = KhoiNganhConstant.CachePrefix,
                    ObjectId = KhoiNganh.IdKhoiNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhoiNganhConstant.BuildCacheKey());
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
