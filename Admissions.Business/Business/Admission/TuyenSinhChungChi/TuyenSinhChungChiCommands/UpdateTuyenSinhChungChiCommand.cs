using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Admissions.Shared;
using Core.Business;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;

namespace Admissions.Business
{
    public class UpdateTuyenSinhChungChiCommand : IRequest<Unit>
    {
        public UpdateTuyenSinhChungChiModel Model { get; set; }

        /// <summary>
        /// Cập nhật chứng chỉ
        /// </summary>
        /// <param name="model">Thông tin chứng chỉ cần cập nhật</param>
        public UpdateTuyenSinhChungChiCommand(UpdateTuyenSinhChungChiModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<UpdateTuyenSinhChungChiCommand, Unit>
        {
            private readonly AdmissionDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(AdmissionDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(UpdateTuyenSinhChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Update {TuyenSinhChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvTuyenSinhChungChis.FirstOrDefaultAsync(x => x.IdTuyenSinhChungChi == model.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["TuyenSinhChungChi.NotFound"]}");
                }

                Log.Information($"Before Update {TuyenSinhChungChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                // Check duplicate - improved with trim and null checking
                var existingItem = await _dataContext.SvTuyenSinhChungChis
                    .FirstOrDefaultAsync(x => x.TenChungChi.ToLower().Trim() == model.TenChungChi.ToLower().Trim() 
                                         && x.LoaiChungChi == model.LoaiChungChi
                                         && x.IdTuyenSinhChungChi != model.Id);
                
                if (existingItem != null)
                {
                    throw new ArgumentException($"{_localizer["TuyenSinhChungChi.Duplicate"]}: {model.TenChungChi}");
                }

                model.UpdateEntity(entity);
                
                _dataContext.SvTuyenSinhChungChis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {TuyenSinhChungChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật chứng chỉ: {entity.TenChungChi}",
                    ObjectCode = TuyenSinhChungChiConstant.CachePrefix,
                    ObjectId = entity.IdTuyenSinhChungChi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TuyenSinhChungChiConstant.BuildCacheKey(entity.IdTuyenSinhChungChi.ToString()));
                _cacheService.Remove(TuyenSinhChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }
}
