using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Admissions.Business
{
    public class GetComboboxTuyenSinhChungChiQuery : IRequest<List<TuyenSinhChungChiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách chứng chỉ cho combobox
        /// </summary>
        /// <param name="count">S<PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxTuyenSinhChungChiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxTuyenSinhChungChiQuery, List<TuyenSinhChungChiSelectItemModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TuyenSinhChungChiSelectItemModel>> Handle(GetComboboxTuyenSinhChungChiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                
                string cacheKey = TuyenSinhChungChiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.SvTuyenSinhChungChis.AsNoTracking().OrderBy(x => x.TenChungChi)
                                select new TuyenSinhChungChiSelectItemModel()
                                {
                                    IdTuyenSinhChungChi = item.IdTuyenSinhChungChi,
                                    TenChungChi = item.TenChungChi,
                                    LoaiChungChi = item.LoaiChungChi
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenChungChi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
