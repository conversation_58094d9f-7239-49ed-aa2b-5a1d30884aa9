using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admissions.Data;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Admissions.Business
{
    public class GetFilterTuyenSinhChungChiQuery : IRequest<PaginationList<TuyenSinhChungChiBaseModel>>
    {
        public TuyenSinhChungChiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách chứng chỉ có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTuyenSinhChungChiQuery(TuyenSinhChungChiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTuyenSinhChungChiQuery, PaginationList<TuyenSinhChungChiBaseModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TuyenSinhChungChiBaseModel>> Handle(GetFilterTuyenSinhChungChiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from cc in _dataContext.SvTuyenSinhChungChis
                            select new TuyenSinhChungChiBaseModel
                            {
                                IdTuyenSinhChungChi = cc.IdTuyenSinhChungChi,
                                TenChungChi = cc.TenChungChi,
                                LoaiChungChi = cc.LoaiChungChi
                            });

                // Filter by TenChungChi (certificate name)
                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenChungChi.ToLower().Contains(ts));
                }

                // Filter by LoaiChungChi (certificate type)
                if (filter.LoaiChungChi.HasValue)
                {
                    data = data.Where(x => x.LoaiChungChi == filter.LoaiChungChi.Value);
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<TuyenSinhChungChiBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }
}
