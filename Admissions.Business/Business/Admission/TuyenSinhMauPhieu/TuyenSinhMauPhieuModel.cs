using Admissions.Data;
using Core.Business;

namespace Admissions.Business
{
    public class TuyenSinhMauPhieuBaseModel
    {
        public int IdMauPhieuTuyenSinh { get; set; }
        public int IdHe { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public string UrlMauPhieuTuyenSinh { get; set; }
        public bool? KetQuaHocBa { get; set; }
        public bool? KetQuaThiThpt { get; set; }
        public bool? KetQuaNangKhieu { get; set; }
        public bool? KetQuaNangLuc { get; set; }
    }
    public class TuyenSinhMauPhieuModel : TuyenSinhMauPhieuBaseModel 
    {
        
    }
    public class TuyenSinhMauPhieuDetailModel : TuyenSinhMauPhieuBaseModel
    {
        public string TenHe { get; set; }
        public string TenPhuongThucXetTuyen { get; set; }
    }
    public class CreateTuyenSinhMauPhieuModel 
    {
        public int IdHe { get; set; }
        public int IdPhuongThucXetTuyen { get; set; }
        public string UrlMauPhieuTuyenSinh { get; set; }
        public bool? KetQuaHocBa { get; set; }
        public bool? KetQuaThiThpt { get; set; }
        public bool? KetQuaNangKhieu { get; set; }
        public bool? KetQuaNangLuc { get; set; }
    }
    public class UpdateTuyenSinhMauPhieuModel : CreateTuyenSinhMauPhieuModel
    {
        public void UpdateEntity(SvTuyenSinhMauPhieu input) 
        {
            input.IdHe = this.IdHe;
            input.IdPhuongThucXetTuyen = this.IdPhuongThucXetTuyen;
            input.UrlMauPhieuTuyenSinh = this.UrlMauPhieuTuyenSinh;
            input.KetQuaThiThpt = this.KetQuaThiThpt;
            input.KetQuaHocBa = this.KetQuaHocBa;
            input.KetQuaNangLuc = this.KetQuaNangLuc;
            input.KetQuaNangKhieu = this.KetQuaNangKhieu;
        }
    }
    public class TuyenSinhMauPhieuFilterModel : BaseQueryFilterModel
    {
        public TuyenSinhMauPhieuFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdMauPhieuTuyenSinh";
        }
    }
}
