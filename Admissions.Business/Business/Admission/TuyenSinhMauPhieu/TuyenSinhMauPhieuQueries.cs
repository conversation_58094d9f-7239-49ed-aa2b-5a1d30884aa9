using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Admissions.Data;
using Core.Business;
using System.Threading.Tasks;
using System;

namespace Admissions.Business
{
    public class GetFilterTuyenSinhMauPhieuQuery : IRequest<PaginationList<TuyenSinhMauPhieuDetailModel>>
    {
        public TuyenSinhMauPhieuFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy mẫu phiếu tuyển sinh theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTuyenSinhMauPhieuQuery(TuyenSinhMauPhieuFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTuyenSinhMauPhieuQuery, PaginationList<TuyenSinhMauPhieuDetailModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;

            public Handler(AdmissionReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TuyenSinhMauPhieuDetailModel>> Handle(GetFilterTuyenSinhMauPhieuQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvTuyenSinhMauPhieus
                            join he in _dataContext.SvHes
                            on dt.IdHe equals he.IdHe
                            join pt in _dataContext.SvTuyenSinhPhuongThucXetTuyens
                            on dt.IdPhuongThucXetTuyen equals pt.IdPhuongThucXetTuyen
                            select new TuyenSinhMauPhieuDetailModel
                            {
                                IdMauPhieuTuyenSinh = dt.IdMauPhieuTuyenSinh, 
                                IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                                IdHe = dt.IdHe,
                                UrlMauPhieuTuyenSinh = dt.UrlMauPhieuTuyenSinh, 
                                TenHe = he.TenHe, 
                                TenPhuongThucXetTuyen = pt.TenPhuongThucXetTuyen,
                                KetQuaHocBa = dt.KetQuaHocBa,
                                KetQuaNangKhieu = dt.KetQuaNangKhieu,
                                KetQuaNangLuc = dt.KetQuaNangLuc,
                                KetQuaThiThpt = dt.KetQuaThiThpt
                            });
                
                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenPhuongThucXetTuyen.ToLower().Contains(ts) || x.TenHe.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<TuyenSinhMauPhieuDetailModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetTuyenSinhMauPhieuByIdQuery : IRequest<TuyenSinhMauPhieuDetailModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin mẫu phiếu tuyển sinh theo id
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        public GetTuyenSinhMauPhieuByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTuyenSinhMauPhieuByIdQuery, TuyenSinhMauPhieuDetailModel>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<TuyenSinhMauPhieuDetailModel> Handle(GetTuyenSinhMauPhieuByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                var lstPhuongThuc = await _mediator.Send(new GetComboboxTuyenSinhPhuongThucXetTuyenQuery());
                var lstHe = await _mediator.Send(new GetComboboxHeQuery());
                var lstMauPhieu = await _mediator.Send(new GetComboboxMauPhieuQuery());

                var item = (from dt in lstMauPhieu
                           join h in lstHe on dt.IdHe equals h.IdHe
                           join pt in lstPhuongThuc on dt.IdPhuongThucXetTuyen equals pt.IdPhuongThucXetTuyen
                           where dt.IdMauPhieuTuyenSinh == id
                           select new TuyenSinhMauPhieuDetailModel()
                           {
                               IdMauPhieuTuyenSinh = dt.IdMauPhieuTuyenSinh,
                               IdHe = dt.IdHe,
                               IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                               KetQuaHocBa = dt.KetQuaHocBa,
                               KetQuaNangKhieu = dt.KetQuaNangKhieu,
                               KetQuaNangLuc = dt.KetQuaNangLuc,
                               KetQuaThiThpt = dt.KetQuaThiThpt,
                               TenHe = h.TenHe,
                               TenPhuongThucXetTuyen = pt.TenPhuongThucXetTuyen,
                               UrlMauPhieuTuyenSinh = dt.UrlMauPhieuTuyenSinh
                           }).FirstOrDefault();

                return item;
            }
        }
    }

    public class GetUrlMauPhieuQuery : IRequest<string>
    {
        public int IdHe { get; set; }
        public int IdPhuongThuc { get; set; }

        /// <summary>
        /// Lấy thông tin mẫu phiếu tuyển sinh theo id
        /// </summary>
        /// <param name="id">Id phương thức xét tuyển</param>
        public GetUrlMauPhieuQuery(int idHe, int idPhuongThuc)
        {
            IdHe = idHe;
            IdPhuongThuc = idPhuongThuc;
        }

        public class Handler : IRequestHandler<GetUrlMauPhieuQuery, string>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<string> Handle(GetUrlMauPhieuQuery request, CancellationToken cancellationToken)
            {
                var idHe = request.IdHe;
                var idPhuongThuc = request.IdPhuongThuc;
                var mauPhieu = _dataContext.SvTuyenSinhMauPhieus.FirstOrDefault(x => x.IdHe == idHe && x.IdPhuongThucXetTuyen == idPhuongThuc);
                if (mauPhieu == null)
                {
                    return String.Empty;
                }
                return mauPhieu.UrlMauPhieuTuyenSinh;
            }
        }
    }

    public class GetComboboxMauPhieuQuery : IRequest<List<TuyenSinhMauPhieuDetailModel>>
    {
        public GetComboboxMauPhieuQuery()
        {
        }

        public class Handler : IRequestHandler<GetComboboxMauPhieuQuery, List<TuyenSinhMauPhieuDetailModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TuyenSinhMauPhieuDetailModel>> Handle(GetComboboxMauPhieuQuery request, CancellationToken cancellationToken)
            {
                string cacheKey = TuyenSinhMauPhieuConstant.BuildCacheKey();
                var result = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var query = (from dt in _dataContext.SvTuyenSinhMauPhieus
                                 select new TuyenSinhMauPhieuDetailModel
                                 {
                                     IdMauPhieuTuyenSinh = dt.IdMauPhieuTuyenSinh,
                                     IdPhuongThucXetTuyen = dt.IdPhuongThucXetTuyen,
                                     IdHe = dt.IdHe,
                                     UrlMauPhieuTuyenSinh = dt.UrlMauPhieuTuyenSinh,
                                     KetQuaHocBa = dt.KetQuaHocBa,
                                     KetQuaNangKhieu = dt.KetQuaNangKhieu,
                                     KetQuaNangLuc = dt.KetQuaNangLuc,
                                     KetQuaThiThpt = dt.KetQuaThiThpt
                                 });
                    return await query.ToListAsync();
                });
                return result;
            }
        }
    }
}
