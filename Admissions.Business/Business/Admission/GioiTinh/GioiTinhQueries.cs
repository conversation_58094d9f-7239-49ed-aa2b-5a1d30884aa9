using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Admissions.Data;
using Core.Business;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Admissions.Business
{
    public class GetComboboxGioiTinhQuery : IRequest<List<GioiTinhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy hệ cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxGioiTinhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxGioiTinhQuery, List<GioiTinhSelectItemModel>>
        {
            private readonly AdmissionReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(AdmissionReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<GioiTinhSelectItemModel>> Handle(GetComboboxGioiTinhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = GioiTinhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvGioiTinhs.OrderBy(x => x.GioiTinh)
                                select new GioiTinhSelectItemModel()
                                {
                                    IdGioiTinh = dt.IdGioiTinh,
                                    GioiTinh = dt.GioiTinh
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.GioiTinh.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
