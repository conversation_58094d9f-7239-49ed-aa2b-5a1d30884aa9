using Core.Business.Core.LockDynamic;
using Core.Business.Core;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Core.Business;

namespace Admissions.Business
{
    public static class ServiceConfigure
    {
        public static void Configure(IServiceCollection services)
        {
            // Apply MediaR Scanning to find all assemblies Requests and Responses
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ServiceConfigure).Assembly));

            // services.AddTransient<IElasticHandler, ElasticHandler>();
            services.AddTransient<ILockDynamicHandler, LockDynamicHandler>();
            services.AddTransient<ISendMailHandler, SendMailHandler>();
            services.AddTransient<IRedisHandler, RedisHandler>();
            services.AddTransient<ICallStoreHelper, CallStoreHelper>();
        }
    }
}
