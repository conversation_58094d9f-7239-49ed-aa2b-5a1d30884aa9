using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Core.Shared.EmailTemplate;
using Microsoft.Extensions.Logging;

namespace Core.Business
{
    public class SeedDataCommand : IRequest<Unit>
    {
        public SystemLogModel SystemLog { get; set; }

        public SeedDataCommand(SystemLogModel systemLog)
        {
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<SeedDataCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ILogger<SeedDataCommand> _logger;
            private readonly IMediator _mediator;
            private readonly ICacheService _cacheService;
            private readonly IConfiguration _config;

            public Handler(SystemDataContext dataContext, IMediator mediator, ICacheService cacheService, IConfiguration config, ILogger<SeedDataCommand> logger)
            {
                _config = config;
                _dataContext = dataContext;
                _mediator = mediator;
                _cacheService = cacheService;
                _logger = logger;
            }

            public async Task<Unit> Handle(SeedDataCommand request, CancellationToken cancellationToken)
            {
                _logger.LogInformation($"Khởi tạo dữ liệu hệ thống");

                #region Khởi tạo các bảng

                // sys_permission
                await CreateSysPermissionTableIfNotExists();

                // sys_file_attachment
                await CreateSysFileAttachmentTableIfNotExists();

                // sys_role_map_permission
                await CreateSysRoleMapPermissionTableIfNotExists();

                // sys_user_map_role
                await CreateSysUserMapRoleTableIfNotExists();

                // sys_email_template
                await CreateSysEmailTemplateTableIfNotExists();

                // [log_action]
                await CreateLogActionIfNotExists();

                // [log_forgot_password]
                await CreateLogForgotPasswordIfNotExists();

                //  [log_send_mail]
                await CreateLogSendMailIfNotExists();

                #endregion

                // UniSystem
                var maPhanHe = "UniSystem";
                int order = 0;

                // Thêm quyền của System

                var phanhe = await _dataContext.HtPhanHes.FirstOrDefaultAsync(x => x.PhanHe == maPhanHe);

                if (phanhe == null)
                {
                    phanhe = new HtPhanHe
                    {
                        PhanHe = maPhanHe,
                        MieuTa = "Phân hệ Quản trị hệ thống",
                        KyHieuPhanHe = "UAM",
                        HinhAnhPhanHe = "/assets/tmp/img/app/UniSystem.png",
                        Active = true,
                        STT = 1,
                        Hiden = false,
                    };
                    await _dataContext.HtPhanHes.AddAsync(phanhe);
                    _logger.LogInformation($"Thêm mới phân hệ {maPhanHe} vào hệ thống");
                }
                
                var listPermissionInDB = await _dataContext.Permissions.Where(x => x.IdPhanHe == phanhe.IdPh).ToListAsync();
                var listPermissionInConstant = new List<PermissionSeedModel>();

                foreach (PermissionSystemEnum permission in Enum.GetValues(typeof(PermissionSystemEnum)))
                {
                    FieldInfo field = permission.GetType().GetField(permission.ToString());
                    DisplayAttribute attribute = (DisplayAttribute)Attribute.GetCustomAttribute(field, typeof(DisplayAttribute));

                    string groupName = attribute.GroupName;
                    string name = attribute.Name;

                    listPermissionInConstant.Add(new PermissionSeedModel
                    {
                        Code = permission.ToString(),
                        Name = name,
                        GroupName = groupName,
                        IsActive = true,
                        Order = order++
                    });
                }

                var listPermissionNeedAdd = listPermissionInConstant.Where(x => !listPermissionInDB.Any(y => y.Code == x.Code)).ToList();
                if (listPermissionNeedAdd.Any())
                {
                    var listNeedAdd = AutoMapperUtils.AutoMap<PermissionSeedModel, Permission>(listPermissionNeedAdd);
                    foreach (var item in listNeedAdd)
                    {
                        item.IdPhanHe = phanhe.IdPh;
                        item.CreatedDate = DateTime.Now;
                    }
                    _logger.LogInformation($"Thêm mới {listNeedAdd.Count} quyền System vào hệ thống");
                    await _dataContext.Permissions.AddRangeAsync(listNeedAdd);
                }

                // Kiểm tra xem Role đã tồn tại chưa, nếu chưa tồn tại thì thêm Role Admin vào DB
                var roleExist = await _dataContext.Roles.AnyAsync();
                if (!roleExist)
                {
                    await _dataContext.Roles.AddAsync(new Role()
                    {
                        Name = "admin",
                        Description = "Administrator"
                    });
                    _logger.LogInformation($"Thêm mới nhóm người dùng Admin mặc định vào hệ thống");
                }

                #region Thêm mẫu email
                var listSeedEmail = EmailTemplateSeedConstant.ListSeedEmails;
                var listEmailInDB = await _dataContext.EmailTemplates.ToListAsync();
                int countEmail = 0;
                listSeedEmail.ForEach(async item =>
                {
                    var emailTemplate = listEmailInDB.FirstOrDefault(x => x.Code == item.Code);
                    if (emailTemplate == null)
                    {
                        emailTemplate = new EmailTemplate
                        {
                            Code = item.Code,
                            Name = item.Name,
                            Subject = item.Subject,
                            Template = item.Template,
                            IsActive = true,
                            Order = countEmail++,
                            CreatedDate = DateTime.Now
                        };
                        await _dataContext.EmailTemplates.AddAsync(emailTemplate);
                    }
                });

                _logger.LogInformation($"Thêm mới {countEmail} mẫu email vào hệ thống");

                #endregion

                #region Khởi tạo cấu hình quy trình

                var lstWorkFlowNameKeys = WorkFlowSeedConstants.WorkflowNameMapFunction.Keys.ToList();
                var lstWorkFlowNameValues = WorkFlowSeedConstants.WorkflowNameMapFunction.Values;

                var lstPhanHe = await _dataContext.HtPhanHes.Select(x => new { x.IdPh, x.MaPhanHe }).ToListAsync();
                var lstThamSo = await _dataContext.HtThamSoHeThongs.Select(x => new { x.IdPh, x.IdThamSo }).ToListAsync();
                int countThamSo = 0; 

                foreach (var item in WorkFlowSeedConstants.WorkflowNameMapFunction)
                {
                    var phanHe = lstPhanHe.FirstOrDefault(x => x.MaPhanHe == item.Value.MaPhanHe);
                    var thamSo = lstThamSo.FirstOrDefault(x => phanHe != null && x.IdPh == phanHe.IdPh && x.IdThamSo == item.Key);
                    if (thamSo == null && phanHe != null)
                    {
                        countThamSo++;

                        var thamSoHeThong = new HtThamSoHeThong
                        {
                            IdThamSo = item.Key,
                            Active = true,
                            DateModify = DateTime.Now,
                            GiaTri = item.Value.WorkFlowName,
                            NhomThamSo = "Cấu hình quy trình",
                            IdPh = phanHe.IdPh,
                            TenThamSo = item.Value.Function
                        };
                        
                        await _dataContext.HtThamSoHeThongs.AddAsync(thamSoHeThong);
                    }
                }

                _logger.LogInformation($"Thêm mới {countThamSo} tham số cấu hình quy trình vào hệ thống");

                #endregion

                await _dataContext.SaveChangesAsync();
                _logger.LogInformation($"Khởi tạo dữ liệu thành công");

                //Xóa cache
                _cacheService.RemoveAll();
                return Unit.Value;
            }

            private async Task CreateSysPermissionTableIfNotExists()
            {
                string createTableQuery = @"
                    IF OBJECT_ID('sys_permission', 'U') IS NULL
                    BEGIN
                        CREATE TABLE sys_permission (
	                        id int IDENTITY(1,1) NOT NULL,
	                        group_name nvarchar(64) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	                        code nvarchar(64) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	                        name nvarchar(128) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	                        id_phan_he int NOT NULL,
	                        [order] int NOT NULL,
	                        is_active bit NOT NULL,
	                        description nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        created_date datetime2 NULL,
	                        created_user_id int NULL,
	                        modified_date datetime2 NULL,
	                        modified_user_id int NULL,
	                        CONSTRAINT PK__sys_perm__3213E83F4B494AA2 PRIMARY KEY (id)
                        )
                    END";

                await _dataContext.Database.ExecuteSqlRawAsync(createTableQuery);
                _logger.LogInformation("Table sys_permission created (if it didn't exist).");
            }

            private async Task CreateSysRoleMapPermissionTableIfNotExists()
            {
                string createTableQuery = @"
                    IF OBJECT_ID('sys_role_map_permission', 'U') IS NULL
                    BEGIN
                        CREATE TABLE sys_role_map_permission (
	                        id int IDENTITY(1,1) NOT NULL,
	                        role_id int NOT NULL,
	                        permission_id int NOT NULL,
	                        CONSTRAINT PK__sys_role__3213E83F24C5EC27 PRIMARY KEY (id)
                        )
                    END";

                await _dataContext.Database.ExecuteSqlRawAsync(createTableQuery);
                _logger.LogInformation("Table sys_role_map_permission created (if it didn't exist).");
            }

            private async Task CreateSysUserMapRoleTableIfNotExists()
            {
                string createTableQuery = @"
                    IF OBJECT_ID('sys_user_map_role', 'U') IS NULL
                    BEGIN
                        CREATE TABLE sys_user_map_role (
                            id int IDENTITY(1,1) NOT NULL,
                            role_id int NOT NULL,
                            user_id int NOT NULL,
                            CONSTRAINT PK__sys_user__3213E83FE4FBD7B0 PRIMARY KEY (id)
                        )
                    END";

                await _dataContext.Database.ExecuteSqlRawAsync(createTableQuery);
                _logger.LogInformation("Table sys_user_map_role created (if it didn't exist).");
            }

            private async Task CreateSysFileAttachmentTableIfNotExists()
            {
                string createTableQuery = @"
                    IF OBJECT_ID('sys_file_attachment', 'U') IS NULL
                    BEGIN
                        CREATE TABLE sys_file_attachment (
	                        Id uniqueidentifier NOT NULL,
	                        BucketName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        ObjectName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        FileName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        FilePath nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        [Length] bigint NULL,
	                        [Source] int NULL,
	                        CreateUserName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        ModifyUserName nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        CreateDate datetime2 NULL,
	                        ModifyDate datetime2 NULL,
	                        CONSTRAINT PK_sys_file_attachment PRIMARY KEY (Id)
                        )
                    END";

                await _dataContext.Database.ExecuteSqlRawAsync(createTableQuery);
                _logger.LogInformation("Table sys_file_attachment created (if it didn't exist).");
            }

            private async Task CreateSysEmailTemplateTableIfNotExists()
            {
                string createTableQuery = @"
                    IF OBJECT_ID('sys_email_template', 'U') IS NULL
                    BEGIN
                        CREATE TABLE sys_email_template (
	                        id int IDENTITY(1,1) NOT NULL,
	                        [order] int NOT NULL,
	                        is_active bit NOT NULL,
	                        description nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        created_date datetime2 NULL,
	                        created_user_id int NULL,
	                        modified_date datetime2 NULL,
	                        modified_user_id int NULL,
	                        code nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        name nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        from_email nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        from_user nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        is_high_priority bit NOT NULL,
	                        cc nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        bcc nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        subject nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        template nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	                        CONSTRAINT PK_sys_email_template PRIMARY KEY (id)
                        )
                    END";

                await _dataContext.Database.ExecuteSqlRawAsync(createTableQuery);
                _logger.LogInformation("Table sys_email_template created (if it didn't exist).");
            }

            private async Task CreateLogActionIfNotExists()
            {
                string createTableQuery = @"
                    IF OBJECT_ID('log_action', 'U') IS NULL
                    BEGIN
                        CREATE TABLE [log_action] (
                            [id] int NOT NULL IDENTITY,
                            [correlation_id] nvarchar(max) NULL,
                            [trace_id] nvarchar(max) NULL,
                            [device_id] nvarchar(max) NULL,
                            [user_id] nvarchar(max) NULL,
                            [user_name] nvarchar(max) NULL,
                            [action_code] nvarchar(max) NULL,
                            [action_name] nvarchar(max) NULL,
                            [client_ip] nvarchar(max) NULL,
                            [created_date] datetime2 NOT NULL,
                            [object_code] nvarchar(max) NULL,
                            [object_id] nvarchar(max) NULL,
                            [request_method] nvarchar(max) NULL,
                            [request_path] nvarchar(max) NULL,
                            [user_agent] nvarchar(max) NULL,
                            [os] nvarchar(max) NULL,
                            [browser] nvarchar(max) NULL,
                            [client_info] nvarchar(max) NULL,
                            [description] nvarchar(max) NULL,
                            [time_execution] bigint NOT NULL,
                            [location_json] nvarchar(max) NULL,
                            [meta_data_json] nvarchar(max) NULL,
                            CONSTRAINT [PK_log_action] PRIMARY KEY ([id])
                        )
                    END";

                await _dataContext.Database.ExecuteSqlRawAsync(createTableQuery);
                _logger.LogInformation("Table log_action created (if it didn't exist).");
            }

            private async Task CreateLogForgotPasswordIfNotExists()
            {
                string createTableQuery = @"
                    IF OBJECT_ID('log_forgot_password', 'U') IS NULL
                    BEGIN
                        CREATE TABLE [log_forgot_password] (
                            [id] int NOT NULL IDENTITY,
                            [trace_id] nvarchar(max) NULL,
                            [user_id] int NOT NULL,
                            [user_name] nvarchar(max) NULL,
                            [user_email] nvarchar(max) NULL,
                            [token] nvarchar(max) NULL,
                            [is_updated_password] bit NOT NULL,
                            [created_date] datetime2 NOT NULL,
                            [expire_time] datetime2 NOT NULL,
                            CONSTRAINT [PK_log_forgot_password] PRIMARY KEY ([id])
                        )
                    END";

                await _dataContext.Database.ExecuteSqlRawAsync(createTableQuery);
                _logger.LogInformation("Table log_forgot_password created (if it didn't exist).");
            }

            private async Task CreateLogSendMailIfNotExists()
            {
                string createTableQuery = @"
                    IF OBJECT_ID('log_send_mail', 'U') IS NULL
                    BEGIN
                        CREATE TABLE [log_send_mail] (
                            [id] int NOT NULL IDENTITY,
                            [trace_id] nvarchar(max) NULL,
                            [to_email] nvarchar(max) NULL,
                            [email_temp_code] nvarchar(max) NULL,
                            [email_temp_name] nvarchar(max) NULL,
                            [subject] nvarchar(max) NULL,
                            [status] int NOT NULL,
                            [message_id] nvarchar(max) NULL,
                            [error_messsage] nvarchar(max) NULL,
                            [created_date] datetime2 NOT NULL,
                            CONSTRAINT [PK_log_send_mail] PRIMARY KEY ([id])
                        )
                    END";

                await _dataContext.Database.ExecuteSqlRawAsync(createTableQuery);
                _logger.LogInformation("Table log_send_mail created (if it didn't exist).");
            }
        }
    }
}