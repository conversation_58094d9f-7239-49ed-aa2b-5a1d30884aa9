using System.ComponentModel.DataAnnotations;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using Core.Shared;

namespace Core.Shared
{
    public class BaseEntity: TrackedChangeEntity
    {
        [Column("order", Order = 100)]
        public int Order { get; set; } = 0;

        [Column("is_active", Order = 101)]
        public bool IsActive { get; set; } = true;
    }

    public class TrackedChangeEntity
    {
        [Key]
        [Column("id", Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column("created_date", Order = 103)]
        public DateTime? CreatedDate { get; set; } = DateTime.Now;

        [Column("created_user_id", Order = 104)]
        public int? CreatedUserId { get; set; }

        [Column("created_user_name", Order = 105)]
        public string CreatedUserName { get; set; }

        [Column("modified_date", Order = 106)]
        public DateTime? ModifiedDate { get; set; }

        [Column("modified_user_id", Order = 107)]
        public int? ModifiedUserId { get; set; }

        [Column("modified_user_name", Order = 108)]
        public string ModifiedUserName { get; set; }
    }
}
