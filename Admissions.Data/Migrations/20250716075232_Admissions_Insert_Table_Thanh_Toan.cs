using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Admissions.Data.Migrations
{
    /// <inheritdoc />
    public partial class Admissions_Insert_Table_Thanh_Toan : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "svTuyenSinhThanhToan",
                columns: table => new
                {
                    ID_tuyen_sinh_thanh_toan = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ID_ho_so = table.Column<int>(type: "int", nullable: true),
                    ID_he = table.Column<int>(type: "int", nullable: true),
                    Nam_tuyen_sinh = table.Column<int>(type: "int", nullable: true),
                    Ngay_thang_giao_dich = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Ma_giao_dich = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Noi_dung = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Ngan_hang = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    So_tien = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Hinh_thuc_thanh_toan = table.Column<int>(type: "int", nullable: true),
                    Trang_thai = table.Column<int>(type: "int", nullable: true),
                    Ngay_tao = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Nguoi_tao = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Nguoi_sua = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_svTuyenSinhThanhToan", x => x.ID_tuyen_sinh_thanh_toan);
                });

            migrationBuilder.CreateTable(
                name: "svTuyenSinhThanhToanChiTiet",
                columns: table => new
                {
                    ID_tuyen_sinh_thanh_toan_chi_tiet = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ID_tuyen_sinh_thanh_toan = table.Column<int>(type: "int", nullable: true),
                    ID_dot = table.Column<int>(type: "int", nullable: true),
                    ID_tuyen_sinh_khoan_thu = table.Column<int>(type: "int", nullable: true),
                    So_luong = table.Column<int>(type: "int", nullable: true),
                    Don_gia = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Don_vi_tinh = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    So_tien = table.Column<decimal>(type: "decimal(18,2)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_svTuyenSinhThanhToanChiTiet", x => x.ID_tuyen_sinh_thanh_toan_chi_tiet);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "svTuyenSinhThanhToan");

            migrationBuilder.DropTable(
                name: "svTuyenSinhThanhToanChiTiet");
        }
    }
}
