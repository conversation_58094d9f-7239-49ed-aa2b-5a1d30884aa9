using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Admissions.Data.Migrations
{
    /// <inheritdoc />
    public partial class Admissions_Insert_Table_svTuyenSinhKhoanThu : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "svTuyenSinhKhoanThu",
                columns: table => new
                {
                    ID_tuyen_sinh_khoan_thu = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ID_dot_dang_ky = table.Column<int>(type: "int", nullable: true),
                    Ten_khoan_thu = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Don_vi_tinh = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    <PERSON><PERSON>_khoan_thu = table.Column<int>(type: "int", nullable: true),
                    So_tien = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Ngay_tao = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Nguoi_tao = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Ngay_sua = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Nguoi_sua = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_svTuyenSinhKhoanThu", x => x.ID_tuyen_sinh_khoan_thu);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "svTuyenSinhKhoanThu");
        }
    }
}
