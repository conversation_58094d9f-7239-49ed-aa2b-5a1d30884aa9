using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Admissions.Data.Migrations
{
    /// <inheritdoc />
    public partial class Admissions_Insert_Table_svTuyenSinhChungChi : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "svTuyenSinhChungChi",
                columns: table => new
                {
                    ID_tuyen_sinh_chung_chi = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Ten_chung_chi = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Loai_chung_chi = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_svTuyenSinhChungChi", x => x.ID_tuyen_sinh_chung_chi);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "svTuyenSinhChungChi");
        }
    }
}
