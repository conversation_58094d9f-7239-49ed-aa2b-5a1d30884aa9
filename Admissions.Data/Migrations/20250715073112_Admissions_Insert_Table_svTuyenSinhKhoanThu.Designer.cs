// <auto-generated />
using System;
using Admissions.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Admissions.Data.Migrations
{
    [DbContext(typeof(AdmissionDataContext))]
    [Migration("20250715073112_Admissions_Insert_Table_svTuyenSinhKhoanThu")]
    partial class Admissions_Insert_Table_svTuyenSinhKhoanThu
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Admissions.Data.SvChiTieuTuyenSinh", b =>
                {
                    b.Property<int>("IdChiTieuTuyenSinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_chi_tieu_tuyen_sinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdChiTieuTuyenSinh"));

                    b.Property<int>("ChiTieuTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Chi_tieu_tuyen_sinh");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ghi_chu");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    b.Property<int>("NamTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Nam_tuyen_sinh");

                    b.Property<DateTime?>("NgaySua")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sua");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tao");

                    b.Property<string>("NguoiSua")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_sua");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_tao");

                    b.HasKey("IdChiTieuTuyenSinh");

                    b.ToTable("svChiTieuTuyenSinh");
                });

            modelBuilder.Entity("Admissions.Data.SvChiTieuTuyenSinhPhuongThuc", b =>
                {
                    b.Property<int>("IdChiTieuTuyenSinhPhuongThuc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_chi_tieu_tuyen_sinh_phuong_thuc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdChiTieuTuyenSinhPhuongThuc"));

                    b.Property<int>("ChiTieuTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Chi_tieu_tuyen_sinh");

                    b.Property<string>("GhiChu")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ghi_chu");

                    b.Property<int>("IdChiTieuTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("ID_chi_tieu_tuyen_sinh");

                    b.Property<int>("IdPhuongThucXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    b.Property<DateTime?>("NgaySua")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sua");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tao");

                    b.Property<string>("NguoiSua")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_sua");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_tao");

                    b.HasKey("IdChiTieuTuyenSinhPhuongThuc");

                    b.ToTable("svChiTieuTuyenSinhPhuongThuc");
                });

            modelBuilder.Entity("Admissions.Data.SvDanToc", b =>
                {
                    b.Property<int>("IdDanToc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dan_toc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDanToc"));

                    b.Property<string>("DanToc")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Dan_toc");

                    b.Property<string>("DanTocEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Dan_toc_en");

                    b.Property<string>("MaDanToc")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_dan_toc");

                    b.HasKey("IdDanToc");

                    b.ToTable("svDanToc");
                });

            modelBuilder.Entity("Admissions.Data.SvDoiTuongTuyenSinh", b =>
                {
                    b.Property<int>("IdDoiTuongTuyenSinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_doi_tuong_tuyen_sinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDoiTuongTuyenSinh"));

                    b.Property<decimal?>("DiemCong")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem_cong");

                    b.Property<bool?>("DiemCongMax")
                        .HasColumnType("bit")
                        .HasColumnName("Diem_cong_max");

                    b.Property<string>("DoiTuongTuyenSinh")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Doi_tuong_tuyen_sinh");

                    b.Property<int?>("IdHoSoGiayTo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so_giay_to");

                    b.Property<string>("MaDoiTuong")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_doi_tuong");

                    b.Property<int?>("Nhom")
                        .HasColumnType("int")
                        .HasColumnName("Nhom");

                    b.Property<int?>("NhomChungChi")
                        .HasColumnType("int")
                        .HasColumnName("Nhom_chung_chi");

                    b.HasKey("IdDoiTuongTuyenSinh");

                    b.ToTable("svDoiTuongTuyenSinh");
                });

            modelBuilder.Entity("Admissions.Data.SvDoiTuongXetTuyen", b =>
                {
                    b.Property<int>("IdDoiTuongXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_doi_tuong_xet_tuyen");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDoiTuongXetTuyen"));

                    b.Property<int>("IdHoSoGiayTo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so_giay_to");

                    b.Property<int?>("IdPhuongThucXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    b.Property<string>("TenDoiTuongXetTuyen")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_doi_tuong_xet_tuyen");

                    b.HasKey("IdDoiTuongXetTuyen");

                    b.ToTable("svDoiTuongXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvGioiTinh", b =>
                {
                    b.Property<int>("IdGioiTinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_gioi_tinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdGioiTinh"));

                    b.Property<string>("GioiTinh")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("Gioi_tinh");

                    b.HasKey("IdGioiTinh");

                    b.ToTable("svGioiTinh");
                });

            modelBuilder.Entity("Admissions.Data.SvHe", b =>
                {
                    b.Property<int>("IdHe")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHe"));

                    b.Property<string>("HinhThucDaoTao")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Hinh_thuc_dao_tao");

                    b.Property<string>("HinhThucDaoTaoEn")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Hinh_thuc_dao_tao_en");

                    b.Property<string>("MaHe")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_he");

                    b.Property<int?>("QuyChe")
                        .HasColumnType("int")
                        .HasColumnName("Quy_che");

                    b.Property<string>("TenBacDaoTao")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_bac_dao_tao");

                    b.Property<string>("TenBacDaoTaoEn")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_bac_dao_tao_en");

                    b.Property<string>("TenHe")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_he");

                    b.Property<string>("TenHeEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_he_en");

                    b.HasKey("IdHe");

                    b.ToTable("svHe");
                });

            modelBuilder.Entity("Admissions.Data.SvHoSoNopTuyenSinh", b =>
                {
                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<int>("IdGiayTo")
                        .HasColumnType("int")
                        .HasColumnName("ID_giay_to");

                    b.Property<bool?>("DaTra")
                        .HasColumnType("bit")
                        .HasColumnName("Da_tra");

                    b.Property<string>("GhiChuNop")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu_nop");

                    b.Property<string>("GhiChuTra")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu_tra");

                    b.Property<int>("IdHoSoNopTuyenSinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so_nop_tuyen_sinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHoSoNopTuyenSinh"));

                    b.Property<DateTime?>("NgayNhan")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_nhan");

                    b.Property<DateTime?>("NgayTra")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tra");

                    b.Property<string>("NguoiNhan")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_nhan");

                    b.Property<string>("NguoiTra")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_tra");

                    b.HasKey("IdHoSo", "IdGiayTo");

                    b.ToTable("svHoSoNopTuyenSinh");
                });

            modelBuilder.Entity("Admissions.Data.SvHoSoNopTuyenSinhUrl", b =>
                {
                    b.Property<int>("IdHoSoNopTuyenSinhUrl")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so_nop_tuyen_sinh_url");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHoSoNopTuyenSinhUrl"));

                    b.Property<string>("FileName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("File_name");

                    b.Property<int>("IdHoSoNopTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so_nop_tuyen_sinh");

                    b.Property<int>("LoaiFile")
                        .HasColumnType("int")
                        .HasColumnName("Loai_file");

                    b.Property<string>("UrlGiayTo")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("URL_giay_to");

                    b.HasKey("IdHoSoNopTuyenSinhUrl");

                    b.ToTable("svHoSoNopTuyenSinhURL");
                });

            modelBuilder.Entity("Admissions.Data.SvHuyen", b =>
                {
                    b.Property<string>("IdHuyen")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_huyen");

                    b.Property<string>("IdHuyenCu")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_huyen_cu");

                    b.Property<string>("IdHuyenCu1")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_huyen_cu1");

                    b.Property<string>("IdTinh")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_tinh");

                    b.Property<string>("TenHuyen")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_huyen");

                    b.Property<string>("TenHuyenCu")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_huyen_cu");

                    b.Property<string>("TenHuyenEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_huyen_en");

                    b.HasKey("IdHuyen");

                    b.ToTable("svHuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvKhuVuc", b =>
                {
                    b.Property<int>("IdKv")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_Kv");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdKv"));

                    b.Property<decimal?>("DiemCongKv")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem_Cong_Kv");

                    b.Property<string>("MaKv")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("Ma_Kv");

                    b.Property<string>("TenKv")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_Kv");

                    b.HasKey("IdKv");

                    b.ToTable("svKhuVuc");
                });

            modelBuilder.Entity("Admissions.Data.SvLoaiGiayTo", b =>
                {
                    b.Property<int>("IdGiayTo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_giay_to");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdGiayTo"));

                    b.Property<bool?>("BatBuoc")
                        .HasColumnType("bit")
                        .HasColumnName("Bat_buoc");

                    b.Property<string>("GhiChu")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ghi_chu");

                    b.Property<int?>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<string>("MaGiayTo")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_giay_to");

                    b.Property<bool?>("MacDinh")
                        .HasColumnType("bit")
                        .HasColumnName("Mac_dinh");

                    b.Property<int?>("Nhom")
                        .HasColumnType("int")
                        .HasColumnName("Nhom");

                    b.Property<int?>("STT")
                        .HasColumnType("int")
                        .HasColumnName("STT");

                    b.Property<string>("TenGiayTo")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_giay_to");

                    b.Property<bool>("TuyenSinh")
                        .HasColumnType("bit")
                        .HasColumnName("Tuyen_sinh");

                    b.HasKey("IdGiayTo");

                    b.ToTable("svLoaiGiayTo");
                });

            modelBuilder.Entity("Admissions.Data.SvNganh", b =>
                {
                    b.Property<int>("IdNganh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNganh"));

                    b.Property<string>("MaNganh")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_nganh");

                    b.Property<bool>("SuPham")
                        .HasColumnType("bit")
                        .HasColumnName("Su_pham");

                    b.Property<string>("TenNganh")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_nganh");

                    b.Property<string>("TenNganhEn")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_nganh_en");

                    b.HasKey("IdNganh");

                    b.ToTable("svNganh");
                });

            modelBuilder.Entity("Admissions.Data.SvNhomChungChi", b =>
                {
                    b.Property<int>("IdNhomChungChi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nhom_chung_chi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNhomChungChi"));

                    b.Property<string>("KyHieuNhom")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ky_hieu_nhom");

                    b.Property<string>("NhomChungChi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Nhom_chung_chi");

                    b.HasKey("IdNhomChungChi");

                    b.ToTable("svNhomChungChi");
                });

            modelBuilder.Entity("Admissions.Data.SvNhomDoiTuong", b =>
                {
                    b.Property<int>("IdNhomDoiTuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nhom_doi_tuong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNhomDoiTuong"));

                    b.Property<string>("MaNhom")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_nhom");

                    b.Property<string>("TenNhom")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_nhom");

                    b.Property<string>("TenNhomEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_nhom_en");

                    b.HasKey("IdNhomDoiTuong");

                    b.ToTable("svNhomDoiTuong");
                });

            modelBuilder.Entity("Admissions.Data.SvQuocTich", b =>
                {
                    b.Property<int>("IdQuocTich")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_quoc_tich");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdQuocTich"));

                    b.Property<string>("MaQuocTich")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_quoc_tich");

                    b.Property<string>("QuocTich")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Quoc_tich");

                    b.HasKey("IdQuocTich");

                    b.ToTable("svQuocTich");
                });

            modelBuilder.Entity("Admissions.Data.SvTinh", b =>
                {
                    b.Property<string>("IdTinh")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_tinh");

                    b.Property<string>("MaTinh")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("Ma_tinh");

                    b.Property<string>("TenTinh")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_tinh");

                    b.Property<string>("TenTinhEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_tinh_en");

                    b.HasKey("IdTinh");

                    b.ToTable("svTinh");
                });

            modelBuilder.Entity("Admissions.Data.SvTruongTHPT", b =>
                {
                    b.Property<int>("IdTruongTHPT")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_truong_THPT");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTruongTHPT"));

                    b.Property<string>("DiaChiTruongTHPT")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Dia_chi_truong_THPT");

                    b.Property<int?>("IdKVTHPT")
                        .HasColumnType("int")
                        .HasColumnName("ID_kv_THPT");

                    b.Property<string>("MaHuyenTHPT")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_huyen_THPT");

                    b.Property<string>("MaTinhTHPT")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_tinh_THPT");

                    b.Property<string>("MaTruongTHPT")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_truong_THPT");

                    b.Property<string>("TenHuyenTHPT")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_huyen_THPT");

                    b.Property<string>("TenTinhTHPT")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_tinh_THPT");

                    b.Property<string>("TenTruongTHPT")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_truong_THPT");

                    b.Property<bool?>("TruongDTNT")
                        .HasColumnType("bit")
                        .HasColumnName("Truong_DTNT");

                    b.HasKey("IdTruongTHPT");

                    b.ToTable("svTruongTHPT");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhDangKyDot", b =>
                {
                    b.Property<int>("IdDotDangKy")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dot_dang_ky");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDotDangKy"));

                    b.Property<int>("ChiTieu")
                        .HasMaxLength(500)
                        .HasColumnType("int")
                        .HasColumnName("Chi_tieu");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ghi_chu");

                    b.Property<int>("IdDotTuyenSinh")
                        .HasMaxLength(20)
                        .HasColumnType("int")
                        .HasColumnName("ID_dot_tuyen_sinh");

                    b.Property<int>("IdHe")
                        .HasMaxLength(500)
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdNganh")
                        .HasMaxLength(500)
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    b.Property<int>("IdPhuongThucXetTuyen")
                        .HasMaxLength(500)
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    b.Property<int>("IdToHopXetTuyen")
                        .HasMaxLength(500)
                        .HasColumnType("int")
                        .HasColumnName("ID_to_hop_xet_tuyen");

                    b.HasKey("IdDotDangKy");

                    b.ToTable("svTuyenSinhDangKyDot");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhDangKyXetTuyen", b =>
                {
                    b.Property<int>("IdTuyenSinhDangKyXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_dang_ky_xet_tuyen");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhDangKyXetTuyen"));

                    b.Property<bool?>("DangKyHocChatLuongCao")
                        .HasColumnType("bit")
                        .HasColumnName("Dang_ky_hoc_chat_luong_cao");

                    b.Property<string>("GhiChu")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ghi_chu");

                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<int>("IdNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    b.Property<int>("IdPhuongThucXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    b.Property<int>("IdToHopXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_to_hop_xet_tuyen");

                    b.Property<int>("ThuTuXet")
                        .HasColumnType("int")
                        .HasColumnName("Thu_tu_xet");

                    b.Property<int>("TrungTuyen")
                        .HasColumnType("int")
                        .HasColumnName("Trung_tuyen");

                    b.HasKey("IdTuyenSinhDangKyXetTuyen");

                    b.ToTable("svTuyenSinhDangKyXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhDoiTuongXetTuyen", b =>
                {
                    b.Property<int>("IdTuyenSinhDoiTuongXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_doi_tuong_xet_tuyen");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhDoiTuongXetTuyen"));

                    b.Property<int>("IdDoiTuongXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_doi_tuong_xet_tuyen");

                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.HasKey("IdTuyenSinhDoiTuongXetTuyen");

                    b.ToTable("svTuyenSinhDoiTuongXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhDotDangKy", b =>
                {
                    b.Property<int>("IdDotDangKy")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dot_dang_ky");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDotDangKy"));

                    b.Property<DateTime>("DenNgayDangKy")
                        .HasColumnType("datetime2")
                        .HasColumnName("Den_ngay_dang_ky");

                    b.Property<string>("DotDangKy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Dot_dang_ky");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ghi_chu");

                    b.Property<bool>("HienThi")
                        .HasColumnType("bit")
                        .HasColumnName("Hien_thi");

                    b.Property<string>("IdAnh")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ID_anh");

                    b.Property<string>("IdFileDinhKem")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ID_file_dinh_kem");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdPhuongThucXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    b.Property<int>("NamTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Nam_tuyen_sinh");

                    b.Property<DateTime?>("NgayCongBo")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_cong_bo");

                    b.Property<DateTime?>("NgaySua")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sua");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tao");

                    b.Property<string>("NguoiSua")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_sua");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_tao");

                    b.Property<string>("TenDotDangKy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_dot_dang_ky");

                    b.Property<DateTime>("TuNgayDangKy")
                        .HasColumnType("datetime2")
                        .HasColumnName("Tu_ngay_dang_ky");

                    b.Property<string>("UrlMoTa")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Url_mo_ta");

                    b.HasKey("IdDotDangKy");

                    b.ToTable("svTuyenSinhDotDangKy");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhDotTuyenSinh", b =>
                {
                    b.Property<int>("IdDotTuyenSinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dot_tuyen_sinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDotTuyenSinh"));

                    b.Property<DateTime>("DenNgay")
                        .HasColumnType("datetime2")
                        .HasColumnName("Den_ngay");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("MaDotTuyenSinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ma_dot_tuyen_sinh");

                    b.Property<int>("NamTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Nam_tuyen_sinh");

                    b.Property<string>("TenDotTuyenSinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ten_dot_tuyen_sinh");

                    b.Property<DateTime>("ThoiGianCongBoKetQua")
                        .HasColumnType("datetime2")
                        .HasColumnName("Thoi_gian_cong_bo_ket_qua");

                    b.Property<DateTime>("TuNgay")
                        .HasColumnType("datetime2")
                        .HasColumnName("Tu_ngay");

                    b.HasKey("IdDotTuyenSinh");

                    b.ToTable("svTuyenSinhDotTuyenSinh");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhHoatDongNgoaiKhoa", b =>
                {
                    b.Property<int>("IdTuyenSinhHoatDongNgoaiKhoa")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_hoat_dong_ngoai_khoa");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhHoatDongNgoaiKhoa"));

                    b.Property<string>("HoatDongNgoaiKhoa")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Hoat_dong_ngoai_khoa");

                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<DateTime?>("NgaySua")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sua");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tao");

                    b.Property<string>("NguoiSua")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Nguoi_sua");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Nguoi_tao");

                    b.Property<string>("ThanhTichDatDuoc")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Thanh_tich_dat_duoc");

                    b.HasKey("IdTuyenSinhHoatDongNgoaiKhoa");

                    b.ToTable("svTuyenSinhHoatDongNgoaiKhoa");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhKetQuaDanhGiaNangLuc", b =>
                {
                    b.Property<int>("IdTuyenSinhKetQuaDanhGiaNangLuc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_ket_qua_danh_gia_nang_luc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhKetQuaDanhGiaNangLuc"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreateDate");

                    b.Property<string>("CreateUserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreateUserName");

                    b.Property<decimal>("Diem")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("Diem");

                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<int>("IdMonXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_xet_tuyen");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifyDate");

                    b.Property<string>("ModifyUserName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifyUserName");

                    b.HasKey("IdTuyenSinhKetQuaDanhGiaNangLuc");

                    b.ToTable("svTuyenSinhKetQuaDanhGiaNangLuc");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhKetQuaHocTapHocBa", b =>
                {
                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<int>("IdMonXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_xet_tuyen");

                    b.Property<decimal?>("Diem1")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem1");

                    b.Property<decimal?>("Diem2")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem2");

                    b.Property<decimal?>("Diem3")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem3");

                    b.Property<decimal?>("Diem4")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem4");

                    b.Property<decimal?>("Diem5")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem5");

                    b.Property<decimal?>("Diem6")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem6");

                    b.Property<int>("IdTuyenSinhKetQuaHocTap")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_ket_qua_hoc_tap");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhKetQuaHocTap"));

                    b.HasKey("IdHoSo", "IdMonXetTuyen");

                    b.ToTable("svTuyenSinhKetQuaHocTapHocBa");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhKetQuaThacSi", b =>
                {
                    b.Property<int>("IdTuyenSinhKetQuaThacSi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_ket_qua_thac_si");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhKetQuaThacSi"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreateDate");

                    b.Property<string>("CreateUserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreateUserName");

                    b.Property<decimal>("Diem")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("Diem");

                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<int>("IdMonXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_xet_tuyen");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifyDate");

                    b.Property<string>("ModifyUserName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifyUserName");

                    b.HasKey("IdTuyenSinhKetQuaThacSi");

                    b.ToTable("svTuyenSinhKetQuaThacSi");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhKetQuaThiNangKhieu", b =>
                {
                    b.Property<int>("IdTuyenSinhKetQuaThiNangKhieu")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_ket_qua_thi_nang_khieu");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhKetQuaThiNangKhieu"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreateDate");

                    b.Property<string>("CreateUserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CreateUserName");

                    b.Property<decimal>("Diem")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("Diem");

                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<int>("IdMonXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_xet_tuyen");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifyDate");

                    b.Property<string>("ModifyUserName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifyUserName");

                    b.HasKey("IdTuyenSinhKetQuaThiNangKhieu");

                    b.ToTable("svTuyenSinhKetQuaThiNangKhieu");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhKetQuaThiTHPT", b =>
                {
                    b.Property<int>("IdTuyenSinhKetQuaThiThpt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_ket_qua_thi_thpt");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhKetQuaThiThpt"));

                    b.Property<DateTime?>("CreateDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreateDate");

                    b.Property<string>("CreateUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreateUserName");

                    b.Property<decimal>("Diem")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem");

                    b.Property<int>("IdHoSo")
                        .HasMaxLength(500)
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<int>("IdMonXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_xet_tuyen");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifyDate");

                    b.Property<string>("ModifyUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifyUserName");

                    b.HasKey("IdTuyenSinhKetQuaThiThpt");

                    b.ToTable("svTuyenSinhKetQuaThiTHPT");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhKhoanThu", b =>
                {
                    b.Property<int>("IdTuyenSinhKhoanThu")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_khoan_thu");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhKhoanThu"));

                    b.Property<string>("DonViTinh")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Don_vi_tinh");

                    b.Property<int?>("IdDotDangKy")
                        .HasColumnType("int")
                        .HasColumnName("ID_dot_dang_ky");

                    b.Property<int?>("LoaiKhoanThu")
                        .HasColumnType("int")
                        .HasColumnName("Loai_khoan_thu");

                    b.Property<DateTime?>("NgaySua")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sua");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tao");

                    b.Property<string>("NguoiSua")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_sua");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nguoi_tao");

                    b.Property<decimal?>("SoTien")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("So_tien");

                    b.Property<string>("TenKhoanThu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_khoan_thu");

                    b.HasKey("IdTuyenSinhKhoanThu");

                    b.ToTable("svTuyenSinhKhoanThu");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhKhoiNganh", b =>
                {
                    b.Property<int>("IdKhoiNganh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_khoi_nganh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdKhoiNganh"));

                    b.Property<string>("MaKhoiNganh")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_khoi_nganh");

                    b.Property<string>("TenKhoiNganh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_khoi_nganh");

                    b.HasKey("IdKhoiNganh");

                    b.ToTable("svTuyenSinhKhoiNganh");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhLoaiDoiTuong", b =>
                {
                    b.Property<int>("IdTuyenSinhLoaiDoiTuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_loai_doi_tuong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhLoaiDoiTuong"));

                    b.Property<string>("MaLoaiDoiTuong")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ma_loai_doi_tuong");

                    b.Property<DateTime?>("NgaySua")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sua");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tao");

                    b.Property<string>("NguoiSua")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Nguoi_sua");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Nguoi_tao");

                    b.Property<string>("TenLoaiDoiTuong")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_loai_doi_tuong");

                    b.HasKey("IdTuyenSinhLoaiDoiTuong");

                    b.ToTable("svTuyenSinhLoaiDoiTuong");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhLoaiMon", b =>
                {
                    b.Property<int>("IdLoaiMon")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_mon");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdLoaiMon"));

                    b.Property<string>("MaLoaiMon")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_loai_mon");

                    b.Property<string>("TenLoaiMon")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_loai_mon");

                    b.HasKey("IdLoaiMon");

                    b.ToTable("svTuyenSinhLoaiMon");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhLoaiNganh", b =>
                {
                    b.Property<int>("IdLoaiNganh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_nganh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdLoaiNganh"));

                    b.Property<string>("MaLoaiNganh")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_loai_nganh");

                    b.Property<string>("TenLoaiNganh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_loai_nganh");

                    b.HasKey("IdLoaiNganh");

                    b.ToTable("svTuyenSinhLoaiNganh");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhMauPhieu", b =>
                {
                    b.Property<int>("IdMauPhieuTuyenSinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_mau_phieu_tuyen_sinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdMauPhieuTuyenSinh"));

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdPhuongThucXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    b.Property<bool?>("KetQuaHocBa")
                        .HasColumnType("bit")
                        .HasColumnName("Ket_qua_hoc_ba");

                    b.Property<bool?>("KetQuaNangKhieu")
                        .HasColumnType("bit")
                        .HasColumnName("Ket_qua_nang_khieu");

                    b.Property<bool?>("KetQuaNangLuc")
                        .HasColumnType("bit")
                        .HasColumnName("Ket_qua_nang_luc");

                    b.Property<bool?>("KetQuaThiThpt")
                        .HasColumnType("bit")
                        .HasColumnName("Ket_qua_thi_thpt");

                    b.Property<string>("UrlMauPhieuTuyenSinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Url_mau_phieu_tuyen_sinh");

                    b.HasKey("IdMauPhieuTuyenSinh");

                    b.ToTable("svTuyenSinhMauPhieu");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhMonXetTuyen", b =>
                {
                    b.Property<int>("IdMonXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_xet_tuyen");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdMonXetTuyen"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu");

                    b.Property<decimal?>("HeSoMon")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("He_so_mon");

                    b.Property<string>("MaMonXetTuyen")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_mon_xet_tuyen");

                    b.Property<bool>("MonNangKhieu")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_nang_khieu");

                    b.Property<string>("TenMonXetTuyen")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_mon_xet_tuyen");

                    b.HasKey("IdMonXetTuyen");

                    b.ToTable("svTuyenSinhMonXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhNganh", b =>
                {
                    b.Property<int>("IdNganh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNganh"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu");

                    b.Property<bool>("HienThi")
                        .HasColumnType("bit")
                        .HasColumnName("Hien_thi");

                    b.Property<int>("IdKhoiNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_khoi_nganh");

                    b.Property<int>("IdLoaiNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_nganh");

                    b.Property<int>("IdNganhChuan")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh_chuan");

                    b.Property<int>("IdNhomNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_nhom_nganh");

                    b.Property<string>("MaNganh")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_nganh");

                    b.Property<string>("TenNganhDaoTao")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_nganh_dao_tao");

                    b.HasKey("IdNganh");

                    b.ToTable("svTuyenSinhNganh");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhNganhChuan", b =>
                {
                    b.Property<int>("IdNganhChuan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh_chuan");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNganhChuan"));

                    b.Property<string>("MaNganhChuan")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_nganh_chuan");

                    b.Property<string>("TenNganhChuan")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_nganh_chuan");

                    b.HasKey("IdNganhChuan");

                    b.ToTable("svTuyenSinhNganhChuan");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhNhomNganh", b =>
                {
                    b.Property<int>("IdNhomNganh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nhom_nganh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNhomNganh"));

                    b.Property<string>("MaNhomNganh")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_nhom_nganh");

                    b.Property<string>("TenNhomNganh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_nhom_nganh");

                    b.HasKey("IdNhomNganh");

                    b.ToTable("svTuyenSinhNhomNganh");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhPhuongAnDiemHocBa", b =>
                {
                    b.Property<int>("IdPhuongAnDiemHocBa")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_an_diem_hoc_ba");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdPhuongAnDiemHocBa"));

                    b.Property<string>("CongThucTinh")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Cong_thuc_tinh");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("MaPhuongAnDiemHocBa")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_phuong_an_diem_hoc_ba");

                    b.Property<string>("TenDiem1")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_diem_1");

                    b.Property<string>("TenDiem2")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_diem_2");

                    b.Property<string>("TenDiem3")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_diem_3");

                    b.Property<string>("TenDiem4")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_diem_4");

                    b.Property<string>("TenDiem5")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_diem_5");

                    b.Property<string>("TenDiem6")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_diem_6");

                    b.Property<string>("TenPhuongAnDiemHocBa")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_phuong_an_diem_hoc_ba");

                    b.Property<string>("TieuDePhuongAnDiemHocBa")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Tieu_de_phuong_an_diem_hoc_ba");

                    b.HasKey("IdPhuongAnDiemHocBa");

                    b.ToTable("svTuyenSinhPhuongAnDiemHocBa");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhPhuongAnMon", b =>
                {
                    b.Property<int>("IdTuyenSinhPhuongAnMon")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_phuong_an_mon");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhPhuongAnMon"));

                    b.Property<int>("IdMonXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_xet_tuyen");

                    b.Property<int>("IdPhuongAnDiemHocBa")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_an_diem_hoc_ba");

                    b.Property<DateTime?>("NgaySua")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sua");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tao");

                    b.Property<string>("NguoiSua")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Nguoi_sua");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Nguoi_tao");

                    b.HasKey("IdTuyenSinhPhuongAnMon");

                    b.ToTable("svTuyenSinhPhuongAnMon");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhPhuongThucXetTuyen", b =>
                {
                    b.Property<int>("IdPhuongThucXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdPhuongThucXetTuyen"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("MaPhuongThucXetTuyen")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_phuong_thuc_xet_tuyen");

                    b.Property<string>("TenPhuongThucXetTuyen")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_phuong_thuc_xet_tuyen");

                    b.HasKey("IdPhuongThucXetTuyen");

                    b.ToTable("svTuyenSinhPhuongThucXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhThiSinhDangKyXetTuyen", b =>
                {
                    b.Property<int>("IdThiSinhDangKyXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_thi_sinh_dang_ky_xet_tuyen");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdThiSinhDangKyXetTuyen"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreateDate");

                    b.Property<string>("CreateUserName")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("CreateUserName");

                    b.Property<string>("GhiChuDuyetHoSo")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ghi_chu_duyet_ho_so");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdHoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    b.Property<int>("IdPhuongAnDiemHocBa")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_an_diem_hoc_ba");

                    b.Property<int>("IdPhuongThucXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    b.Property<bool>("KetQuaXetTuyen")
                        .HasColumnType("bit")
                        .HasColumnName("Ket_qua_xet_tuyen");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifyDate");

                    b.Property<string>("ModifyUserName")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("ModifyUserName");

                    b.Property<int>("NamTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Nam_tuyen_sinh");

                    b.Property<string>("NguoiDuyetHoSo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_duyet_ho_so");

                    b.Property<string>("NguoiDuyetTaiChinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_duyet_tai_chinh");

                    b.Property<int?>("SoTienLePhiDaNop")
                        .HasColumnType("int")
                        .HasColumnName("So_tien_le_phi_da_nop");

                    b.Property<int?>("SoTienLePhiPhaiNop")
                        .HasColumnType("int")
                        .HasColumnName("So_tien_le_phi_phai_nop");

                    b.Property<int>("TrangThaiHoSo")
                        .HasColumnType("int")
                        .HasColumnName("Trang_thai_ho_so");

                    b.Property<int>("TrangThaiTaiChinh")
                        .HasColumnType("int")
                        .HasColumnName("Trang_thai_tai_chinh");

                    b.HasKey("IdThiSinhDangKyXetTuyen");

                    b.ToTable("svTuyenSinhThiSinhDangKyXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhToHopMonXetTuyen", b =>
                {
                    b.Property<int>("IdTuyenSinhToHopMonXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tuyen_sinh_to_hop_mon_xet_tuyen");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhToHopMonXetTuyen"));

                    b.Property<decimal?>("HeSo")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("He_so");

                    b.Property<int>("IdMonXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_xet_tuyen");

                    b.Property<int>("IdToHopXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_to_hop_xet_tuyen");

                    b.HasKey("IdTuyenSinhToHopMonXetTuyen");

                    b.ToTable("svTuyenSinhToHopMonXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhToHopNganhXetTuyen", b =>
                {
                    b.Property<int>("IdTuyenSinhToHopNganhXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_to_hop_xet_tuyen_nganh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTuyenSinhToHopNganhXetTuyen"));

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    b.Property<int>("IdPhuongThucXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_xet_tuyen");

                    b.Property<int>("IdToHopXetTuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_to_hop_xet_tuyen");

                    b.HasKey("IdTuyenSinhToHopNganhXetTuyen");

                    b.ToTable("svTuyenSinhToHopNganhXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvTuyenSinhToHopXetTuyen", b =>
                {
                    b.Property<int>("IdToHopXetTuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_to_hop_xet_tuyen");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdToHopXetTuyen"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("MaToHop")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_to_hop");

                    b.Property<string>("TenToHopXetTuyen")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_to_hop_xet_tuyen");

                    b.HasKey("IdToHopXetTuyen");

                    b.ToTable("svTuyenSinhToHopXetTuyen");
                });

            modelBuilder.Entity("Admissions.Data.SvXa", b =>
                {
                    b.Property<string>("IdXa")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)")
                        .HasColumnName("ID_xa");

                    b.Property<string>("IdHuyen")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)")
                        .HasColumnName("ID_huyen");

                    b.Property<string>("TenXa")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("Ten_xa");

                    b.Property<string>("TenXaEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_xa_en");

                    b.HasKey("IdXa");

                    b.ToTable("svXa");
                });

            modelBuilder.Entity("Admissions.Data.SvXepLoaiHanhKiemTHPT", b =>
                {
                    b.Property<int>("IdXepLoaiHanhKiemLop12")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai_hanh_kiem_lop12");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepLoaiHanhKiemLop12"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("MaXepLoaiHanhKiemLop12")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_xep_loai_hanh_kiem_lop12");

                    b.Property<string>("TenXepLoaiHanhKiemLop12")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_xep_loai_hanh_kiem_lop12");

                    b.HasKey("IdXepLoaiHanhKiemLop12");

                    b.ToTable("svXepLoaiHanhKiemTHPT");
                });

            modelBuilder.Entity("Admissions.Data.SvXepLoaiHocTapTHPT", b =>
                {
                    b.Property<int>("IdXepLoaiHocTapLop12")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai_hoc_tap_lop12");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepLoaiHocTapLop12"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("MaXepLoaiHocTapLop12")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_xep_loai_hoc_tap_lop12");

                    b.Property<string>("TenXepLoaiHocTapLop12")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_xep_loai_hoc_tap_lop12");

                    b.HasKey("IdXepLoaiHocTapLop12");

                    b.ToTable("svXepLoaiHocTapTHPT");
                });

            modelBuilder.Entity("Admissions.Data.svHoSoTuyenSinh", b =>
                {
                    b.Property<int>("IdHoSo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_ho_so");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHoSo"));

                    b.Property<int?>("BoTucKienThuc")
                        .HasColumnType("int")
                        .HasColumnName("Bo_tuc_kien_thuc");

                    b.Property<string>("CMND")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("CMND");

                    b.Property<string>("ChucVuDonViCongTac")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Chuc_vu_don_vi_cong_tac");

                    b.Property<bool?>("CoQuanCuDiHoc")
                        .HasColumnType("bit")
                        .HasColumnName("Co_quan_cu_di_hoc");

                    b.Property<DateTime?>("CreateDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreateDate");

                    b.Property<string>("CreateUserName")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("CreateUserName");

                    b.Property<bool?>("DaCoBangSoCap")
                        .HasColumnType("bit")
                        .HasColumnName("Da_co_bang_so_cap");

                    b.Property<string>("DiaChiBaoTin")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Dia_chi_bao_tin");

                    b.Property<string>("DiaChiLienHeBo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Dia_chi_lien_he_bo");

                    b.Property<string>("DiaChiLienHeMe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Dia_chi_lien_he_me");

                    b.Property<string>("DiaChiTruongPtth")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Dia_chi_truong_PTTH");

                    b.Property<string>("DiaChiTt")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Dia_chi_tt");

                    b.Property<float?>("Diem1")
                        .HasColumnType("real")
                        .HasColumnName("Diem1");

                    b.Property<float?>("Diem2")
                        .HasColumnType("real")
                        .HasColumnName("Diem2");

                    b.Property<float?>("Diem3")
                        .HasColumnType("real")
                        .HasColumnName("Diem3");

                    b.Property<float?>("Diem4")
                        .HasColumnType("real")
                        .HasColumnName("Diem4");

                    b.Property<float?>("DiemTbhk")
                        .HasColumnType("real")
                        .HasColumnName("DiemTBHK");

                    b.Property<float?>("DiemUtDt")
                        .HasColumnType("real")
                        .HasColumnName("Diem_UT_DT");

                    b.Property<float?>("DiemUtKv")
                        .HasColumnType("real")
                        .HasColumnName("Diem_UT_KV");

                    b.Property<string>("DienThoaiCQ")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Dien_thoai_CQ");

                    b.Property<string>("DienThoaiCQBo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Dien_thoai_CQ_bo");

                    b.Property<string>("DienThoaiCQMe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Dien_thoai_CQ_me");

                    b.Property<string>("DienThoaiCaNhan")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Dienthoai_canhan");

                    b.Property<string>("DienThoaiNR")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Dien_thoai_NR");

                    b.Property<int?>("DoiTuongDuThi")
                        .HasColumnType("int")
                        .HasColumnName("Doi_tuong_du_thi");

                    b.Property<string>("DonVi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Don_vi");

                    b.Property<int?>("DotThiTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Dot_thi_tuyen_sinh");

                    b.Property<string>("Email")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Email");

                    b.Property<string>("EmailBo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Email_bo");

                    b.Property<string>("EmailMe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Email_me");

                    b.Property<string>("GhiChuTuyenSinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ghi_chu_tuyen_sinh");

                    b.Property<string>("HinhThucDaoTaoBang1")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Hinh_thuc_dao_tao_bang_1");

                    b.Property<string>("HinhThucDaoTaoBang2")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Hinh_thuc_dao_tao_bang_2");

                    b.Property<string>("HinhThucDaoTaoThacSi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Hinh_thuc_dao_tao_thac_si");

                    b.Property<bool?>("HoKhauTtKv1")
                        .HasColumnType("bit")
                        .HasColumnName("Ho_khau_tt_kv1");

                    b.Property<bool?>("HoKhauTtXaKhoKhan")
                        .HasColumnType("bit")
                        .HasColumnName("Ho_khau_tt_xa_kho_khan");

                    b.Property<bool?>("HoSoXoa")
                        .HasColumnType("bit")
                        .HasColumnName("Ho_so_xoa");

                    b.Property<string>("HoTen")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ho_ten");

                    b.Property<string>("HoTenCha")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ho_ten_cha");

                    b.Property<string>("HoTenMe")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ho_ten_me");

                    b.Property<string>("HoTenNguoiNhan")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ho_ten_nguoi_nhan");

                    b.Property<bool?>("HocVanHoaThpt")
                        .HasColumnType("bit")
                        .HasColumnName("Hoc_van_hoa_THPT");

                    b.Property<string>("IdAnhThiSinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ID_anh_thi_sinh");

                    b.Property<int?>("IdDanToc")
                        .HasColumnType("int")
                        .HasColumnName("ID_dan_toc");

                    b.Property<int?>("IdDoiTuongTS")
                        .HasColumnType("int")
                        .HasColumnName("ID_doi_tuong_TS");

                    b.Property<int?>("IdDoiTuongTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("ID_doi_tuong_tuyen_sinh");

                    b.Property<int?>("IdGiaoVienTuVan")
                        .HasColumnType("int")
                        .HasColumnName("ID_giao_vien_tu_van");

                    b.Property<int?>("IdGioiTinh")
                        .HasColumnType("int")
                        .HasColumnName("ID_gioi_tinh");

                    b.Property<int?>("IdHanhKiemLop12")
                        .HasColumnType("int")
                        .HasColumnName("ID_hanh_kiem_lop12");

                    b.Property<int?>("IdHeNhapHoc")
                        .HasColumnType("int")
                        .HasColumnName("id_he_nhap_hoc");

                    b.Property<string>("IdHuyenTt")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_huyen_tt");

                    b.Property<int?>("IdKhoaNhapHoc")
                        .HasColumnType("int")
                        .HasColumnName("ID_khoa_nhap_hoc");

                    b.Property<int?>("IdKhuVucTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("ID_khu_vuc_tuyen_sinh");

                    b.Property<int?>("IdLoaiDoiTuong")
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_doi_tuong");

                    b.Property<int?>("IdNganhBang1")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh_bang_1");

                    b.Property<int?>("IdNganhBang2")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh_bang_2");

                    b.Property<int?>("IdNganhNhapHoc")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh_nhap_hoc");

                    b.Property<int?>("IdNganhNhapHoc2")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh_nhap_hoc2");

                    b.Property<int?>("IdNganhThacSi")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh_thac_si");

                    b.Property<int?>("IdNguonTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("ID_nguon_tuyen_sinh");

                    b.Property<string>("IdNoiCapCMND")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ID_noi_cap_CMND");

                    b.Property<int?>("IdQuocTich")
                        .HasColumnType("int")
                        .HasColumnName("ID_quoc_tich");

                    b.Property<string>("IdTinhNs")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_tinh_ns");

                    b.Property<string>("IdTinhTt")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_tinh_tt");

                    b.Property<int?>("IdTruongTHPTLop10")
                        .HasColumnType("int")
                        .HasColumnName("ID_truong_THPT_lop10");

                    b.Property<int?>("IdTruongTHPTLop11")
                        .HasColumnType("int")
                        .HasColumnName("ID_truong_THPT_lop11");

                    b.Property<int?>("IdTruongTHPTLop12")
                        .HasColumnType("int")
                        .HasColumnName("ID_truong_THPT_lop12");

                    b.Property<int?>("IdXepLoaiHanhKiemLop10")
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai_hanh_kiem_lop10");

                    b.Property<int?>("IdXepLoaiHanhKiemLop11")
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai_hanh_kiem_lop11");

                    b.Property<int?>("IdXepLoaiHanhKiemLop12")
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai_hanh_kiem_lop12");

                    b.Property<int?>("IdXepLoaiHocTapLop12")
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai_hoc_tap_lop12");

                    b.Property<string>("LinhVucChuyenMon")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Linh_vuc_chuyen_mon");

                    b.Property<int?>("LoaiHopDongHienTai")
                        .HasColumnType("int")
                        .HasColumnName("Loai_hop_dong_hien_tai");

                    b.Property<string>("LoaiTotNghiepBang1")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Loai_tot_nghiep_bang_1");

                    b.Property<string>("LoaiTotNghiepBang2")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Loai_tot_nghiep_bang_2");

                    b.Property<string>("MaHoSo")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_ho_so");

                    b.Property<string>("MaTruongThpt")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ma_truong_THPT");

                    b.Property<string>("MatKhauThiSinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Mat_khau_thi_sinh");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifyDate");

                    b.Property<string>("ModifyUserName")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("ModifyUserName");

                    b.Property<string>("NamBatDauCongTac")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nam_bat_dau_cong_tac");

                    b.Property<int?>("NamSinhCha")
                        .HasColumnType("int")
                        .HasColumnName("Nam_sinh_cha");

                    b.Property<int?>("NamSinhMe")
                        .HasColumnType("int")
                        .HasColumnName("Nam_sinh_me");

                    b.Property<string>("NamTotNghiep")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Nam_tot_nghiep");

                    b.Property<string>("NamTotNghiepBang1")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Nam_tot_nghiep_bang_1");

                    b.Property<string>("NamTotNghiepBang2")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Nam_tot_nghiep_bang_2");

                    b.Property<string>("NamTotNghiepThacSi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Nam_tot_nghiep_thac_si");

                    b.Property<int?>("NamTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Nam_tuyen_sinh");

                    b.Property<DateTime?>("NgayCapCMND")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_cap_CMND");

                    b.Property<DateTime>("NgaySinh")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sinh");

                    b.Property<DateTime?>("NgayTiepNhan")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_tiep_nhan");

                    b.Property<string>("NgheNghiep")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Nghe_nghiep");

                    b.Property<string>("NgheNghiepCha")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nghe_nghiep_cha");

                    b.Property<string>("NgheNghiepMe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nghe_nghiep_me");

                    b.Property<string>("NguoiThamKhao1DiaChi")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_1_dia_chi");

                    b.Property<string>("NguoiThamKhao1DienThoai")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_1_dien_thoai");

                    b.Property<string>("NguoiThamKhao1Email")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_1_email");

                    b.Property<string>("NguoiThamKhao1HoTen")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_1_ho_ten");

                    b.Property<string>("NguoiThamKhao1NgheNghiep")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_1_nghe_nghiep");

                    b.Property<string>("NguoiThamKhao1NoiCongTac")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_1_noi_cong_tac");

                    b.Property<string>("NguoiThamKhao1QuanHe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_1_quan_he");

                    b.Property<string>("NguoiThamKhao2DiaChi")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_2_dia_chi");

                    b.Property<string>("NguoiThamKhao2DienThoai")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_2_dien_thoai");

                    b.Property<string>("NguoiThamKhao2Email")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_2_email");

                    b.Property<string>("NguoiThamKhao2HoTen")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_2_ho_ten");

                    b.Property<string>("NguoiThamKhao2NgheNghiep")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_2_nghe_nghiep");

                    b.Property<string>("NguoiThamKhao2NoiCongTac")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_2_noi_cong_tac");

                    b.Property<string>("NguoiThamKhao2QuanHe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tham_khao_2_quan_he");

                    b.Property<string>("NguoiTiepNhan")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Nguoi_tiep_nhan");

                    b.Property<string>("NoiDangKyBaoHiem")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Noi_dang_ky_bao_hiem");

                    b.Property<bool>("NopLePhi")
                        .HasColumnType("bit")
                        .HasColumnName("Nop_le_phi");

                    b.Property<string>("QueQuan")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Que_quan");

                    b.Property<bool?>("RutHoSo")
                        .HasColumnType("bit")
                        .HasColumnName("Rut_ho_so");

                    b.Property<string>("SBD")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("SBD");

                    b.Property<string>("SizeAo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Size_Ao");

                    b.Property<string>("SizeQuan")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Size_quan");

                    b.Property<string>("SoDienThoaiBo")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("So_dien_thoai_bo");

                    b.Property<string>("SoDienThoaiMe")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("So_dien_thoai_me");

                    b.Property<string>("SoDienThoaiNguoiNhan")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("So_dien_thoai_nguoi_nhan");

                    b.Property<string>("SoDienThoaiNguoiTiepNhan")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("So_dien_thoai_nguoi_tiep_nhan");

                    b.Property<int?>("SoLuongBaiBao")
                        .HasColumnType("int")
                        .HasColumnName("So_luong_bai_bao");

                    b.Property<string>("TenTruongBang1")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_truong_bang_1");

                    b.Property<string>("TenTruongBang2")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_truong_bang_2");

                    b.Property<string>("TenTruongThacSi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_truong_thac_si");

                    b.Property<string>("TenTruongThpt")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ten_truongTHPT");

                    b.Property<int?>("ThamNienNgheNghiep")
                        .HasColumnType("int")
                        .HasColumnName("Tham_nien_nghe_nghiep");

                    b.Property<string>("TonGiao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ton_giao");

                    b.Property<float?>("TongDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tong_diem");

                    b.Property<string>("TrinhDoNgoaiNgu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Trinh_do_ngoai_ngu");

                    b.Property<string>("TrinhDoVanHoa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Trinh_do_van_hoa");

                    b.Property<bool?>("TrungTuyen")
                        .HasColumnType("bit")
                        .HasColumnName("Trung_tuyen");

                    b.Property<bool?>("TrungTuyenNv2")
                        .HasColumnType("bit")
                        .HasColumnName("Trung_tuyen_nv2");

                    b.Property<string>("TuVanTuyenSinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Tu_van_tuyen_sinh");

                    b.Property<string>("XaPhuongTt")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Xa_phuong_tt");

                    b.HasKey("IdHoSo");

                    b.ToTable("svHoSoTuyenSinh");
                });
#pragma warning restore 612, 618
        }
    }
}
