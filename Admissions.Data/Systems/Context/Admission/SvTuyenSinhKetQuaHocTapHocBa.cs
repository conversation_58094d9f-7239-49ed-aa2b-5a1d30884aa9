using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTuyenSinhKetQuaHocTapHocBa")]
    public class SvTuyenSinhKetQuaHocTapHocBa
    {
        public SvTuyenSinhKetQuaHocTapHocBa()
        {

        }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_tuyen_sinh_ket_qua_hoc_tap")]
        public int IdTuyenSinhKetQuaHocTap { get; set; }

        [Column("ID_ho_so")]
        public int IdHoSo { get; set; }

        [Column("ID_mon_xet_tuyen")]
        public int IdMonXetTuyen { get; set; }

        [Column("Diem1")]
        public decimal? Diem1 { get; set; }

        [Column("Diem2")]
        public decimal? Diem2 { get; set; }

        [Column("Diem3")]
        public decimal? Diem3 { get; set; }

        [Column("Diem4")]
        public decimal? Diem4 { get; set; }

        [Column("Diem5")]
        public decimal? Diem5 { get; set; }

        [Column("Diem6")]
        public decimal? Diem6 { get; set; }
    }
}
