using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTuyenSinhNganh")]
    public class SvTuyenSinhNganh
    {
        public SvTuyenSinhNganh()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_nganh")]
        public int IdNganh { get; set; }

        [Column("ID_khoi_nganh")]
        public int IdKhoiNganh { get; set; }

        [Column("ID_nhom_nganh")]
        public int IdNhomNganh { get; set; }

        [Column("ID_nganh_chuan")]
        public int IdNganhChuan { get; set; }

        [Column("Ma_nganh"), MaxLength(20)]
        public string MaNganh { get; set; }

        [Column("Ten_nganh_dao_tao"), Max<PERSON>ength(100)]
        public string TenNganhDaoTao { get; set; }

        [Column("ID_loai_nganh")]
        public int IdLoaiNganh { get; set; }

        [<PERSON>umn("Ghi_chu"), Max<PERSON>ength(200)]
        public string Ghi<PERSON>hu { get; set; }

        [Column("Hien_thi")]
        public bool HienThi { get; set; }
    }
}
