using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svNhomChungChi")]
    public class SvNhomChungChi
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_nhom_chung_chi")]
        public int IdNhomChungChi { get; set; }

        [Column("Ky_hieu_nhom"), MaxLength(20)]
        public string KyHieuNhom { get; set; }

        [Column("Nhom_chung_chi"), MaxLength(200)]
        public string <PERSON>homChung<PERSON><PERSON> { get; set; }
    }
}
