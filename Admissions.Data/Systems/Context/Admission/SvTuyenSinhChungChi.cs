using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Admissions.Data
{
    [Table("svTuyenSinhChungChi")]
    public class SvTuyenSinhChungChi
    {
        public SvTuyenSinhChungChi() 
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_tuyen_sinh_chung_chi")]
        public int IdTuyenSinhChungChi { get; set; }

        [Column("Ten_chung_chi"), MaxLength(200)]
        public string TenChungChi { get; set; }

        [Column("Loai_chung_chi")]
        public int? LoaiChungChi { get; set; }
    }
}
