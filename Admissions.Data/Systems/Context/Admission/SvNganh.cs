using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svNganh")]
    public class SvNganh
    {
        public SvNganh()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_nganh")]
        public int IdNganh { get; set; }

        [Column("Ma_nganh"), MaxLength(20)]
        public string MaNganh { get; set; }

        [Column("Ten_nganh"), MaxLength(200)]
        public string TenNganh { get; set; }

        [Column("Ten_nganh_en"), MaxLength(200)]
        public string TenNganhEn { get; set; }

        [Column("Su_pham")]
        public bool SuPham { get; set; }
    }
}
