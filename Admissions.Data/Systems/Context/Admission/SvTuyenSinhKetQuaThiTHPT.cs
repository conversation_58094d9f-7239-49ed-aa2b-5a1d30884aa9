using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTuyenSinhKetQuaThiTHPT")]
    public class SvTuyenSinhKetQuaThiTHPT
    {
        public SvTuyenSinhKetQuaThiTHPT()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_tuyen_sinh_ket_qua_thi_thpt")]
        public int IdTuyenSinhKetQuaThiThpt { get; set; }

        [Column("ID_ho_so"), MaxLength(500)]
        public int IdHoSo { get; set; }

        [Column("ID_mon_xet_tuyen")]
        public int IdMonXetTuyen { get; set; }

        [Column("Diem")]
        public decimal Diem { get; set; }

        [Column("CreateDate")]
        public DateTime? CreateDate { get; set; }

        [Column("CreateUserName")]
        public string CreateUserName { get; set; }

        [Column("ModifyDate")]
        public DateTime? ModifyDate { get; set; }

        [Column("ModifyUserName")]
        public string ModifyUserName { get; set; }
    }
}
