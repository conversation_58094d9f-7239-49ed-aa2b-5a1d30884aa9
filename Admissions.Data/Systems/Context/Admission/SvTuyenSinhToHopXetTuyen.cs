using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTuyenSinhToHopXetTuyen")]
    public class SvTuyenSinhToHopXetTuyen
    {
        public SvTuyenSinhToHopXetTuyen()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_to_hop_xet_tuyen")]
        public int IdToHopXetTuyen { get; set; }

        [Column("Ma_to_hop"), MaxLength(20)]
        public string MaToHop { get; set; }

        [Column("Ten_to_hop_xet_tuyen"), Max<PERSON>ength(100)]
        public string TenToHopXetTuyen { get; set; }

        [<PERSON>umn("Ghi_chu"), MaxLength(200)]
        public string GhiChu { get; set; }

    }
}
