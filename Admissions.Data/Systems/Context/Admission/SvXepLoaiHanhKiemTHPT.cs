using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svXepLoaiHanhKiemTHPT")]
    public class SvXepLoaiHanhKiemTHPT
    {
        public SvXepLoaiHanhKiemTHPT() 
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_loai_hanh_kiem_lop12")]
        public int IdXepLoaiHanhKiemLop12 { get; set; }

        [Column("Ma_xep_loai_hanh_kiem_lop12"), MaxLength(20)]
        public string MaXepLoaiHanhKiemLop12 { get; set; }

        [Column("Ten_xep_loai_hanh_kiem_lop12"), Max<PERSON>ength(50)]
        public string TenXepLoaiHanhKiemLop12 { get; set; }

        [Column("Ghi_chu"), Max<PERSON>ength(200)]
        public string <PERSON><PERSON><PERSON><PERSON> { get; set; }

    }
}
