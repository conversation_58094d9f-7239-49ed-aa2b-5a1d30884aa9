using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Admissions.Data
{
    [Table("svTuyenSinhThanhToan")]
    public class SvTuyenSinhThanhToan
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_tuyen_sinh_thanh_toan")]
        public int IdTuyenSinhThanhToan { get; set; }

        [Column("ID_ho_so")]
        public int IdHoSo { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("Nam_tuyen_sinh")]
        public int NamTuyenSinh { get; set; }

        [Column("Ngay_thang_giao_dich")]
        public DateTime? NgayThangGiaoDich { get; set; }

        [Column("Ma_giao_dich"), <PERSON><PERSON>ength(100)]
        public string MaGiaoDich { get; set; }

        [Column("Noi_dung"), MaxLength(200)]
        public string Noi<PERSON>ung { get; set; }

        [<PERSON>um<PERSON>("Ngan_hang"), <PERSON><PERSON><PERSON>th(50)]
        public string Ng<PERSON><PERSON>ang { get; set; }

        [Column("So_tien")]
        public decimal? SoTien { get; set; }

        [Column("Hinh_thuc_thanh_toan")]
        public int? HinhThucThanhToan { get; set; }

        [Column("Trang_thai")]
        public int? TrangThai { get; set; }

        [Column("Ngay_tao")]
        public DateTime? NgayTao { get; set; }

        [Column("Nguoi_tao"), MaxLength(50)]
        public string NguoiTao { get; set; }
        
        [Column("Nguoi_sua"), MaxLength(50)]
        public string NguoiSua { get; set; }
    }
}
