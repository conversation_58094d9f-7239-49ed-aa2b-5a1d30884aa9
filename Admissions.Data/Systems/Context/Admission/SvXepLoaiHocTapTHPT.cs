using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svXepLoaiHocTapTHPT")]
    public class SvXepLoaiHocTapTHPT
    {
        public SvXepLoaiHocTapTHPT() 
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_loai_hoc_tap_lop12")]
        public int IdXepLoaiHocTapLop12 { get; set; }

        [Column("Ma_xep_loai_hoc_tap_lop12"), MaxLength(20)]
        public string MaXepLoaiHocTapLop12 { get; set; }

        [Column("Ten_xep_loai_hoc_tap_lop12"), MaxLength(50)]
        public string TenXepLoaiHocTapLop12 { get; set; }

        [Column("Ghi_chu"), Max<PERSON>ength(200)]
        public string <PERSON><PERSON><PERSON><PERSON> { get; set; }

    }
}
