using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTuyenSinhToHopNganhXetTuyen")]
    public class SvTuyenSinhToHopNganhXetTuyen
    {
        public SvTuyenSinhToHopNganhXetTuyen()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_to_hop_xet_tuyen_nganh")]
        public int IdTuyenSinhToHopNganhXetTuyen { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("ID_nganh")]
        public int IdNganh { get; set; }

        [Column("ID_phuong_thuc_xet_tuyen")]
        public int IdPhuongThucXetTuyen { get; set; }

        [Column("ID_to_hop_xet_tuyen")]
        public int IdToHopXetTuyen { get; set; }

    }
}
