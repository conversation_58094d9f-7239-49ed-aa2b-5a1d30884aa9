using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svHuyen")]
    public class SvHuyen
    {
        [Key]
        [Column("ID_huyen"), <PERSON><PERSON><PERSON>th(5)]
        public string IdHuy<PERSON> { get; set; }

        [<PERSON>umn("ID_tinh"), <PERSON><PERSON><PERSON>th(5)]
        public string IdTinh { get; set; }

        [<PERSON>um<PERSON>("Ten_huyen"), <PERSON><PERSON>ength(50)]
        public string TenHuyen { get; set; }

        [<PERSON>umn("Ten_huyen_en"), MaxLength(50)]
        public string TenHuyenEn { get; set; }

        [<PERSON>umn("ID_huyen_cu"), <PERSON><PERSON>ength(5)]
        public string Id<PERSON><PERSON><PERSON><PERSON>u { get; set; }

        [<PERSON>umn("ID_huyen_cu1"), Max<PERSON>ength(5)]
        public string IdHuyenCu1 { get; set; }

        [<PERSON>umn("Ten_huyen_cu"), <PERSON><PERSON><PERSON><PERSON>(50)]
        public string <PERSON><PERSON>uy<PERSON>Cu { get; set; }
    }
}
