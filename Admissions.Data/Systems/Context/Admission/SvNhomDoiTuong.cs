using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svNhomDoiTuong")]
    public class SvNhomDoiTuong
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_nhom_doi_tuong")]
        public int IdNhomDoiTuong { get; set; }

        [Column("Ma_nhom"), MaxLength(5)]
        public string MaNhom { get; set; }

        [Column("Ten_nhom"), MaxLength(100)]
        public string TenNhom { get; set; }

        [Column("Ten_nhom_en"), MaxLength(50)]
        public string TenNhomEn { get; set; }
    }
}
