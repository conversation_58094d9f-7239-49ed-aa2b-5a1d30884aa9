using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTinh")]
    public class SvTinh
    {
        public SvTinh() 
        {

        }

        [Key]
        [Column("ID_tinh"), <PERSON><PERSON>ength(5)]
        public string IdTinh { get; set; }

        [Column("Ten_tinh"), Max<PERSON>ength(50)]
        public string TenTinh { get; set; }

        [<PERSON>umn("Ten_tinh_en"), MaxLength(50)]
        public string TenTinhEn { get; set; }

        [Column("Ma_tinh"), MaxLength(3)]
        public string MaTinh { get; set; }
    }
}
