using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTuyenSinhMauPhieu")]
    public class SvTuyenSinhMauPhieu
    {
        public SvTuyenSinhMauPhieu() 
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_mau_phieu_tuyen_sinh")]
        public int IdMauPhieuTuyenSinh { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("ID_phuong_thuc_xet_tuyen")]
        public int IdPhuongThucXetTuyen { get; set; }

        [Column("Url_mau_phieu_tuyen_sinh")]
        public string UrlMauPhieuTuyenSinh { get; set; }

        [Column("Ket_qua_hoc_ba")]
        public bool? KetQuaHocBa { get; set; }

        [Column("Ket_qua_thi_thpt")]
        public bool? KetQuaThiThpt { get; set; }

        [Column("Ket_qua_nang_khieu")]
        public bool? KetQuaNangKhieu { get; set; }

        [Column("Ket_qua_nang_luc")]
        public bool? KetQuaNangLuc { get; set; }

    }
}
