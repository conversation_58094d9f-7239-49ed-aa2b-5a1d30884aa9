using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTuyenSinhMonXetTuyen")]
    public class SvTuyenSinhMonXetTuyen
    {
        public SvTuyenSinhMonXetTuyen()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_mon_xet_tuyen")]
        public int IdMonXetTuyen { get; set; }

        [Column("Ma_mon_xet_tuyen"), MaxLength(20)]
        public string MaMonXetTuyen { get; set; }

        [Column("Ten_mon_xet_tuyen"), MaxLength(50)]
        public string TenMonXetTuyen { get; set; }

        [Column("Ghi_chu"), MaxLength(200)]
        public string Ghi<PERSON>hu { get; set; }
     
        [Column("He_so_mon")]
        public decimal? HeSoMon { get; set; }

        [Column("Mon_nang_khieu")]
        public bool MonNangKhieu { get; set; }
    }
}
