using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Admissions.Data
{
    [Table("svGioiTinh")]
    public class SvGioiTinh
    {
        public SvGioiTinh()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_gioi_tinh")]
        public int IdGioiTinh { get; set; }

        [Column("Gioi_tinh"), MaxLength(3)]
        public string GioiTinh { get; set; }
    }
}
