using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Admissions.Data
{
    [Table("svTuyenSinhToHopMonXetTuyen")]
    public class SvTuyenSinhToHopMonXetTuyen
    {
        public SvTuyenSinhToHopMonXetTuyen()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_tuyen_sinh_to_hop_mon_xet_tuyen")]
        public int IdTuyenSinhToHopMonXetTuyen { get; set; }

        [Column("ID_to_hop_xet_tuyen")]
        public int IdToHopXetTuyen { get; set; }

        [Column("ID_mon_xet_tuyen")]
        public int IdMonXetTuyen { get; set; }

        [Column("He_so")]
        public decimal? HeSo { get; set; }

    }
}
