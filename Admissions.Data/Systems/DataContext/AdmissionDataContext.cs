using Microsoft.EntityFrameworkCore;

namespace Admissions.Data
{
    public partial class AdmissionDataContext : DbContext
    {
        public AdmissionDataContext()
        {
        }

        public AdmissionDataContext(DbContextOptions<AdmissionDataContext> options)
            : base(options)
        {
        }

        public virtual DbSet<SvTuyenSinhPhuongThucXetTuyen> SvTuyenSinhPhuongThucXetTuyens { get; set; }
        public virtual DbSet<SvDoiTuongXetTuyen> SvDoiTuongXetTuyens { get; set; }
        public virtual DbSet<SvKhuVuc> SvKhuVucs { get; set; }
        public virtual DbSet<SvTuyenSinhNganh> SvTuyenSinhNganhs { get; set; }
        public virtual DbSet<SvTuyenSinhLoaiMon> SvTuyenSinhLoaiMons { get; set; }
        public virtual DbSet<SvTuyenSinhMonXetTuyen> SvTuyenSinhMonXetTuyens { get; set; }
        public virtual DbSet<SvLoaiGiayTo> SvLoaiGiayTos { get; set; }
        public virtual DbSet<SvTuyenSinhToHopXetTuyen> SvTuyenSinhToHopXetTuyens { get; set; }
        public virtual DbSet<SvTruongTHPT> SvTruongTHPTs { get; set; }
        public virtual DbSet<SvXa> SvXas { get; set; }
        public virtual DbSet<SvTinh> SvTinhs { get; set; }
        public virtual DbSet<SvHuyen> SvHuyens { get; set; }
        public virtual DbSet<SvTuyenSinhKhoiNganh> SvTuyenSinhKhoiNganhs { get; set; }
        public virtual DbSet<SvTuyenSinhLoaiNganh> SvTuyenSinhLoaiNganhs { get; set; }
        public virtual DbSet<SvTuyenSinhNganhChuan> SvTuyenSinhNganhChuans { get; set; }
        public virtual DbSet<SvTuyenSinhDotTuyenSinh> SvTuyenSinhDotTuyenSinhs { get; set; }
        public virtual DbSet<SvTuyenSinhToHopMonXetTuyen> SvTuyenSinhToHopMonXetTuyens { get; set; }
        public virtual DbSet<SvTuyenSinhNhomNganh> SvTuyenSinhNhomNganhs { get; set; }
        public virtual DbSet<SvHe> SvHes { get; set; }
        public virtual DbSet<SvTuyenSinhDangKyDot> SvTuyenSinhDangKyDots { get; set; }
        public virtual DbSet<SvDoiTuongTuyenSinh> SvDoiTuongTuyenSinhs { get; set; }
        public virtual DbSet<SvNhomChungChi> SvNhomChungChis { get; set; }
        public virtual DbSet<SvNhomDoiTuong> SvNhomDoiTuongs { get; set; }
        public virtual DbSet<SvTuyenSinhPhuongAnDiemHocBa> SvTuyenSinhPhuongAnDiemHocBas { get; set; }
        public virtual DbSet<SvTuyenSinhMauPhieu> SvTuyenSinhMauPhieus { get; set; }
        public virtual DbSet<SvXepLoaiHocTapTHPT> SvXepLoaiHocTapTHPTs { get; set; }
        public virtual DbSet<SvXepLoaiHanhKiemTHPT> SvXepLoaiHanhKiemTHPTs { get; set; }
        public virtual DbSet<svHoSoTuyenSinh> SvHoSoTuyenSinhs { get; set; }
        public virtual DbSet<SvHoSoNopTuyenSinh> SvHoSoNopTuyenSinhs { get; set; }
        public virtual DbSet<SvTuyenSinhDangKyXetTuyen> SvTuyenSinhDangKyXetTuyens { get; set; }
        public virtual DbSet<SvTuyenSinhKetQuaHocTapHocBa> SvTuyenSinhKetQuaHocTapHocBas { get; set; }
        public virtual DbSet<SvTuyenSinhThiSinhDangKyXetTuyen> SvTuyenSinhThiSinhDangKyXetTuyens { get; set; }
        public virtual DbSet<SvNganh> SvNganhs { get; set; }
        public virtual DbSet<SvGioiTinh> SvGioiTinhs { get; set; }
        public virtual DbSet<SvDanToc> SvDanTocs { get; set; }
        public virtual DbSet<SvQuocTich> SvQuocTichs { get; set; }
        public virtual DbSet<SvTuyenSinhToHopNganhXetTuyen> SvTuyenSinhToHopNganhXetTuyens { get; set; }
        public virtual DbSet<SvChiTieuTuyenSinh> SvChiTieuTuyenSinhs { get; set; }
        public virtual DbSet<SvChiTieuTuyenSinhPhuongThuc> SvChiTieuTuyenSinhPhuongThucs { get; set; }
        public virtual DbSet<SvTuyenSinhDotDangKy> SvTuyenSinhDotDangKys { get; set; }
        public virtual DbSet<SvHoSoNopTuyenSinhUrl> SvHoSoNopTuyenSinhUrls { get; set; }
        public virtual DbSet<SvTuyenSinhDoiTuongXetTuyen> SvTuyenSinhDoiTuongXetTuyens { get; set; }
        public virtual DbSet<SvTuyenSinhKetQuaThiTHPT> SvTuyenSinhKetQuaThiTHPTs { get; set; }
        public virtual DbSet<SvTuyenSinhKetQuaThiNangKhieu> SvTuyenSinhKetQuaThiNangKhieus { get; set; }
        public virtual DbSet<SvTuyenSinhKetQuaDanhGiaNangLuc> SvTuyenSinhKetQuaDanhGiaNangLucs { get; set; }
        public virtual DbSet<SvTuyenSinhKetQuaThacSi> SvTuyenSinhKetQuaThacSis { get; set; }
        public virtual DbSet<SvTuyenSinhLoaiDoiTuong> SvTuyenSinhLoaiDoiTuongs { get; set; }
        public virtual DbSet<SvTuyenSinhHoatDongNgoaiKhoa> SvTuyenSinhHoatDongNgoaiKhoas { get; set; }
        public virtual DbSet<SvTuyenSinhPhuongAnMon> SvTuyenSinhPhuongAnMons { get; set; }
        public virtual DbSet<SvTuyenSinhKhoanThu> SvTuyenSinhKhoanThus { get; set; }
        public virtual DbSet<SvTuyenSinhChungChi> SvTuyenSinhChungChis { get; set; }
        public virtual DbSet<SvTuyenSinhChungChiThiSinh> SvTuyenSinhChungChiThiSinhs { get; set; }
        public virtual DbSet<SvTuyenSinhChungChiMinhChung> SvTuyenSinhChungChiMinhChungs { get; set; }
        public virtual DbSet<SvTuyenSinhThanhToan> SvTuyenSinhThanhToans { get; set; }
        public virtual DbSet<SvTuyenSinhThanhToanChiTiet> SvTuyenSinhThanhToanChiTiets { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            OnModelCreatingPartial(modelBuilder);
            modelBuilder.Entity<SvHoSoNopTuyenSinh>()
                .HasKey(e => new { e.IdHoSo, e.IdGiayTo });
            modelBuilder.Entity<SvTuyenSinhKetQuaHocTapHocBa>()
                .HasKey(e => new { e.IdHoSo, e.IdMonXetTuyen });
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
